<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>write-to-bigtable</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/06/03 22:58:31.237</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/06/03 22:58:31.237</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>Neo4j Cypher</from>
      <to>Beam Bigtable Output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Beam Bigtable Output</name>
    <type>BeamBigtableOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <columns>
      <column>
        <family>f1</family>
        <qualifier>firstName</qualifier>
        <source_field>First name</source_field>
      </column>
      <column>
        <family>f2</family>
        <qualifier>lastName</qualifier>
        <source_field>Last name</source_field>
      </column>
    </columns>
    <instance_id>hop-bigtable</instance_id>
    <key_field>id</key_field>
    <project_id>apachehop</project_id>
    <table_id>customers</table_id>
    <attributes/>
    <GUI>
      <xloc>576</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Neo4j Cypher</name>
    <type>Neo4jCypherOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection/>
    <cypher/>
    <batch_size/>
    <read_only>N</read_only>
    <nr_retries_on_error/>
    <retry>Y</retry>
    <cypher_from_field>N</cypher_from_field>
    <cypher_field/>
    <unwind>N</unwind>
    <unwind_map/>
    <returning_graph>N</returning_graph>
    <return_graph_field/>
    <mappings/>
    <returns/>
    <attributes/>
    <GUI>
      <xloc>144</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
