<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>dynamic-sql-row</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/02/14 20:20:51.940</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/02/14 20:20:51.940</modified_date>
  </info>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Noto Sans</fontname>
      <fontsize>10</fontsize>
      <height>44</height>
      <xloc>384</xloc>
      <yloc>128</yloc>
      <note>get a list of ID columns from the all the tables in the PUBLIC schema. 
build a query to list of of the available IDs per table</note>
      <width>393</width>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>get ID columns</from>
      <to>build query</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>build query</from>
      <to>Dynamic SQL row</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Dynamic SQL row</from>
      <to>keep TABLE_NAME. ID_COL</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>keep TABLE_NAME. ID_COL</from>
      <to>log table_name, id</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Dynamic SQL row</name>
    <type>DynamicSqlRow</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>hop-samples</connection>
    <outer_join>N</outer_join>
    <query_only_on_change>N</query_only_on_change>
    <replace_vars>N</replace_vars>
    <rowlimit>0</rowlimit>
    <sql>select cast(ID as INT) as id_col, 'DIM_SAMPLE' as table_name from DIM_SAMPLE;</sql>
    <sql_fieldname>query</sql_fieldname>
    <attributes/>
    <GUI>
      <xloc>272</xloc>
      <yloc>240</yloc>
    </GUI>
  </transform>
  <transform>
    <name>log table_name, id</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage/>
    <fields>
      <field>
        <name>TABLE_NAME</name>
      </field>
      <field>
        <name>ID_COL</name>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>672</xloc>
      <yloc>240</yloc>
    </GUI>
  </transform>
  <transform>
    <name>build query</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>//Script here

var query = "select cast(" + COLUMN_NAME + " as INT) as id_col, '" + TABLE_NAME + "' as table_name from " + TABLE_NAME + ";" </jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>query</name>
        <rename>query</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>272</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>get ID columns</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>hop-samples</connection>
    <execute_each_row>N</execute_each_row>
    <limit>0</limit>
    <sql>SELECT *
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA  = 'PUBLIC'
  AND COLUMN_NAME = 'ID'
</sql>
    <variables_active>N</variables_active>
    <attributes/>
    <GUI>
      <xloc>112</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>keep TABLE_NAME. ID_COL</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>TABLE_NAME</name>
      </field>
      <field>
        <name>ID_COL</name>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <GUI>
      <xloc>480</xloc>
      <yloc>240</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
