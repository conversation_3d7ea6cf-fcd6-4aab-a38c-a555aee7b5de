<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<workflow>
  <name>workflow-executor-child</name>
  <name_sync_with_filename>Y</name_sync_with_filename>
  <description/>
  <extended_description/>
  <workflow_version/>
  <created_user>-</created_user>
  <created_date>2021/04/27 12:41:26.346</created_date>
  <modified_user>-</modified_user>
  <modified_date>2021/04/27 12:41:26.346</modified_date>
  <parameters>
    </parameters>
  <actions>
    <action>
      <name>Start</name>
      <description/>
      <type>SPECIAL</type>
      <attributes/>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <intervalSeconds>0</intervalSeconds>
      <intervalMinutes>60</intervalMinutes>
      <hour>12</hour>
      <minutes>0</minutes>
      <weekDay>1</weekDay>
      <DayOfMonth>1</DayOfMonth>
      <parallel>N</parallel>
      <xloc>160</xloc>
      <yloc>160</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>Wait for</name>
      <description/>
      <type>DELAY</type>
      <attributes/>
      <maximumTimeout>1</maximumTimeout>
      <scaletime>0</scaletime>
      <parallel>N</parallel>
      <xloc>320</xloc>
      <yloc>160</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>Add filenames to result</name>
      <description/>
      <type>ADD_RESULT_FILENAMES</type>
      <attributes/>
      <arg_from_previous>N</arg_from_previous>
      <fields>
        <field>
          <name>${HOP_UNIT_TESTS_FOLDER}</name>
          <filemask>.*</filemask>
        </field>
      </fields>
      <delete_all_before>N</delete_all_before>
      <include_subfolders>Y</include_subfolders>
      <parallel>N</parallel>
      <xloc>480</xloc>
      <yloc>160</yloc>
      <attributes_hac/>
    </action>
  </actions>
  <hops>
    <hop>
      <from>Start</from>
      <to>Wait for</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>Y</unconditional>
    </hop>
    <hop>
      <from>Wait for</from>
      <to>Add filenames to result</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
  </hops>
  <notepads>
  </notepads>
  <attributes/>
</workflow>
