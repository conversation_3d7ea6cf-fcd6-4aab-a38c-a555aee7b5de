<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<workflow>
  <name>variable_substitution</name>
  <name_sync_with_filename>Y</name_sync_with_filename>
  <description/>
  <extended_description/>
  <workflow_version/>
  <workflow_status>0</workflow_status>
  <created_user>-</created_user>
  <created_date>2024/05/25 13:59:25.213</created_date>
  <modified_user>-</modified_user>
  <modified_date>2024/05/25 13:59:25.213</modified_date>
  <parameters>
    <parameter>
      <name>parameter_variable</name>
      <default_value>param_value</default_value>
      <description/>
    </parameter>
  </parameters>
  <actions>
    <action>
      <name>Start</name>
      <description/>
      <type>SPECIAL</type>
      <attributes/>
      <DayOfMonth>1</DayOfMonth>
      <hour>12</hour>
      <intervalMinutes>60</intervalMinutes>
      <intervalSeconds>0</intervalSeconds>
      <minutes>0</minutes>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <weekDay>1</weekDay>
      <parallel>N</parallel>
      <xloc>48</xloc>
      <yloc>144</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>Set variables "Variable substitution enabled"</name>
      <description/>
      <type>SET_VARIABLES</type>
      <attributes/>
      <fields>
        <field>
          <variable_name>${parameter_variable}</variable_name>
          <variable_type>CURRENT_WORKFLOW</variable_type>
          <variable_value>${variable1}</variable_value>
        </field>
      </fields>
      <file_variable_type>CURRENT_WORKFLOW</file_variable_type>
      <filename>${PROJECT_HOME}/files/properties_file.properties</filename>
      <replacevars>Y</replacevars>
      <parallel>N</parallel>
      <xloc>336</xloc>
      <yloc>144</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>Write to log</name>
      <description/>
      <type>WRITE_TO_LOG</type>
      <attributes/>
      <loglevel>Basic</loglevel>
      <logmessage>## Output ##

# Variable values from properties file!
# First value
variable1 = ${variable1}
# Second value
variable2 = ${variable2}
# Third value
variable3 = ${variable3}

## 
parameter_variable = ${parameter_variable}

# New created variable
param_value = ${param_value}</logmessage>
      <logsubject/>
      <parallel>N</parallel>
      <xloc>640</xloc>
      <yloc>144</yloc>
      <attributes_hac/>
    </action>
  </actions>
  <hops>
    <hop>
      <from>Start</from>
      <to>Set variables "Variable substitution enabled"</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>Y</unconditional>
    </hop>
    <hop>
      <from>Set variables "Variable substitution enabled"</from>
      <to>Write to log</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
  </hops>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Segoe UI</fontname>
      <fontsize>9</fontsize>
      <height>74</height>
      <xloc>48</xloc>
      <yloc>48</yloc>
      <note>The Workflow takes a parameter named parameter_value with a default value set to "param_value". 
Using the "Variable substitution" option in the "Set Variables" step enables you to dynamically create a new variable. 
This new variable is named based on the value of the content of the parameter_value variable, 
and its value can be set using other variables.</note>
      <width>621</width>
    </notepad>
  </notepads>
  <attributes/>
</workflow>
