<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>neo4j-output-parallel-load</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <pipeline_status>0</pipeline_status>
    <parameters>
      <parameter>
        <name>COPIES</name>
        <default_value>4</default_value>
        <description>Parallelism</description>
      </parameter>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2018/07/07 08:56:27.330</created_date>
    <modified_user>-</modified_user>
    <modified_date>2018/07/07 08:56:27.330</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>files/customers-100.txt</from>
      <to>Create :Customer nodes</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Create :Customer nodes</name>
    <type>Neo4JOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>${COPIES}</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>demo</connection>
    <batch_size>30</batch_size>
    <key/>
    <create_indexes>N</create_indexes>
    <use_create>N</use_create>
    <only_create_relationships>N</only_create_relationships>
    <returning_graph>N</returning_graph>
    <return_graph_field>graph</return_graph_field>
    <from>
      <read_only_from_node>N</read_only_from_node>
      <labels>
        <label/>
        <value>Customer</value>
      </labels>
      <properties>
        <property>
          <name>id</name>
          <value>id</value>
          <type>Integer</type>
          <primary>Y</primary>
        </property>
        <property>
          <name>lastName</name>
          <value>Last name</value>
          <type>String</type>
          <primary>N</primary>
        </property>
        <property>
          <name>firstName</name>
          <value>First name</value>
          <type>String</type>
          <primary>N</primary>
        </property>
        <property>
          <name>custZipCode</name>
          <value>cust_zip_code</value>
          <type>String</type>
          <primary>N</primary>
        </property>
        <property>
          <name>city</name>
          <value>city</value>
          <type>String</type>
          <primary>N</primary>
        </property>
        <property>
          <name>birthdate</name>
          <value>birthdate</value>
          <type>String</type>
          <primary>N</primary>
        </property>
        <property>
          <name>street</name>
          <value>street</value>
          <type>String</type>
          <primary>N</primary>
        </property>
        <property>
          <name>housenr</name>
          <value>housenr</value>
          <type>String</type>
          <primary>N</primary>
        </property>
        <property>
          <name>stateCode</name>
          <value>stateCode</value>
          <type>String</type>
          <primary>N</primary>
        </property>
        <property>
          <name>state</name>
          <value>state</value>
          <type>String</type>
          <primary>N</primary>
        </property>
      </properties>
    </from>
    <to>
      <read_only_to_node>N</read_only_to_node>
      <labels/>
      <properties/>
    </to>
    <relationship/>
    <relationship_value/>
    <relprops/>
    <attributes/>
    <GUI>
      <xloc>400</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>files/customers-100.txt</name>
    <type>CSVInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <filename>${PROJECT_HOME}/files/customers-100.txt</filename>
    <filename_field/>
    <rownum_field/>
    <include_filename>N</include_filename>
    <separator>;</separator>
    <enclosure>"</enclosure>
    <header>Y</header>
    <buffer_size>50000</buffer_size>
    <lazy_conversion>N</lazy_conversion>
    <add_filename_result>N</add_filename_result>
    <parallel>Y</parallel>
    <newline_possible>N</newline_possible>
    <encoding/>
    <fields>
      <field>
        <name>id</name>
        <type>Integer</type>
        <format> #</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>15</length>
        <precision>0</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>Last name</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>15</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>First name</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>20</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>cust_zip_code</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>15</length>
        <precision>0</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>city</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>8</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>birthdate</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>street</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>11</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>housenr</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>15</length>
        <precision>0</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>stateCode</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>9</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>state</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>30</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>128</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes>
    <group>
      <name>debug_plugin</name>
    </group>
  </attributes>
</pipeline>
