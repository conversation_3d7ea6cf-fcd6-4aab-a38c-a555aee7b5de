<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<workflow>
  <name>repeat_action</name>
  <name_sync_with_filename>Y</name_sync_with_filename>
  <description/>
  <extended_description/>
  <workflow_version/>
  <workflow_status>0</workflow_status>
  <created_user>-</created_user>
  <created_date>2022/04/11 09:13:28.977</created_date>
  <modified_user>-</modified_user>
  <modified_date>2022/04/11 09:13:28.977</modified_date>
  <parameters>
    <parameter>
      <name>COUNTER</name>
      <default_value>0</default_value>
      <description/>
    </parameter>
  </parameters>
  <actions>
    <action>
      <name>Start</name>
      <description/>
      <type>SPECIAL</type>
      <attributes/>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <intervalSeconds>0</intervalSeconds>
      <intervalMinutes>60</intervalMinutes>
      <hour>12</hour>
      <minutes>0</minutes>
      <weekDay>1</weekDay>
      <DayOfMonth>1</DayOfMonth>
      <parallel>N</parallel>
      <xloc>48</xloc>
      <yloc>160</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>Repeat</name>
      <description/>
      <type>Repeat</type>
      <attributes/>
      <filename>${PROJECT_HOME}/actions/repeat_child.hpl</filename>
      <run_configuration>local</run_configuration>
      <variable_name>END_LOOP</variable_name>
      <variable_value/>
      <delay/>
      <keep_values>N</keep_values>
      <logfile_enabled>N</logfile_enabled>
      <logfile_appended>Y</logfile_appended>
      <logfile_base/>
      <logfile_extension>log</logfile_extension>
      <logfile_add_date>Y</logfile_add_date>
      <logfile_add_time>N</logfile_add_time>
      <logfile_add_repetition>N</logfile_add_repetition>
      <logfile_update_interval>5000</logfile_update_interval>
      <parameters/>
      <parallel>N</parallel>
      <xloc>176</xloc>
      <yloc>160</yloc>
      <attributes_hac/>
    </action>
  </actions>
  <hops>
    <hop>
      <from>Start</from>
      <to>Repeat</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>Y</unconditional>
    </hop>
  </hops>
  <notepads>
    <notepad>
      <note>This action will repeat a pipeline until a condition is met.
It will increment the counter parameter until value 5 has been reached.
Then it will set the END_LOOP variable causing the repeat action to stop</note>
      <xloc>64</xloc>
      <yloc>48</yloc>
      <width>447</width>
      <heigth>58</heigth>
      <fontname>.AppleSystemUIFont</fontname>
      <fontsize>13</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <attributes/>
</workflow>
