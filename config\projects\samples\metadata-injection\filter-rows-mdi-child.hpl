<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>0022-filter-rows-child</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2022/11/29 11:41:29.990</created_date>
    <modified_user>-</modified_user>
    <modified_date>2022/11/29 11:41:29.990</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>files/customers-100.txt</from>
      <to>FL and housenr>100</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>FL and housenr>100</from>
      <to>True</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>FL and housenr>100</from>
      <to>False</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>FL and housenr>100</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compare>
      <condition>
        <conditions>
</conditions>
        <function>=</function>
        <leftvalue>stateCode</leftvalue>
        <negated>N</negated>
        <operator>-</operator>
        <value>
          <isnull>N</isnull>
          <length>-1</length>
          <name>constant</name>
          <precision>-1</precision>
          <text>FL</text>
          <type>String</type>
        </value>
      </condition>
    </compare>
    <attributes/>
    <GUI>
      <xloc>480</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>False</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>752</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform>
    <name>True</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>752</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>files/customers-100.txt</name>
    <type>CSVInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <filename>${PROJECT_HOME}/files/customers-100.txt</filename>
    <filename_field/>
    <rownum_field/>
    <include_filename>N</include_filename>
    <separator>;</separator>
    <enclosure>"</enclosure>
    <header>Y</header>
    <buffer_size>50000</buffer_size>
    <lazy_conversion>N</lazy_conversion>
    <add_filename_result>N</add_filename_result>
    <parallel>N</parallel>
    <newline_possible>N</newline_possible>
    <encoding/>
    <fields>
      <field>
        <name>id</name>
        <type>Integer</type>
        <format> #</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>15</length>
        <precision>0</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>name</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>10</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>firstname</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>13</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>zip</name>
        <type>Integer</type>
        <format> #</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>15</length>
        <precision>0</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>city</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>8</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>birthdate</name>
        <type>Date</type>
        <format>yyyy/MM/dd</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>street</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>11</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>housenr</name>
        <type>Integer</type>
        <format> #</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>15</length>
        <precision>0</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>stateCode</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>9</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>state</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <length>30</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>224</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
