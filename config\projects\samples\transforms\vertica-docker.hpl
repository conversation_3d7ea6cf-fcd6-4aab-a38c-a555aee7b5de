<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>vertica-docker</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/10/07 08:35:44.364</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/10/07 08:35:44.364</modified_date>
  </info>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Noto Sans</fontname>
      <fontsize>10</fontsize>
      <height>486</height>
      <xloc>688</xloc>
      <yloc>48</yloc>
      <note>Run this sample in a Vertica Docker container:  

1) pull the docker image: "docker pull vertica/vertica-ce"

2) start the Vertica container: 
docker run -p 5433:5433 -p 5444:5444 \
           --mount type=volume,source=vertica-data,target=/data \
           --name vertica_ce \
           vertica/vertica-ce

3) create a Vertica connection: 
- connection name: vertica 
- host: localhost
- username: dbadmin 
- port: 5433
- password: leave blank 
- database name: vmart 

4) create the "books" table
CREATE TABLE books (
  author VARCHAR(150)
, genre VARCHAR(150)
, publisher VARCHAR(150)
, title VARCHAR(150)
)

5) run the pipeline 
;</note>
      <width>357</width>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>generate 10M rows</from>
      <to>generate book data</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>generate book data</from>
      <to>string length 150</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>string length 150</from>
      <to>Vertica bulk loader</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Vertica bulk loader</name>
    <type>VerticaBulkLoader</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <abort_on_error>Y</abort_on_error>
    <connection>vertica</connection>
    <direct>Y</direct>
    <exceptions_filename/>
    <fields>
      <field>
        <column_name>author</column_name>
        <stream_name>author</stream_name>
      </field>
      <field>
        <column_name>genre</column_name>
        <stream_name>genre</stream_name>
      </field>
      <field>
        <column_name>publisher</column_name>
        <stream_name>publisher</stream_name>
      </field>
      <field>
        <column_name>title</column_name>
        <stream_name>title</stream_name>
      </field>
    </fields>
    <only_when_have_rows>N</only_when_have_rows>
    <rejected_data_filename/>
    <schema/>
    <specify_fields>Y</specify_fields>
    <stream_name/>
    <table>books</table>
    <truncate>N</truncate>
    <attributes/>
    <GUI>
      <xloc>559</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>generate 10M rows</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
</fields>
    <interval_in_ms>5000</interval_in_ms>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <limit>10000000</limit>
    <never_ending>N</never_ending>
    <row_time_field>now</row_time_field>
    <attributes/>
    <GUI>
      <xloc>64</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>generate book data</name>
    <type>Fake</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>4</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>publisher</name>
        <topic>publisher</topic>
        <type>Book</type>
      </field>
      <field>
        <name>genre</name>
        <topic>genre</topic>
        <type>Book</type>
      </field>
      <field>
        <name>author</name>
        <topic>author</topic>
        <type>Book</type>
      </field>
      <field>
        <name>title</name>
        <topic>title</topic>
        <type>Book</type>
      </field>
    </fields>
    <locale>en</locale>
    <attributes/>
    <GUI>
      <xloc>229</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>string length 150</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>publisher</name>
      </field>
      <field>
        <name>genre</name>
      </field>
      <field>
        <name>author</name>
      </field>
      <field>
        <name>title</name>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <GUI>
      <xloc>394</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
