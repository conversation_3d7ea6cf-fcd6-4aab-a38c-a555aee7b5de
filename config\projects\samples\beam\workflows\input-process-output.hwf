<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<workflow>
  <name>input-process-output</name>
  <name_sync_with_filename>Y</name_sync_with_filename>
  <description/>
  <extended_description/>
  <workflow_version/>
  <created_user>-</created_user>
  <created_date>2020/05/12 16:45:17.069</created_date>
  <modified_user>-</modified_user>
  <modified_date>2020/05/12 16:45:17.069</modified_date>
  <parameters>
    </parameters>
  <pass_batchid>N</pass_batchid>
  <actions>
    <action>
      <name>START</name>
      <description/>
      <type>SPECIAL</type>
      <attributes/>
      <start>Y</start>
      <dummy>N</dummy>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <intervalSeconds>0</intervalSeconds>
      <intervalMinutes>60</intervalMinutes>
      <hour>12</hour>
      <minutes>0</minutes>
      <weekDay>1</weekDay>
      <DayOfMonth>1</DayOfMonth>
      <parallel>N</parallel>
      <nr>0</nr>
      <xloc>208</xloc>
      <yloc>128</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>input-process-output</name>
      <description/>
      <type>PIPELINE</type>
      <attributes/>
      <filename>${PROJECT_HOME}/beam/pipelines/input-process-output.hpl</filename>
      <params_from_previous>N</params_from_previous>
      <exec_per_row>N</exec_per_row>
      <clear_rows>N</clear_rows>
      <clear_files>N</clear_files>
      <set_logfile>N</set_logfile>
      <logfile/>
      <logext/>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <loglevel>Basic</loglevel>
      <set_append_logfile>N</set_append_logfile>
      <wait_until_finished>Y</wait_until_finished>
      <follow_abort_remote>N</follow_abort_remote>
      <create_parent_folder>N</create_parent_folder>
      <run_configuration>Direct</run_configuration>
      <parameters>
        <pass_all_parameters>Y</pass_all_parameters>
      </parameters>
      <parallel>N</parallel>
      <nr>0</nr>
      <xloc>368</xloc>
      <yloc>128</yloc>
      <attributes_hac/>
    </action>
  </actions>
  <hops>
    <hop>
      <from>START</from>
      <to>input-process-output</to>
      <from_nr>0</from_nr>
      <to_nr>0</to_nr>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>Y</unconditional>
    </hop>
  </hops>
  <notepads>
  </notepads>
  <attributes/>
</workflow>
