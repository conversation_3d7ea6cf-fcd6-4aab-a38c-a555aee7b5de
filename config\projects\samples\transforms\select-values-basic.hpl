<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>select-values-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/07/14 19:52:50.709</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/07/14 19:52:50.709</modified_date>
    <key_for_session_key/>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Modifies the fields and metadata in the pipeline stream in a variety of ways: 
*) Rename reorder fields (Select &amp; Alter tab): rename fields, change order of the desc and date fields
*) Remove date (Remove tab): remove the date field
*) Cast data types (Metadata tab): cast the String to a date 
and 
*) Output Cast Errors: handle errors thrown by 'Cast data types'. Click on the 'Cast data types' transform and choose 'Error Handling' to configure</note>
      <xloc>128</xloc>
      <yloc>64</yloc>
      <width>808</width>
      <heigth>112</heigth>
      <fontname>Noto Sans</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Test Data</from>
      <to>Rename, reorder fields</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Test Data</from>
      <to>Remove date </to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Test Data</from>
      <to>Cast data types</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Rename, reorder fields</from>
      <to>Output Rename, reorder</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Remove date </from>
      <to>Output Remove</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Cast data types</from>
      <to>Output Cast</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Cast data types</from>
      <to>Output Cast Errors </to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Test Data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>id</name>
        <precision>-1</precision>
        <type>Integer</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>desc</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>date</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
    </fields>
    <data>
      <line>
        <item>1</item>
        <item>one</item>
        <item>2021-01-01</item>
      </line>
      <line>
        <item>2</item>
        <item>two</item>
        <item>2022-01-01</item>
      </line>
      <line>
        <item>3</item>
        <item>three</item>
        <item>2023-01-01</item>
      </line>
      <line>
        <item>4</item>
        <item>four</item>
        <item>2024-01-01</item>
      </line>
      <line>
        <item>5</item>
        <item>five</item>
        <item>2025-13-01</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>160</xloc>
      <yloc>368</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Rename, reorder fields</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>id</name>
        <rename>id_renamed</rename>
      </field>
      <field>
        <name>date</name>
        <rename>date_renamed</rename>
      </field>
      <field>
        <name>desc</name>
        <rename>desc_renamed</rename>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <GUI>
      <xloc>368</xloc>
      <yloc>240</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Remove date </name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <select_unspecified>N</select_unspecified>
      <remove>
        <name>date</name>
      </remove>
    </fields>
    <attributes/>
    <GUI>
      <xloc>368</xloc>
      <yloc>368</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Cast data types</name>
    <type>SelectValues</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <select_unspecified>N</select_unspecified>
      <meta>
        <name>date</name>
        <rename>date</rename>
        <type>Date</type>
        <length>-2</length>
        <precision>-2</precision>
        <conversion_mask>yyyy-MM-dd</conversion_mask>
        <date_format_lenient>false</date_format_lenient>
        <date_format_locale/>
        <date_format_timezone/>
        <lenient_string_to_number>false</lenient_string_to_number>
        <encoding/>
        <decimal_symbol/>
        <grouping_symbol/>
        <currency_symbol/>
        <storage_type/>
      </meta>
    </fields>
    <attributes/>
    <GUI>
      <xloc>368</xloc>
      <yloc>496</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output Rename, reorder</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>560</xloc>
      <yloc>240</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output Remove</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>560</xloc>
      <yloc>368</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output Cast</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>560</xloc>
      <yloc>496</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output Cast Errors </name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>368</xloc>
      <yloc>656</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
    <error>
      <source_transform>Cast data types</source_transform>
      <target_transform>Output Cast Errors </target_transform>
      <is_enabled>Y</is_enabled>
      <nr_valuename>nb_errors</nr_valuename>
      <descriptions_valuename>error_description</descriptions_valuename>
      <fields_valuename>error_fields</fields_valuename>
      <codes_valuename>error_codes</codes_valuename>
      <max_errors>1</max_errors>
      <max_pct_errors>20</max_pct_errors>
      <min_pct_rows>0</min_pct_rows>
    </error>
  </transform_error_handling>
  <attributes/>
</pipeline>
