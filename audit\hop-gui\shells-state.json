{"nameStateMap": {"Open file": {"name": "Open file", "stateMap": {"max": false, "x": 472, "width": 842, "y": 309, "height": 421}}, "Error": {"name": "Error", "stateMap": {"max": false, "x": 430, "width": 740, "y": 300, "height": 302}}, "Pipeline": {"name": "Pipeline", "stateMap": {"max": false, "x": 531, "width": 538, "y": 66, "height": 720}}, "Error Details": {"name": "<PERSON><PERSON><PERSON>", "stateMap": {"max": false, "x": 327, "width": 945, "y": 215, "height": 421}}, "Run Options": {"name": "Run Options", "stateMap": {"max": false, "x": 590, "width": 420, "y": 224, "height": 404}}, "Generate rows": {"name": "Generate rows", "stateMap": {"max": false, "x": 375, "width": 850, "y": 249, "height": 354}}, "Text file output": {"name": "Text file output", "stateMap": {"max": false, "x": 312, "width": 975, "y": 175, "height": 501}}, "Menu actions...": {"name": "Menu actions...", "stateMap": {"max": false, "x": 200, "width": 1200, "y": 107, "height": 638}}, "生成记录": {"name": "生成记录", "stateMap": {"max": false, "x": 463, "width": 674, "y": 249, "height": 354}}, "打开文件": {"name": "打开文件", "stateMap": {"max": false, "x": 489, "width": 891, "y": 182, "height": 445}}, "桥接": {"name": "桥接", "stateMap": {"max": false, "x": 600, "width": 400, "y": 366, "height": 120}}, "csv 文件输入": {"name": "csv 文件输入", "stateMap": {"max": false, "x": 564, "width": 472, "y": 260, "height": 332}}, "CSV 文件输入": {"name": "CSV 文件输入", "stateMap": {"max": false, "x": 447, "width": 761, "y": 164, "height": 650}}, "自定义常量数据": {"name": "自定义常量数据", "stateMap": {"max": false, "x": 462, "width": 675, "y": 297, "height": 258}}, "文件元数据": {"name": "文件元数据", "stateMap": {"max": false, "x": 533, "width": 533, "y": 132, "height": 588}}, "执行 Pipeline": {"name": "执行 Pipeline", "stateMap": {"max": false, "x": 359, "width": 882, "y": 228, "height": 396}}, "Save File?": {"name": "Save File?", "stateMap": {"max": false, "x": 575, "width": 449, "y": 373, "height": 105}}}}