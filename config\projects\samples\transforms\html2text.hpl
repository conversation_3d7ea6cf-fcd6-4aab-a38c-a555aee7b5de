<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>html2text</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <pipeline_status>0</pipeline_status>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/12/18 15:00:31.032</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/12/18 15:00:31.032</modified_date>
  </info>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Ubuntu</fontname>
      <fontsize>11</fontsize>
      <height>234</height>
      <xloc>672</xloc>
      <yloc>192</yloc>
      <note>This plugin uses JSoup to clean and extract text from HTML documents.

Cleaning creates a new, clean document, from the original dirty document, containing only elements allowed by the safelist.

Safe-lists define what HTML (elements and attributes) to allow through the cleaner. Everything else is removed.
Available safelists are:
- None
- Relaxed
- Basic
- Simple text
- Basic with Images

For more details on the safelists, see https://jsoup.org/apidocs/org/jsoup/safety/Safelist.html
</note>
      <width>753</width>
    </notepad>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Ubuntu</fontname>
      <fontsize>11</fontsize>
      <height>74</height>
      <xloc>640</xloc>
      <yloc>624</yloc>
      <note>In this example, there is little difference between cleaning the HTML beforehand or not, aside from a return character in one of the outputs.
However, the variation in inputs for text extraction can affect the extracted output.
Additionally, cleaning the HTML beforehand allows the inclusion of extra steps, such as applying regex rules, to improve the pre-processing of
HTML documents before text extraction.</note>
      <width>865</width>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Html Documents</from>
      <to>Basic Html Cleaning</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Basic Html Cleaning</from>
      <to>Extract Text</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Html Documents</from>
      <to>Extract Text (no pre-cleaning)</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Extract Text</from>
      <to>Text output (with pre-cleaning)</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Extract Text (no pre-cleaning)</from>
      <to>Text output (no pre-cleaning)</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Basic Html Cleaning</name>
    <type>Html2Text</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <htmlField>html</htmlField>
    <outputField>clean_html</outputField>
    <safelistType>basic</safelistType>
    <cleanOnly>Y</cleanOnly>
    <parallelism>N</parallelism>
    <attributes/>
    <GUI>
      <xloc>864</xloc>
      <yloc>448</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Extract Text</name>
    <type>Html2Text</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <htmlField>clean_html</htmlField>
    <outputField>text</outputField>
    <safelistType>basic</safelistType>
    <cleanOnly>N</cleanOnly>
    <parallelism>N</parallelism>
    <attributes/>
    <GUI>
      <xloc>1024</xloc>
      <yloc>448</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Extract Text (no pre-cleaning)</name>
    <type>Html2Text</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <htmlField>html</htmlField>
    <outputField>text</outputField>
    <safelistType>basic</safelistType>
    <cleanOnly>N</cleanOnly>
    <parallelism>N</parallelism>
    <attributes/>
    <GUI>
      <xloc>1024</xloc>
      <yloc>544</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Html Documents</name>
    <type>DataGrid</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>&lt;!DOCTYPE html>
&lt;html>
&lt;head>
&lt;title> My Webpage &lt;title>
  &lt;meta charset="utf8">
  &lt;meta name="description" content=" This is a sample page ">
  &lt;link rel="stylesheet" href="styles.css"
&lt;/head>
&lt;body>
&lt;div class="header">
&lt;h1>Welcome to my webpage&lt;h1>
&lt;p>This page is under construction&lt;p>
&lt;/div
&lt;div class="content">
    &lt;img src="image.jpg" alt="A sample image"
    &lt;p>Here is some text about the image&lt;/p
    &lt;a href="http://example.com">Click here&lt;a>
&lt;/div
&lt;div class="footer">
&lt;p>Footer text&lt;p>
&lt;a href="contact.html">Contact&lt;a>
&lt;/div
&lt;/body>
&lt;html>
</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>html</name>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>624</xloc>
      <yloc>544</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Text output (no pre-cleaning)</name>
    <type>TextFileOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <schema_definition/>
    <separator>;</separator>
    <enclosure>"</enclosure>
    <enclosure_forced>N</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>Y</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding>UTF-8</encoding>
    <endedLine/>
    <fileNameInField>N</fileNameInField>
    <fileNameField/>
    <create_parent_folder>Y</create_parent_folder>
    <file>
      <name>html2text_no-pre-cleaning</name>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>Y</do_not_open_new_file_init>
      <extention>csv</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format/>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>N</fast_dump>
      <splitevery/>
    </file>
    <fields>
    </fields>
    <attributes/>
    <GUI>
      <xloc>1264</xloc>
      <yloc>544</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Text output (with pre-cleaning)</name>
    <type>TextFileOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <schema_definition/>
    <separator>;</separator>
    <enclosure>"</enclosure>
    <enclosure_forced>N</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>Y</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding>UTF-8</encoding>
    <endedLine/>
    <fileNameInField>N</fileNameInField>
    <fileNameField/>
    <create_parent_folder>Y</create_parent_folder>
    <file>
      <name>html2text_pre-cleaning</name>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>Y</do_not_open_new_file_init>
      <extention>csv</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format/>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>N</fast_dump>
      <splitevery/>
    </file>
    <fields>
    </fields>
    <attributes/>
    <GUI>
      <xloc>1264</xloc>
      <yloc>448</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
