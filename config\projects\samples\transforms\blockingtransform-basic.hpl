<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>blockingtransform-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/07/21 09:47:39.961</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/07/21 09:47:39.961</modified_date>
    <key_for_session_key/>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Generate 100k random UUIDs, block until done</note>
      <xloc>160</xloc>
      <yloc>144</yloc>
      <width>272</width>
      <heigth>27</heigth>
      <fontname>Noto Sans</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Generate 100k rows</from>
      <to>Generate random UUID</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Generate random UUID</from>
      <to>Blocking transform</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Blocking transform</from>
      <to>Output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Generate 100k rows</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
</fields>
    <interval_in_ms>5000</interval_in_ms>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <never_ending>N</never_ending>
    <limit>100000</limit>
    <row_time_field>now</row_time_field>
    <attributes/>
    <GUI>
      <xloc>176</xloc>
      <yloc>208</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Generate random UUID</name>
    <type>RandomValue</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>uuid</name>
        <type>random uuid</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>352</xloc>
      <yloc>208</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Blocking transform</name>
    <type>BlockingTransform</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <cache_size>5000</cache_size>
    <compress>Y</compress>
    <directory>${java.io.tmpdir}</directory>
    <pass_all_rows>Y</pass_all_rows>
    <prefix>block</prefix>
    <attributes/>
    <GUI>
      <xloc>528</xloc>
      <yloc>208</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>704</xloc>
      <yloc>208</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
