# Hop GUI 数据映射测试使用说明

## 🔧 问题已修复

之前遇到的路径错误已经解决！现在所有文件都已正确放置在默认项目中。

## 📁 文件位置

所有测试文件现在位于：
```
config/projects/default/
├── datasets/
│   ├── source_table.csv        # 源数据（8条员工记录）
│   └── department_table.csv    # 部门数据（4条记录）
├── pipelines/
│   ├── data_mapping_pipeline.hpl    # 原始管道
│   └── simple_mapping_test.hpl      # 简化版管道（推荐使用）
└── workflows/
    └── data_processing_workflow.hwf # 数据处理工作流
```

## 🚀 在 Hop GUI 中的操作步骤

### 第一步：确认项目
1. 在 Hop GUI 中，确保当前项目是 "default"
2. 如果不是，点击项目下拉菜单选择 "default"

### 第二步：打开简化版管道（推荐）
1. 在左侧项目浏览器中，展开 "pipelines" 文件夹
2. 双击 `simple_mapping_test.hpl` 打开管道
3. 您将看到三个步骤：
   - **CSV输入** → **字段映射** → **CSV输出**

### 第三步：查看管道设计
管道功能：
- **CSV输入**：读取 `datasets/source_table.csv`
- **字段映射**：将字段重命名为中文
  - `id` → `员工ID`
  - `name` → `姓名`
  - `age` → `年龄`
  - `city` → `工作城市`
  - `salary` → `月薪`
- **CSV输出**：输出到 `datasets/mapped_result.csv`

### 第四步：运行管道
1. 点击工具栏中的绿色运行按钮（▶️）
2. 在弹出的对话框中：
   - 运行配置：选择 "local"
   - 点击 "Launch" 开始执行
3. 查看执行日志，确认成功完成

### 第五步：验证结果
1. 检查 `config/projects/default/datasets/` 目录
2. 应该生成了 `mapped_result.csv` 文件
3. 打开文件查看字段是否正确映射

## 📊 测试数据说明

### 源数据 (source_table.csv)
```csv
id,name,age,city,salary
1,张三,25,北京,8000
2,李四,30,上海,12000
3,王五,28,广州,9500
4,赵六,35,深圳,15000
5,钱七,22,杭州,7000
6,孙八,29,南京,10000
7,周九,31,成都,11000
8,吴十,26,武汉,8500
```

### 预期输出 (mapped_result.csv)
```csv
员工ID,姓名,年龄,工作城市,月薪
1,张三,25,北京,8000
2,李四,30,上海,12000
...
```

## 🔍 故障排除

如果仍然遇到路径问题：

1. **检查项目设置**：确保在 "default" 项目中
2. **重新启动 Hop GUI**：关闭并重新打开 hop-gui
3. **检查文件路径**：确认文件在正确位置
4. **查看日志**：在执行窗口查看详细错误信息

## 🎯 下一步扩展

成功运行基础管道后，您可以尝试：

1. **修改字段映射**：添加或修改字段重命名规则
2. **添加数据转换**：插入计算字段、过滤器等
3. **连接多表**：使用 department_table.csv 进行表连接
4. **创建工作流**：使用 data_processing_workflow.hwf

现在您可以在 Hop GUI 中成功运行数据映射测试了！
