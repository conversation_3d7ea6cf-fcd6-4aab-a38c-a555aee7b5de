<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>cleanup-remove-all-constraints</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/08/03 18:23:20.199</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/08/03 18:23:20.199</modified_date>
    <key_for_session_key/>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>Build DROP CONSTRAINT cypher</from>
      <to>perform DROP CONSTRAINT</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Get constraints list on database</from>
      <to>remove "</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>remove "</from>
      <to>Build DROP CONSTRAINT cypher</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Build DROP CONSTRAINT cypher</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>
var cypher = "DROP "+description+";";
</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>cypher</name>
        <rename>cypher</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>384</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Get constraints list on database</name>
    <type>Neo4jCypherOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>demo</connection>
    <cypher>call db.constraints()</cypher>
    <batch_size>1</batch_size>
    <read_only>N</read_only>
    <nr_retries_on_error/>
    <retry>Y</retry>
    <cypher_from_field>N</cypher_from_field>
    <cypher_field/>
    <unwind>N</unwind>
    <unwind_map/>
    <returning_graph>N</returning_graph>
    <return_graph_field/>
    <mappings/>
    <returns>
      <return>
        <name>description</name>
        <type>String</type>
        <source_type/>
      </return>
    </returns>
    <attributes/>
    <GUI>
      <xloc>128</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>perform DROP CONSTRAINT</name>
    <type>Neo4jCypherOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>demo</connection>
    <cypher/>
    <batch_size>1</batch_size>
    <read_only>N</read_only>
    <nr_retries_on_error/>
    <retry>Y</retry>
    <cypher_from_field>Y</cypher_from_field>
    <cypher_field>cypher</cypher_field>
    <unwind>N</unwind>
    <unwind_map/>
    <returning_graph>N</returning_graph>
    <return_graph_field/>
    <mappings/>
    <returns/>
    <attributes/>
    <GUI>
      <xloc>384</xloc>
      <yloc>368</yloc>
    </GUI>
  </transform>
  <transform>
    <name>remove "</name>
    <type>ReplaceString</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <in_stream_name>description</in_stream_name>
        <out_stream_name/>
        <use_regex>yes</use_regex>
        <replace_string>\"</replace_string>
        <replace_by_string/>
        <set_empty_string>N</set_empty_string>
        <replace_field_by_string/>
        <whole_word>no</whole_word>
        <case_sensitive>no</case_sensitive>
        <is_unicode>no</is_unicode>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>384</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
