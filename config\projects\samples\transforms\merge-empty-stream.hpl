<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>merge_empty_stream</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/11/11 16:31:19.403</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/11/11 16:31:19.403</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>A</from>
      <to>new_field_A</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch / case</from>
      <to>A</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch / case</from>
      <to>Default</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>new_field_A</from>
      <to>Stream Schema Merge</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>new_field_B</from>
      <to>Stream Schema Merge</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch / case</from>
      <to>B</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>B</from>
      <to>new_field_B</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Default</from>
      <to>Stream Schema Merge</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Data grid</from>
      <to>Switch / case</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>A</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>352</xloc>
      <yloc>192</yloc>
    </GUI>
  </transform>
  <transform>
    <name>B</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>352</xloc>
      <yloc>304</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Data grid</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>first</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>second</name>
        <precision>-1</precision>
        <type>Integer</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>third</name>
        <precision>-1</precision>
        <type>Number</type>
      </field>
    </fields>
    <data>
      <line>
        <item>A</item>
        <item>1</item>
        <item>111</item>
      </line>
      <line>
        <item>C</item>
        <item>2</item>
        <item>123</item>
      </line>
      <line>
        <item>C</item>
        <item>3</item>
        <item>321</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>96</xloc>
      <yloc>192</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Default</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>352</xloc>
      <yloc>416</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Stream Schema Merge</name>
    <type>StreamSchema</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <transforms>
      <transform>
        <name>new_field_A</name>
      </transform>
      <transform>
        <name>new_field_B</name>
      </transform>
      <transform>
        <name>Default</name>
      </transform>
    </transforms>
    <attributes/>
    <GUI>
      <xloc>656</xloc>
      <yloc>416</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Switch / case</name>
    <type>SwitchCase</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <cases>
      <case>
        <target_transform>A</target_transform>
        <value>A</value>
      </case>
      <case>
        <target_transform>B</target_transform>
        <value>B</value>
      </case>
    </cases>
    <case_value_type>String</case_value_type>
    <default_target_transform>Default</default_target_transform>
    <fieldname>first</fieldname>
    <use_contains>N</use_contains>
    <attributes/>
    <GUI>
      <xloc>208</xloc>
      <yloc>192</yloc>
    </GUI>
  </transform>
  <transform>
    <name>new_field_A</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>new_field_A</name>
        <precision>-1</precision>
        <type>Number</type>
        <nullif>666</nullif>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>464</xloc>
      <yloc>192</yloc>
    </GUI>
  </transform>
  <transform>
    <name>new_field_B</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>new_field_B</name>
        <precision>-1</precision>
        <type>String</type>
        <nullif>BBB</nullif>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>464</xloc>
      <yloc>304</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
