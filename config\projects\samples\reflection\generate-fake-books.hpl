<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>pipeline-probe-generate-fake-books</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/01/21 10:27:52.237</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/01/21 10:27:52.237</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>generate 10k rows</from>
      <to>fake books</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>fake books</from>
      <to>set length</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>set length</from>
      <to>dummy</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>fake books</name>
    <type>Fake</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>genre</name>
        <topic>genre</topic>
        <type>Book</type>
      </field>
      <field>
        <name>author</name>
        <topic>author</topic>
        <type>Book</type>
      </field>
      <field>
        <name>title</name>
        <topic>title</topic>
        <type>Book</type>
      </field>
    </fields>
    <locale>en</locale>
    <attributes/>
    <GUI>
      <xloc>272</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>generate 10k rows</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
</fields>
    <interval_in_ms>5000</interval_in_ms>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <limit>10000</limit>
    <never_ending>N</never_ending>
    <row_time_field>now</row_time_field>
    <attributes/>
    <GUI>
      <xloc>80</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>set length</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>genre</name>
      </field>
      <field>
        <name>author</name>
      </field>
      <field>
        <name>title</name>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <GUI>
      <xloc>448</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>dummy</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>624</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
