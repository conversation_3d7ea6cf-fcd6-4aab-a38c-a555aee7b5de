<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<workflow>
  <name>loops-process-rows-from-memory</name>
  <name_sync_with_filename>Y</name_sync_with_filename>
  <description/>
  <extended_description/>
  <workflow_version/>
  <created_user>-</created_user>
  <created_date>2023/03/05 10:47:19.660</created_date>
  <modified_user>-</modified_user>
  <modified_date>2023/03/05 10:47:19.660</modified_date>
  <parameters>
    </parameters>
  <actions>
    <action>
      <name>Start</name>
      <description/>
      <type>SPECIAL</type>
      <attributes/>
      <DayOfMonth>1</DayOfMonth>
      <hour>12</hour>
      <intervalMinutes>60</intervalMinutes>
      <intervalSeconds>0</intervalSeconds>
      <minutes>0</minutes>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <weekDay>1</weekDay>
      <parallel>N</parallel>
      <xloc>50</xloc>
      <yloc>50</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>loops-copy-rows-to-result.hpl</name>
      <description/>
      <type>PIPELINE</type>
      <attributes/>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <clear_files>N</clear_files>
      <clear_rows>N</clear_rows>
      <create_parent_folder>N</create_parent_folder>
      <exec_per_row>N</exec_per_row>
      <filename>${PROJECT_HOME}/loops/child-loops-copy-rows-to-result.hpl</filename>
      <logext/>
      <logfile/>
      <loglevel>Basic</loglevel>
      <parameters>
        <pass_all_parameters>Y</pass_all_parameters>
      </parameters>
      <params_from_previous>N</params_from_previous>
      <run_configuration>local</run_configuration>
      <set_append_logfile>N</set_append_logfile>
      <set_logfile>N</set_logfile>
      <wait_until_finished>Y</wait_until_finished>
      <parallel>N</parallel>
      <xloc>272</xloc>
      <yloc>48</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>loops-log-counter.hpl</name>
      <description/>
      <type>PIPELINE</type>
      <attributes/>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <clear_files>N</clear_files>
      <clear_rows>N</clear_rows>
      <create_parent_folder>N</create_parent_folder>
      <exec_per_row>Y</exec_per_row>
      <filename>${PROJECT_HOME}/loops/child-loops-log-counter.hpl</filename>
      <logext/>
      <logfile/>
      <loglevel>Basic</loglevel>
      <parameters>
        <parameter>
          <name>PRM_COUNTER</name>
          <stream_name>counter</stream_name>
          <value/>
        </parameter>
        <pass_all_parameters>Y</pass_all_parameters>
      </parameters>
      <params_from_previous>N</params_from_previous>
      <run_configuration>local</run_configuration>
      <set_append_logfile>N</set_append_logfile>
      <set_logfile>N</set_logfile>
      <wait_until_finished>Y</wait_until_finished>
      <parallel>N</parallel>
      <xloc>560</xloc>
      <yloc>48</yloc>
      <attributes_hac/>
    </action>
  </actions>
  <hops>
    <hop>
      <from>Start</from>
      <to>loops-copy-rows-to-result.hpl</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>Y</unconditional>
    </hop>
    <hop>
      <from>loops-copy-rows-to-result.hpl</from>
      <to>loops-log-counter.hpl</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
  </hops>
  <notepads>
  </notepads>
  <attributes/>
</workflow>
