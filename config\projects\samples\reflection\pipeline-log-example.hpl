<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>pipeline-log-example</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/02/16 20:54:20.525</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/02/16 20:54:20.525</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>Pipeline Logging</from>
      <to>pipeline logs</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>pipeline logs</from>
      <to>unique pipeline data</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Pipeline Logging</from>
      <to>transform logs</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>transform logs</from>
      <to>pipeline-log-transforms.csv out</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>transform logs</from>
      <to>get some stats</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>unique pipeline data</from>
      <to>transform LU</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>get some stats</from>
      <to>transform LU</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>transform LU</from>
      <to>r/s in</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>r/s in</from>
      <to>pipeline-log.csv out</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Pipeline Logging</name>
    <type>PipelineLogging</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <log_transforms>Y</log_transforms>
    <attributes/>
    <GUI>
      <xloc>160</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>get some stats</name>
    <type>MemoryGroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <aggregate>total_errors</aggregate>
        <subject>transformErrors</subject>
        <type>SUM</type>
      </field>
      <field>
        <aggregate>min_input</aggregate>
        <subject>transformLinesInput</subject>
        <type>MIN</type>
      </field>
      <field>
        <aggregate>max_input</aggregate>
        <subject>transformLinesInput</subject>
        <type>MAX</type>
      </field>
      <field>
        <aggregate>min_output</aggregate>
        <subject>transformLinesOutput</subject>
        <type>MIN</type>
      </field>
      <field>
        <aggregate>max_output</aggregate>
        <subject>transformLinesOutput</subject>
        <type>MAX</type>
      </field>
      <field>
        <aggregate>min_read</aggregate>
        <subject>transformLinesRead</subject>
        <type>MIN</type>
      </field>
      <field>
        <aggregate>max_read</aggregate>
        <subject>transformLinesRead</subject>
        <type>MAX</type>
      </field>
      <field>
        <aggregate>min_written</aggregate>
        <subject>transformLinesWritten</subject>
        <type>MIN</type>
      </field>
      <field>
        <aggregate>max_written</aggregate>
        <subject>transformLinesWritten</subject>
        <type>MAX</type>
      </field>
      <field>
        <aggregate>max_duration</aggregate>
        <subject>transformDuration</subject>
        <type>MAX</type>
      </field>
    </fields>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>pipelineFilename</name>
      </field>
    </group>
    <attributes/>
    <GUI>
      <xloc>688</xloc>
      <yloc>272</yloc>
    </GUI>
  </transform>
  <transform>
    <name>pipeline logs</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>loggingDate</name>
      </field>
      <field>
        <name>loggingPhase</name>
      </field>
      <field>
        <name>pipelineName</name>
      </field>
      <field>
        <name>pipelineFilename</name>
      </field>
      <field>
        <name>pipelineStart</name>
      </field>
      <field>
        <name>pipelineEnd</name>
      </field>
      <field>
        <name>pipelineLogChannelId</name>
      </field>
      <field>
        <name>parentLogChannelId</name>
      </field>
      <field>
        <name>pipelineLogging</name>
      </field>
      <field>
        <name>pipelineErrorCount</name>
      </field>
      <field>
        <name>pipelineStatusDescription</name>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <GUI>
      <xloc>320</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>pipeline-log-transforms.csv out</name>
    <type>TextFileOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <separator>;</separator>
    <enclosure>"</enclosure>
    <enclosure_forced>N</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>Y</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding/>
    <endedLine/>
    <fileNameInField>N</fileNameInField>
    <fileNameField/>
    <create_parent_folder>Y</create_parent_folder>
    <file>
      <name>${PROJECT_HOME}/reflection/output/pipeline-log-transforms</name>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>Y</do_not_open_new_file_init>
      <extention>csv</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format/>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>N</fast_dump>
      <splitevery/>
    </file>
    <fields>
      <field>
        <name>loggingDate</name>
        <type>Date</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>pipelineName</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>pipelineFilename</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>transformName</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>transformCopyNr</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>transformStatusDescription</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>pipelineLogChannelId</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>transformLogChannelId</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>transformLoggingText</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>transformLinesRead</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>transformLinesWritten</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>transformLinesInput</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>transformLinesOutput</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>transformLinesUpdated</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>transformLinesRejected</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>transformErrors</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>transformStart</name>
        <type>Date</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>transformEnd</name>
        <type>Date</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>transformDuration</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>320</xloc>
      <yloc>432</yloc>
    </GUI>
  </transform>
  <transform>
    <name>pipeline-log.csv out</name>
    <type>TextFileOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <separator>;</separator>
    <enclosure>"</enclosure>
    <enclosure_forced>N</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>Y</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding/>
    <endedLine/>
    <fileNameInField>N</fileNameInField>
    <fileNameField/>
    <create_parent_folder>Y</create_parent_folder>
    <file>
      <name>${PROJECT_HOME}/reflection/output/pipeline-log</name>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>Y</do_not_open_new_file_init>
      <extention>csv</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format/>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>N</fast_dump>
      <splitevery/>
    </file>
    <fields>
      <field>
        <name>loggingDate</name>
        <type>Date</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>loggingPhase</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>pipelineName</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>pipelineFilename</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>pipelineStart</name>
        <type>Date</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>pipelineEnd</name>
        <type>Date</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>pipelineLogChannelId</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>parentLogChannelId</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>pipelineLogging</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>pipelineErrorCount</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>pipelineStatusDescription</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>total_errors</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>min_input</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>max_input</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>min_output</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>max_output</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>min_read</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>max_read</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>min_written</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>max_written</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>max_duration</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>max_rs_read</name>
        <type>Integer</type>
        <format>0</format>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>max_rs_written</name>
        <type>Integer</type>
        <format>0</format>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>1088</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>r/s in</name>
    <type>Calculator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <calculation>
      <calc_type>DIVIDE</calc_type>
      <field_a>max_read</field_a>
      <field_b>max_duration</field_b>
      <field_name>max_rs_read</field_name>
      <remove>N</remove>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <value_type>Integer</value_type>
    </calculation>
    <calculation>
      <calc_type>DIVIDE</calc_type>
      <field_a>max_written</field_a>
      <field_b>max_duration</field_b>
      <field_name>max_rs_written</field_name>
      <remove>N</remove>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <value_type>Integer</value_type>
    </calculation>
    <failIfNoFile>Y</failIfNoFile>
    <attributes/>
    <GUI>
      <xloc>848</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>transform LU</name>
    <type>StreamLookup</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <from>get some stats</from>
    <input_sorted>N</input_sorted>
    <preserve_memory>Y</preserve_memory>
    <sorted_list>N</sorted_list>
    <integer_pair>N</integer_pair>
    <lookup>
      <key>
        <name>pipelineFilename</name>
        <field>pipelineFilename</field>
      </key>
      <value>
        <name>total_errors</name>
        <rename>total_errors</rename>
        <default/>
        <type>Integer</type>
      </value>
      <value>
        <name>min_input</name>
        <rename>min_input</rename>
        <default/>
        <type>Integer</type>
      </value>
      <value>
        <name>max_input</name>
        <rename>max_input</rename>
        <default/>
        <type>Integer</type>
      </value>
      <value>
        <name>min_output</name>
        <rename>min_output</rename>
        <default/>
        <type>Integer</type>
      </value>
      <value>
        <name>max_output</name>
        <rename>max_output</rename>
        <default/>
        <type>Integer</type>
      </value>
      <value>
        <name>min_read</name>
        <rename>min_read</rename>
        <default/>
        <type>Integer</type>
      </value>
      <value>
        <name>max_read</name>
        <rename>max_read</rename>
        <default/>
        <type>Integer</type>
      </value>
      <value>
        <name>min_written</name>
        <rename>min_written</rename>
        <default/>
        <type>Integer</type>
      </value>
      <value>
        <name>max_written</name>
        <rename>max_written</rename>
        <default/>
        <type>Integer</type>
      </value>
      <value>
        <name>max_duration</name>
        <rename>max_duration</rename>
        <default/>
        <type>Integer</type>
      </value>
    </lookup>
    <attributes/>
    <GUI>
      <xloc>688</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>transform logs</name>
    <type>SelectValues</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>loggingDate</name>
      </field>
      <field>
        <name>pipelineName</name>
      </field>
      <field>
        <name>pipelineFilename</name>
      </field>
      <field>
        <name>transformName</name>
      </field>
      <field>
        <name>transformCopyNr</name>
      </field>
      <field>
        <name>transformStatusDescription</name>
      </field>
      <field>
        <name>pipelineLogChannelId</name>
      </field>
      <field>
        <name>transformLogChannelId</name>
      </field>
      <field>
        <name>transformLoggingText</name>
      </field>
      <field>
        <name>transformLinesRead</name>
      </field>
      <field>
        <name>transformLinesWritten</name>
      </field>
      <field>
        <name>transformLinesInput</name>
      </field>
      <field>
        <name>transformLinesOutput</name>
      </field>
      <field>
        <name>transformLinesUpdated</name>
      </field>
      <field>
        <name>transformLinesRejected</name>
      </field>
      <field>
        <name>transformErrors</name>
      </field>
      <field>
        <name>transformStart</name>
      </field>
      <field>
        <name>transformEnd</name>
      </field>
      <field>
        <name>transformDuration</name>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <GUI>
      <xloc>320</xloc>
      <yloc>272</yloc>
    </GUI>
  </transform>
  <transform>
    <name>unique pipeline data</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>${java.io.tmpdir}</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>Y</unique_rows>
    <fields>
      <field>
        <name>loggingDate</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>loggingPhase</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>pipelineName</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>pipelineFilename</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>pipelineStart</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>pipelineEnd</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>pipelineLogChannelId</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>parentLogChannelId</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>pipelineLogging</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>pipelineErrorCount</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>pipelineStatusDescription</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>496</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
