{"name": "filter-rows-mdi-child", "filename": "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\metadata-injection\\filter-rows-mdi-child.hpl", "id": "5b95efcc-4f64-4c5a-8a10-788e5c9de608", "parentId": null, "executionType": "Pipeline", "executorXml": "<pipeline>\n  <info>\n    <name>filter-rows-mdi-child</name>\n    <name_sync_with_filename>Y</name_sync_with_filename>\n    <description/>\n    <extended_description/>\n    <pipeline_version/>\n    <pipeline_type>Normal</pipeline_type>\n    <parameters>\n    </parameters>\n    <capture_transform_performance>N</capture_transform_performance>\n    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>\n    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>\n    <created_user>-</created_user>\n    <created_date>2022/11/29 11:41:29.990</created_date>\n    <modified_user>-</modified_user>\n    <modified_date>2022/11/29 11:41:29.990</modified_date>\n  </info>\n  <notepads>\n  </notepads>\n  <order>\n    <hop>\n      <from>files/customers-100.txt</from>\n      <to>FL and housenr>100</to>\n      <enabled>Y</enabled>\n    </hop>\n    <hop>\n      <from>FL and housenr>100</from>\n      <to>True</to>\n      <enabled>Y</enabled>\n    </hop>\n    <hop>\n      <from>FL and housenr>100</from>\n      <to>False</to>\n      <enabled>Y</enabled>\n    </hop>\n  </order>\n  <transform>\n    <name>FL and housenr>100</name>\n    <type>FilterRows</type>\n    <description/>\n    <distribute>Y</distribute>\n    <custom_distribution/>\n    <copies>1</copies>\n    <partitioning>\n      <method>none</method>\n      <schema_name/>\n    </partitioning>\n    <compare>\n      <condition>\n        <conditions>\n</conditions>\n        <function>=</function>\n        <leftvalue>stateCode</leftvalue>\n        <negated>N</negated>\n        <operator>-</operator>\n        <value>\n          <isnull>N</isnull>\n          <length>-1</length>\n          <name>constant</name>\n          <precision>-1</precision>\n          <text>FL</text>\n          <type>String</type>\n        </value>\n      </condition>\n    </compare>\n    <attributes/>\n    <GUI>\n      <xloc>480</xloc>\n      <yloc>112</yloc>\n    </GUI>\n  </transform>\n  <transform>\n    <name>False</name>\n    <type>Dummy</type>\n    <description/>\n    <distribute>Y</distribute>\n    <custom_distribution/>\n    <copies>1</copies>\n    <partitioning>\n      <method>none</method>\n      <schema_name/>\n    </partitioning>\n    <attributes/>\n    <GUI>\n      <xloc>752</xloc>\n      <yloc>224</yloc>\n    </GUI>\n  </transform>\n  <transform>\n    <name>True</name>\n    <type>Dummy</type>\n    <description/>\n    <distribute>Y</distribute>\n    <custom_distribution/>\n    <copies>1</copies>\n    <partitioning>\n      <method>none</method>\n      <schema_name/>\n    </partitioning>\n    <attributes/>\n    <GUI>\n      <xloc>752</xloc>\n      <yloc>112</yloc>\n    </GUI>\n  </transform>\n  <transform>\n    <name>files/customers-100.txt</name>\n    <type>CSVInput</type>\n    <description/>\n    <distribute>Y</distribute>\n    <custom_distribution/>\n    <copies>1</copies>\n    <partitioning>\n      <method>none</method>\n      <schema_name/>\n    </partitioning>\n    <filename>${PROJECT_HOME}/files/customers-100.txt</filename>\n    <filename_field/>\n    <rownum_field/>\n    <include_filename>N</include_filename>\n    <separator>;</separator>\n    <enclosure>\"</enclosure>\n    <header>Y</header>\n    <buffer_size>50000</buffer_size>\n    <schemaDefinition/>\n    <lazy_conversion>N</lazy_conversion>\n    <add_filename_result>N</add_filename_result>\n    <parallel>N</parallel>\n    <newline_possible>N</newline_possible>\n    <encoding/>\n    <fields>\n      <field>\n        <name>id</name>\n        <type>Integer</type>\n        <format> #</format>\n        <currency>$</currency>\n        <decimal>.</decimal>\n        <group>,</group>\n        <length>15</length>\n        <precision>0</precision>\n        <trim_type>none</trim_type>\n      </field>\n      <field>\n        <name>name</name>\n        <type>String</type>\n        <format/>\n        <currency>$</currency>\n        <decimal>.</decimal>\n        <group>,</group>\n        <length>10</length>\n        <precision>-1</precision>\n        <trim_type>none</trim_type>\n      </field>\n      <field>\n        <name>firstname</name>\n        <type>String</type>\n        <format/>\n        <currency>$</currency>\n        <decimal>.</decimal>\n        <group>,</group>\n        <length>13</length>\n        <precision>-1</precision>\n        <trim_type>none</trim_type>\n      </field>\n      <field>\n        <name>zip</name>\n        <type>Integer</type>\n        <format> #</format>\n        <currency>$</currency>\n        <decimal>.</decimal>\n        <group>,</group>\n        <length>15</length>\n        <precision>0</precision>\n        <trim_type>none</trim_type>\n      </field>\n      <field>\n        <name>city</name>\n        <type>String</type>\n        <format/>\n        <currency>$</currency>\n        <decimal>.</decimal>\n        <group>,</group>\n        <length>8</length>\n        <precision>-1</precision>\n        <trim_type>none</trim_type>\n      </field>\n      <field>\n        <name>birthdate</name>\n        <type>Date</type>\n        <format>yyyy/MM/dd</format>\n        <currency>$</currency>\n        <decimal>.</decimal>\n        <group>,</group>\n        <length>-1</length>\n        <precision>-1</precision>\n        <trim_type>none</trim_type>\n      </field>\n      <field>\n        <name>street</name>\n        <type>String</type>\n        <format/>\n        <currency>$</currency>\n        <decimal>.</decimal>\n        <group>,</group>\n        <length>11</length>\n        <precision>-1</precision>\n        <trim_type>none</trim_type>\n      </field>\n      <field>\n        <name>housenr</name>\n        <type>Integer</type>\n        <format> #</format>\n        <currency>$</currency>\n        <decimal>.</decimal>\n        <group>,</group>\n        <length>15</length>\n        <precision>0</precision>\n        <trim_type>none</trim_type>\n      </field>\n      <field>\n        <name>stateCode</name>\n        <type>String</type>\n        <format/>\n        <currency>$</currency>\n        <decimal>.</decimal>\n        <group>,</group>\n        <length>9</length>\n        <precision>-1</precision>\n        <trim_type>none</trim_type>\n      </field>\n      <field>\n        <name>state</name>\n        <type>String</type>\n        <format/>\n        <currency>$</currency>\n        <decimal>.</decimal>\n        <group>,</group>\n        <length>30</length>\n        <precision>-1</precision>\n        <trim_type>none</trim_type>\n      </field>\n    </fields>\n    <attributes/>\n    <GUI>\n      <xloc>224</xloc>\n      <yloc>112</yloc>\n    </GUI>\n  </transform>\n  <transform_error_handling>\n  </transform_error_handling>\n  <attributes/>\n</pipeline>\n", "metadataJson": "{\"server\":[],\"pipeline-probe\":[],\"execution-info-location\":[{\"virtualPath\":null,\"executionInfoLocation\":{\"local-folder\":{\"pluginName\":\"File location\",\"pluginId\":\"local-folder\",\"rootFolder\":\"${HOP_AUDIT_FOLDER}\\/executions\\/\"}},\"dataLoggingDelay\":\"2000\",\"name\":\"local-audit\",\"description\":\"\",\"dataLoggingInterval\":\"5000\"}],\"workflow-log\":[],\"GoogleStorageConnectionDefinition\":[],\"schema-definition\":[],\"cassandra-connection\":[],\"neo4j-graph-model\":[],\"mongodb-connection\":[],\"execution-data-profile\":[{\"virtualPath\":null,\"name\":\"first-last\",\"description\":\"capture the first and last 100 rows of every Hop transform\",\"sampler\":[{\"FirstRowsExecutionDataSampler\":{\"sampleSize\":\"100\"}},{\"LastRowsExecutionDataSampler\":{\"sampleSize\":\"100\"}}]}],\"partition\":[],\"workflow-run-configuration\":[{\"engineRunConfiguration\":{\"Local\":{\"safe_mode\":false,\"transactional\":false}},\"virtualPath\":null,\"defaultSelection\":true,\"name\":\"local\",\"description\":\"\",\"executionInfoLocationName\":\"local-audit\"}],\"restconnection\":[],\"unit-test\":[],\"rdbms\":[],\"AzureConnectionDefinition\":[],\"web-service\":[],\"pipeline-run-configuration\":[{\"engineRunConfiguration\":{\"Local\":{\"feedback_size\":\"50000\",\"sample_size\":\"100\",\"sample_type_in_gui\":\"Last\",\"wait_time\":\"20\",\"rowset_size\":\"10000\",\"safe_mode\":false,\"show_feedback\":false,\"topo_sort\":false,\"gather_metrics\":false,\"transactional\":false}},\"virtualPath\":null,\"defaultSelection\":true,\"configurationVariables\":[],\"name\":\"local\",\"description\":\"\",\"dataProfile\":\"first-last\",\"executionInfoLocationName\":\"local-audit\"}],\"neo4j-connection\":[],\"async-web-service\":[],\"pipeline-log\":[],\"file-definition\":[],\"splunk\":[],\"dataset\":[],\"variable-resolver\":[]}", "runConfigurationName": "local", "logLevel": "BASIC", "parameterValues": {}, "environmentDetails": {"JavaUser": "Yeheng.Hu", "ContainerId": "266e2140-15ab-493f-a651-bce4316d9164", "AvailableProcessors": "20", "TotalMemory": "278921216", "JavaVersion": "17.0.14", "HostAddress": "************", "HostName": "CN-D-6L0MKZ3", "MaxMemory": "2147483648", "FreeMemory": "98656272"}, "registrationDate": 1756888790161, "executionStartDate": 1756888790069, "copyNr": null}