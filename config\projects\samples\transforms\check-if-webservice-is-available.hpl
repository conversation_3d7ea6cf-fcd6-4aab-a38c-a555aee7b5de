<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>transformscheck-if-webservice-is-available</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/02/07 20:08:56.935</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/02/07 20:08:56.935</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>generate 1 row</from>
      <to>Check if webservice is available</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>available?</from>
      <to>log available</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>available?</from>
      <to>log not available</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Check if webservice is available</from>
      <to>available?</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>generate 1 row</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>url</name>
        <nullif>https://httpbin.org/get</nullif>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <type>String</type>
      </field>
    </fields>
    <interval_in_ms>5000</interval_in_ms>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <limit>10</limit>
    <never_ending>N</never_ending>
    <row_time_field>now</row_time_field>
    <attributes/>
    <GUI>
      <xloc>80</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Check if webservice is available</name>
    <type>WebServiceAvailable</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connectTimeOut>0</connectTimeOut>
    <readTimeOut>0</readTimeOut>
    <resultfieldname>result</resultfieldname>
    <urlField>url</urlField>
    <attributes/>
    <GUI>
      <xloc>282</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>available?</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compare>
      <condition>
        <conditions>
</conditions>
        <function>=</function>
        <leftvalue>result</leftvalue>
        <negated>N</negated>
        <operator>-</operator>
        <value>
          <isnull>N</isnull>
          <length>-1</length>
          <name>constant</name>
          <precision>-1</precision>
          <text>Y</text>
          <type>Boolean</type>
        </value>
      </condition>
    </compare>
    <send_false_to>log not available</send_false_to>
    <send_true_to>log available</send_true_to>
    <attributes/>
    <GUI>
      <xloc>484</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>log available</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage>the web service is available.</logmessage>
    <fields>
      </fields>
    <attributes/>
    <GUI>
      <xloc>686</xloc>
      <yloc>48</yloc>
    </GUI>
  </transform>
  <transform>
    <name>log not available</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage>the web service is not available.</logmessage>
    <fields>
      </fields>
    <attributes/>
    <GUI>
      <xloc>688</xloc>
      <yloc>192</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
