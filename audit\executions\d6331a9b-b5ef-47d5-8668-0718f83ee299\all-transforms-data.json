{"executionType": "Transform", "dataSetMeta": null, "finished": true, "collectionDate": 1756949112318, "parentId": "d6331a9b-b5ef-47d5-8668-0718f83ee299", "ownerId": "all-transforms", "setMetaData": {"FirstOutput/字段映射.0": {"setKey": "FirstOutput/字段映射.0", "logChannelId": "faafb041-184e-4c20-bfc7-6e7f0681b085", "name": "字段映射", "fieldName": null, "sampleDescription": null, "copyNr": "0", "description": "First rows of 字段映射.0"}, "LastOutput/字段映射.0": {"setKey": "LastOutput/字段映射.0", "logChannelId": "faafb041-184e-4c20-bfc7-6e7f0681b085", "name": "字段映射", "fieldName": null, "sampleDescription": null, "copyNr": "0", "description": "Last rows of 字段映射.0"}, "LastOutput/CSV输入.0": {"setKey": "LastOutput/CSV输入.0", "logChannelId": "a4a06d54-e4f0-4f43-a61a-42c7158ec6cb", "name": "CSV输入", "fieldName": null, "sampleDescription": null, "copyNr": "0", "description": "Last rows of CSV输入.0"}, "FirstOutput/CSV输出.0": {"setKey": "FirstOutput/CSV输出.0", "logChannelId": "a679aed3-b292-4857-bca2-1f174861d4b4", "name": "CSV输出", "fieldName": null, "sampleDescription": null, "copyNr": "0", "description": "First rows of CSV输出.0"}, "FirstOutput/CSV输入.0": {"setKey": "FirstOutput/CSV输入.0", "logChannelId": "a4a06d54-e4f0-4f43-a61a-42c7158ec6cb", "name": "CSV输入", "fieldName": null, "sampleDescription": null, "copyNr": "0", "description": "First rows of CSV输入.0"}, "LastOutput/CSV输出.0": {"setKey": "LastOutput/CSV输出.0", "logChannelId": "a679aed3-b292-4857-bca2-1f174861d4b4", "name": "CSV输出", "fieldName": null, "sampleDescription": null, "copyNr": "0", "description": "Last rows of CSV输出.0"}}, "rowsBinaryGzipBase64Encoded": "H4sIAAAAAAAA/2NgYGBjkHLLLCou8S8tKSgt0X+6dvqzdVufzVjwdEOLngEDDEj6JBJSIoakxDk47MW+yU9blyLJiyNbA1XQvouAAhQTsNiAbAAAC7ndZs0AAAA="}