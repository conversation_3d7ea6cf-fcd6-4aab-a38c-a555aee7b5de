<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>kafka-consumer-read-pokemon-data</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/07/29 10:49:00.946</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/07/29 10:49:00.946</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>This sample reads JSON messages from a Kafka topic.  
A batch of messages is read and sent to the child pipeline.  
If processing was a success the messages are flagged as read on the Kafka server topic and a next batch of records is taken.


Please set the following variables in your environment configuration file(s):

  KAFKA_SERVER
  KAFKA_TOPIC
  KAFKA_CLIENT_ID
</note>
      <xloc>96</xloc>
      <yloc>144</yloc>
      <width>640</width>
      <heigth>181</heigth>
      <fontname>Noto Sans</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Kafka Consumer</from>
      <to>Output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Kafka Consumer</name>
    <type>KafkaConsumer</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <topic>${KAFKA_TOPIC}</topic>
    <consumerGroup/>
    <pipelinePath>${PROJECT_HOME}/transforms/kafka-consumer-read-pokemon-data-child.hpl</pipelinePath>
    <subTransform>OUTPUT</subTransform>
    <batchSize>1000</batchSize>
    <batchDuration>1000</batchDuration>
    <directBootstrapServers>${KAFKA_SERVER}</directBootstrapServers>
    <AUTO_COMMIT>Y</AUTO_COMMIT>
    <OutputField kafkaName="key" type="String">Key</OutputField>
    <OutputField kafkaName="message" type="String">Message</OutputField>
    <OutputField kafkaName="topic" type="String">Topic</OutputField>
    <OutputField kafkaName="partition" type="Integer">Partition</OutputField>
    <OutputField kafkaName="offset" type="Integer">Offset</OutputField>
    <OutputField kafkaName="timestamp" type="Integer">Timestamp</OutputField>
    <advancedConfig>
      <option property="auto.offset.reset" value="latest"/>
      <option property="ssl.key.password" value=""/>
      <option property="ssl.keystore.location" value=""/>
      <option property="ssl.keystore.password" value=""/>
      <option property="ssl.truststore.location" value=""/>
      <option property="ssl.truststore.password" value=""/>
    </advancedConfig>
    <attributes/>
    <GUI>
      <xloc>128</xloc>
      <yloc>64</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>464</xloc>
      <yloc>64</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
