                                 Apache License
                           Version 2.0, January 2004
                        http://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

   END OF TERMS AND CONDITIONS

   APPENDIX: How to apply the Apache License to your work.

      To apply the Apache License to your work, attach the following
      boilerplate notice, with the fields enclosed by brackets "[]"
      replaced with your own identifying information. (Don't include
      the brackets!)  The text should be enclosed in the appropriate
      comment syntax for the file format. We also recommend that a
      file or class name and description of purpose be included on the
      same "printed page" as the copyright notice for easier
      identification within third-party archives.

   Copyright [yyyy] [name of copyright owner]

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

=======================================================================
Apache Hop Subcomponents:

The Apache Hop project contains subcomponents with separate copyright
notices and license terms. Your use of the source code for the these
subcomponents is subject to the terms and conditions of the following
licenses.

This product bundles accessors-smart 2.5.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles activation 1.1, which is available under a
"Common Development and Distribution 1.0" license. For details, see licenses/LICENSE-CDDL10.

This product bundles adal4j 1.6.4, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles adapter-rxjava 2.7.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles airline 0.8, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles amazon-kinesis-client 1.14.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles amazon-kinesis-producer 0.14.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles animal-sniffer-annotations 1.24, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles annotations 13.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles annotations 4.1.1.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles antlr 3.5.3, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles antlr-complete 3.5.3, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles antlr-runtime 3.5.2, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles aopalliance 1.0, which is available under a
"Public Domain" license. For details, see Public Domain.

This product bundles aopalliance-repackaged 2.6.1, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles apache-mime4j-core 0.8.11, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles apache-mime4j-dom 0.8.11, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles api-common 2.38.0, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles apicurio-registry-protobuf-schema-utilities 3.0.0.M2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles asm 9.7, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles asm-analysis 9.2, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles asm-commons 9.7, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles asm-tree 9.7, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles asm-util 9.7, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles async-profiler 2.9, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles auto-common 1.2.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles auto-service 1.1.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles auto-service-annotations 1.1.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles auto-value 1.11.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles auto-value-annotations 1.11.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles automaton 1.11-8, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles avro 1.12.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles aws-java-sdk-cloudwatch 1.12.135, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles aws-java-sdk-core 1.12.347, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles aws-java-sdk-core 1.12.651, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles aws-java-sdk-dynamodb 1.11.844, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles aws-java-sdk-kinesis 1.12.135, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles aws-java-sdk-kms 1.12.347, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles aws-java-sdk-kms 1.12.651, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles aws-java-sdk-s3 1.12.347, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles aws-java-sdk-s3 1.12.651, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles aws-java-sdk-sns 1.12.651, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles aws-java-sdk-sqs 1.12.651, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles azure-annotations 1.10.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-client-authentication 1.7.3, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-client-runtime 1.7.3, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-core 1.40.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-core 1.50.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-core-http-netty 1.13.4, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-core-http-netty 1.15.2, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-eventhubs 3.3.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-eventhubs-eph 3.3.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-identity 1.9.1, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-json 1.0.1, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-json 1.1.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-keyvault-core 1.0.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-storage 8.0.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles azure-storage 8.6.5, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles azure-storage-blob 12.22.3, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-storage-blob 12.27.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-storage-common 12.21.2, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-storage-common 12.26.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-storage-file-datalake 12.20.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-storage-internal-avro 12.12.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-storage-internal-avro 12.7.2, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles azure-xml 1.0.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles batik-anim 1.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles batik-awt-util 1.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles batik-bridge 1.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles batik-codec 1.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles batik-constants 1.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles batik-css 1.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles batik-dom 1.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles batik-ext 1.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles batik-gvt 1.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles batik-i18n 1.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles batik-parser 1.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles batik-script 1.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles batik-svg-dom 1.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles batik-svggen 1.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles batik-transcoder 1.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles batik-util 1.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles batik-xml 1.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles bcprov-jdk18on 1.78.1, which is available under a
"Bouncy Castle Licence" license. For details, see licenses/LICENSE-BOUNCY.

This product bundles beam-model-fn-execution 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-model-job-management 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-model-pipeline 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-runners-core-java 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-runners-direct-java 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-runners-flink-1.19 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-runners-google-cloud-dataflow-java 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-runners-java-fn-execution 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-runners-spark-3 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-sdks-java-core 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-sdks-java-expansion-service 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-sdks-java-extensions-avro 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-sdks-java-extensions-google-cloud-platform-core 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-sdks-java-extensions-join-library 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-sdks-java-extensions-protobuf 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-sdks-java-io-azure 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-sdks-java-io-google-cloud-platform 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-sdks-java-io-hadoop-common 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-sdks-java-io-hadoop-file-system 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-sdks-java-io-hcatalog 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-sdks-java-io-kafka 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-sdks-java-io-kinesis 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-sdks-java-io-synthetic 2.62.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-vendor-grpc-1_60_1 0.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles beam-vendor-guava-32_1_2-jre 0.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles byte-buddy 1.14.15, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles caffeine 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles cassandra-all 4.1.8, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles checker-qual 3.47.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles chronicle-bytes 2.20.111, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles chronicle-core 2.20.126, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles chronicle-queue 5.20.123, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles chronicle-threads 2.20.111, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles chronicle-wire 2.20.117, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles classgraph 4.8.162, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles clickhouse-jdbc-0.8.2-all.jar , which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles client 1.0.0-beta.2, which is available under a
"2-clause BSD" license. For details, see licenses/LICENSE-BSD2.

This product bundles client-runtime 1.7.3, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles com.wcohen.secondstring 0.1, which is available under a
"The University of Illinois/NCSA Open Source" license. For details, see licenses/LICENSE-NCSA.

This product bundles common 1.0.0-beta.2, which is available under a
"2-clause BSD" license. For details, see licenses/LICENSE-BSD2.

This product bundles common-utils 7.6.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-beanutils 1.9.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-cli 1.5.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-codec 1.17.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-collections 3.2.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-collections4 4.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-compiler 3.1.11, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles commons-compress 1.27.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-configuration2 2.10.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-csv 1.11.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-daemon 1.0.13, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-dbcp2 2.12.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-digester 2.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-exec 1.4.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-io 2.16.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-lang 2.6, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-lang3 3.12.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-logging 1.1.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-math3 3.6.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-net 3.9.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-pool2 2.12.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-text 1.10.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-validator 1.9.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles commons-vfs2 2.10.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles concurrent-trees 2.4.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles config 1.4.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles conscrypt-openjdk-uber 2.5.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles content-type 2.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles converter-gson 2.9.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles converter-jackson 2.7.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles crate-jdbc 2.7.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles curvesapi 1.08, which is available under a
"2-clause BSD" license. For details, see licenses/LICENSE-BSD2.

This product bundles dd-plist 1.28, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles dec 0.1.2, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles derbyclient 10.17.1.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles detector-resources-support 0.32.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles dnsjava 3.6.1, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles dom4j 2.1.3, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles drools-compiler 7.74.1.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles drools-core 7.74.1.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles drools-core-dynamic 7.74.1.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles drools-core-reflective 7.74.1.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles drools-decisiontables 7.74.1.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles drools-ecj 7.74.1.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles drools-mvel 7.74.1.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles drools-templates 7.74.1.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles dropbox-core-sdk 7.0.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles duckdb_jdbc 1.2.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles ecj 4.6.1, which is available under a
"Eclipse Public 1.0" license. For details, see licenses/LICENSE-EPL10.

This product bundles eclipse-collections 11.1.0, which is available under a
"Eclipse Public 1.0" license. For details, see licenses/LICENSE-EPL10.

This product bundles eclipse-collections-api 11.1.0, which is available under a
"Eclipse Public 1.0" license. For details, see licenses/LICENSE-EPL10.

This product bundles encoder 1.2, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles error_prone_annotations 2.35.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles exporter-metrics 0.31.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles failureaccess 1.0.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-annotations 1.19.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-clients 1.19.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-connector-datagen 1.19.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-core 1.19.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-file-sink-common 1.19.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-hadoop-fs 1.19.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-java 1.19.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-metrics-core 1.19.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-optimizer 1.19.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-queryable-state-client-java 1.19.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-rpc-akka-loader 1.19.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-rpc-core 1.19.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-runtime 1.19.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-shaded-asm-9 9.5-17.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-shaded-guava 31.1-jre-17.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-shaded-jackson 2.14.2-17.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-shaded-netty 4.1.91.Final-17.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-shaded-zookeeper-3 3.7.1-17.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flink-streaming-java 1.19.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flogger 0.7.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles flogger-system-backend 0.7.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles fontbox 2.0.31, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles force-partner-api 58.0.0, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles force-wsc 58.0.0, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles gapic-google-cloud-storage-v2 2.43.2-beta, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles gax 2.55.0, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles gax-grpc 2.55.0, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles gax-httpjson 2.55.0, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles gcsio 2.2.26, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles generex 1.0.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-analytics-data 0.65.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-api-client 2.7.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-api-client-jackson2 2.7.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-api-services-bigquery v2-rev20240919-2.0.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-api-services-cloudresourcemanager v1-rev20240310-2.0.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-api-services-dataflow v1b3-rev20240817-2.0.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-api-services-drive v3-rev20250216-2.0.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-api-services-iamcredentials v1-rev20211203-2.0.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-api-services-sheets v4-rev20250211-2.0.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-api-services-sqladmin v1-rev20230721-2.0.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-api-services-storage v1-rev20240924-2.0.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-auth-library-credentials 1.28.0, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles google-auth-library-credentials 1.30.0, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles google-auth-library-oauth2-http 1.28.0, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles google-auth-library-oauth2-http 1.30.0, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles google-cloud-bigquery 2.43.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-cloud-bigquerystorage 3.9.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-cloud-bigtable 2.45.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-cloud-core 2.45.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-cloud-core-grpc 2.45.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-cloud-core-http 2.45.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-cloud-monitoring 3.53.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-cloud-secretmanager 2.52.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-cloud-storage 2.43.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-cloud-storage-control 2.43.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-extensions 0.7.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-http-client 1.45.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-http-client-apache-v2 1.45.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-http-client-appengine 1.45.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-http-client-gson 1.45.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-http-client-jackson2 1.45.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-oauth-client 1.36.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-oauth-client-java6 1.36.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles google-oauth-client-jetty 1.36.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles groovy 4.0.22, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles groovy-jsr223 4.0.22, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-alts 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-api 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-auth 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-census 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-context 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-core 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-google-cloud-bigquerystorage-v1 3.9.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-google-cloud-bigquerystorage-v1beta1 0.181.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-google-cloud-bigquerystorage-v1beta2 0.181.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-google-cloud-bigtable-v2 2.45.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-google-cloud-storage-control-v2 2.43.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-google-cloud-storage-v2 2.43.2-beta, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-googleapis 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-grpclb 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-inprocess 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-netty-shaded 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-opentelemetry 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-protobuf 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-protobuf-lite 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-rls 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-s2a 1.70.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-services 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-stub 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-util 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles grpc-xds 1.67.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles gson 2.10, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles gson 2.11.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles guava 33.3.1-jre, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles guice 4.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles guice-servlet 4.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles h2 2.3.232, which is available under a
"Mozilla Public 2.0" license. For details, see licenses/LICENSE-MPL20.

This product bundles hadoop-annotations 3.4.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles hadoop-archives 3.1.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles hadoop-auth 3.4.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles hadoop-client 3.4.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles hadoop-common 3.4.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles hadoop-hdfs 3.1.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles hadoop-hdfs-client 3.4.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles hadoop-mapreduce-client-core 3.1.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles hadoop-mapreduce-client-core 3.4.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles hadoop-shaded-guava 1.3.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles hadoop-shaded-protobuf_3_25 1.3.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles hadoop-yarn-api 3.1.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles hadoop-yarn-client 3.1.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles hadoop-yarn-common 3.1.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles hamcrest 2.1, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles HdrHistogram 2.1.12, which is available under a
"Creative Commons" license. For details, see licenses/LICENSE-CC0.

This product bundles high-scale-lib 1.0.6, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles hive-hcatalog-core 3.1.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles hive-jdbc 4.0.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles hk2-api 2.6.1, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles hk2-locator 2.6.1, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles hk2-utils 2.6.1, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles hppc 0.8.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles hsqldb 2.7.4, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles httpclient 4.5.13, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles httpclient5 5.3.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles httpcore 4.4.15, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles httpcore5 5.2.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles httpcore5-h2 5.2.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles httpmime 4.5.13, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles icu4j 73.2, which is available under a
"Unicode/ICU License" license. For details, see licenses/LICENSE-UNICODE.

This product bundles ini4j 0.5.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles ion-java 1.0.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles ipaddress 5.3.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles istack-commons-runtime 3.0.12, which is available under a
"Eclipse Public 1.0" license. For details, see licenses/LICENSE-EPL10.

This product bundles j2objc-annotations 1.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles j2objc-annotations 3.0.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackcess 4.0.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackcess 5.1.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackcess-encrypt 4.0.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackson-annotations 2.15.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackson-core 2.15.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackson-databind 2.15.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackson-dataformat-avro 2.15.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackson-dataformat-cbor 2.15.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackson-dataformat-csv 2.14.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackson-dataformat-xml 2.15.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackson-datatype-jdk8 2.15.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackson-datatype-joda 2.10.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackson-datatype-joda 2.15.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackson-datatype-jsr310 2.13.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackson-datatype-jsr310 2.13.5, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackson-datatype-jsr310 2.18.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackson-jaxrs-base 2.15.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackson-jaxrs-json-provider 2.15.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jackson-module-jaxb-annotations 2.15.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jai-imageio-core 1.4.0, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles jakarta.activation 2.0.1, which is available under a
"Eclipse Public 1.0" license. For details, see licenses/LICENSE-EPL10.

This product bundles jakarta.activation-api-1.2.1.jar , which is available under a
"Eclipse Public 1.0" license. For details, see licenses/LICENSE-EPL10.

This product bundles jakarta.annotation-api 1.3.5, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles jakarta.inject 2.6.1, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles jakarta.mail 2.0.1, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles jakarta.transaction-api 1.3.3, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles jakarta.validation-api 2.0.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jakarta.ws.rs-api 2.1.6, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles jakarta.xml.bind-api 4.0.2, which is available under a
"Eclipse Public 1.0" license. For details, see licenses/LICENSE-EPL10.

This product bundles jakarta.xml.bind-api-2.3.2.jar , which is available under a
"Eclipse Public 1.0" license. For details, see licenses/LICENSE-EPL10.

This product bundles jamm 0.3.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jandex 3.2.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles janino 3.1.11, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles java-driver-core 4.17.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles java-driver-query-builder 4.17.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles java-driver-shaded-guava 25.1-jre-graal-sub-1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles java-libpst 0.9.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles JavaEWAH 1.2.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles javafaker 1.0.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles javapoet 1.13.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles javassist 3.28.0-GA, which is available under a
"Mozilla Public 1.1" license. For details, see licenses/LICENSE-MPL11.

This product bundles javax.annotation-api 1.3.2, which is available under a
"Common Development and Distribution 1.1" license. For details, see licenses/LICENSE-CDDL11.

This product bundles javax.inject 1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles javax.mail 1.6.1, which is available under a
"Common Development and Distribution 1.0" license. For details, see licenses/LICENSE-CDDL10.

This product bundles javax.servlet-api 3.1.0, which is available under a
"Common Development and Distribution 1.1" license. For details, see licenses/LICENSE-CDDL11.

This product bundles jaxb-api 2.2.11, which is available under a
"Common Development and Distribution 1.1" license. For details, see licenses/LICENSE-CDDL11.

This product bundles jaxb-runtime 2.3.6, which is available under a
"Eclipse Public 1.0" license. For details, see licenses/LICENSE-EPL10.

This product bundles jaxen 1.2.0, which is available under a
"2-clause BSD" license. For details, see licenses/LICENSE-BSD2.

This product bundles jbcrypt 0.4, which is available under a
"ISC License" license. For details, see licenses/LICENSE-ISC.

This product bundles jbig2-imageio 3.0.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jcip-annotations 1.0-1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jcommander 1.30, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jctools-core 3.1.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jdom2 *******, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jempbox 1.8.17, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jersey-apache-connector 2.43, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles jersey-client 2.43, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles jersey-common 2.43, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles jersey-container-servlet 2.43, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles jersey-container-servlet-core 2.43, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles jersey-hk2 2.43, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles jersey-jetty-connector 2.43, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles jersey-server 2.43, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles jettison 1.5.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty 6.1.26, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty-client 6.1.26, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty-client 9.4.54.v20240208, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty-continuation 9.4.41.v20210516, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty-http 9.4.41.v20210516, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty-io 9.4.41.v20210516, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty-jaas 9.4.41.v20210516, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty-security 9.4.41.v20210516, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty-server 9.4.41.v20210516, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty-servlet 9.4.41.v20210516, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty-servlets 9.4.41.v20210516, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty-sslengine 6.1.26, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty-util 6.1.26, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty-util 9.4.41.v20210516, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty-util-ajax 9.4.41.v20210516, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty-util5 6.1.26, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty-webapp 9.4.41.v20210516, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jetty-xml 9.4.41.v20210516, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jffi 1.3.9, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jffi-1.3.9-native.jar , which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jhighlight 1.1.0, which is available under a
"Common Development and Distribution 1.0" license. For details, see licenses/LICENSE-CDDL10.

This product bundles jmatio 1.5, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles jmespath-java 1.12.135, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jmespath-java 1.12.651, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jna 4.2.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jna 5.13.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jna-platform 4.2.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jna-platform 5.13.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jnr-a64asm 1.0.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jnr-constants 0.10.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jnr-ffi 2.2.11, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jnr-posix 3.1.15, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles jnr-x86asm 1.0.2, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles joda-time 2.12.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jsch 0.1.55, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles jsch 0.2.18, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles jsendnsca 2.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles json 20240303, which is available under a
"Public Domain" license. For details, see Public Domain.

This product bundles json-path 2.9.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles json-simple 1.1.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles json-smart 2.5.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jsoup 1.18.1, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles jsp-api 2.1, which is available under a
"Common Development and Distribution 1.1" license. For details, see licenses/LICENSE-CDDL11.

This product bundles jsr305 2.0.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jsr305 3.0.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jt400 21.0.0, which is available under a
"IBM Public 1.0" license. For details, see licenses/LICENSE-IBM10.

This product bundles jtokkit 1.0.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles juniversalchardet 2.4.0, which is available under a
"Mozilla Public 1.1" license. For details, see licenses/LICENSE-MPL11.

This product bundles junrar 7.5.5, which is available under a
"UnRar License" license. For details, see licenses/LICENSE-UNRAR.

This product bundles jvm-attach-api 1.5, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jwarc 0.29.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles jython-standalone 2.7.4, which is available under a
"Jython Software License" license. For details, see licenses/LICENSE-JYTHON.

This product bundles kafka-avro-serializer 7.5.5, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kafka-avro-serializer 7.6.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kafka-clients 3.7.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kafka-schema-registry-client 7.5.5, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kafka-schema-registry-client 7.6.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kafka-schema-serializer 7.5.5, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kafka-schema-serializer 7.6.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kaml 0.20.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kerb-core 2.0.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kerb-crypto 2.0.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kerb-util 2.0.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kerby-asn1 2.0.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kerby-config 2.0.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kerby-pkix 2.0.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kerby-util 2.0.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kie-api 7.74.1.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kie-internal 7.74.1.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kie-memory-compiler 7.74.1.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kie-soup-commons 7.74.1.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kie-soup-maven-support 7.74.1.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kie-soup-project-datamodel-api 7.74.1.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kie-soup-project-datamodel-commons 7.74.1.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kie-soup-xstream 7.74.1.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kotlin-reflect 1.9.20, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kotlin-stdlib 1.9.25, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kotlin-stdlib-common 1.9.10, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kotlin-stdlib-jdk7 1.9.25, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kotlin-stdlib-jdk8 1.9.25, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kotlinpoet 1.12.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kotlinpoet-jvm 1.15.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kotlinx-coroutines-core-jvm 1.5.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kotlinx-datetime-jvm 0.4.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kotlinx-serialization-core-jvm 1.0.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles kryo 2.24.0, which is available under a
"2-clause BSD" license. For details, see licenses/LICENSE-BSD2.

This product bundles lang-tag 1.7, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles langchain4j 0.29.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles langchain4j-anthropic 0.29.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles langchain4j-core 0.29.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles langchain4j-hugging-face 0.29.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles langchain4j-mistral-ai 0.29.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles langchain4j-ollama 0.29.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles langchain4j-open-ai 0.29.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles leveldbjni-all 1.8, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles libphonenumber 8.13.40, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles listenablefuture 9999.0-empty-to-avoid-conflict-with-guava, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles log4j-api 2.23.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles log4j-core 2.23.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles logging-interceptor 3.12.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles logredactor 1.0.12, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles logredactor-metrics 1.0.12, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles lz4-java 1.8.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles managed-kafka-auth-login-handler 1.0.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles metadata-extractor 2.19.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles metrics-core 4.1.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles metrics-core 4.1.18, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles metrics-jvm 3.1.5, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles metrics-logback 3.1.5, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles minimal-json 0.9.5, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles minlog 1.2, which is available under a
"2-clause BSD" license. For details, see licenses/LICENSE-BSD2.

This product bundles monetdb-jdbc 12.0, which is available under a
"Mozilla Public 2.0" license. For details, see licenses/LICENSE-MPL20.

This product bundles mongo-java-driver 3.12.14, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles moshi 1.15.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles moshi 1.3.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles msal4j 1.13.8, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles msal4j-persistence-extension 1.2.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles mssql-jdbc 12.8.1.jre11, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles mvel2 2.4.15.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles mxdump 0.14, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles mxparser 1.2.2, which is available under a
"Indiana University Extreme! Lab Software License" license. For details, see licenses/LICENSE-IUE.

This product bundles native-protocol 1.5.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles neo4j-java-driver 4.4.20, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-all 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-buffer 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-codec 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-codec-dns 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-codec-haproxy 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-codec-http 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-codec-http2 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-codec-memcache 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-codec-mqtt 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-codec-redis 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-codec-smtp 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-codec-socks 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-codec-stomp 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-codec-xml 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-common 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-handler 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-handler-proxy 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-handler-ssl-ocsp 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-resolver 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-resolver-dns 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-resolver-dns-classes-macos 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-resolver-dns-native-macos-4.1.119.Final-osx-aarch_64.jar , which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-resolver-dns-native-macos-4.1.119.Final-osx-x86_64.jar , which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-tcnative-boringssl-static 2.0.65.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-tcnative-boringssl-static-2.0.65.Final-linux-aarch_64.jar , which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-tcnative-boringssl-static-2.0.65.Final-linux-x86_64.jar , which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-tcnative-boringssl-static-2.0.65.Final-osx-aarch_64.jar , which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-tcnative-boringssl-static-2.0.65.Final-osx-x86_64.jar , which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-tcnative-boringssl-static-2.0.65.Final-windows-x86_64.jar , which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-tcnative-classes 2.0.65.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-transport 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-transport-classes-epoll 4.1.100.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-transport-classes-epoll 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-transport-classes-kqueue 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-transport-native-epoll 4.1.100.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-transport-native-epoll-4.1.119.Final-linux-aarch_64.jar , which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-transport-native-epoll-4.1.119.Final-linux-riscv64.jar , which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-transport-native-epoll-4.1.119.Final-linux-x86_64.jar , which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-transport-native-kqueue-4.1.119.Final-osx-aarch_64.jar , which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-transport-native-kqueue-4.1.119.Final-osx-x86_64.jar , which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-transport-native-unix-common 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-transport-rxtx 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-transport-sctp 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles netty-transport-udt 4.1.119.Final, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles nimbus-jose-jwt 9.30.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles nimbus-jose-jwt 9.8.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles oauth2-oidc-sdk 10.7.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles oauth2-oidc-sdk 6.5, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles objenesis-2.1.jar , which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles odfdom-java 0.8.7, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles ohc-core 0.5.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles ohc-core-j8 0.5.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles okhttp 4.12.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles okhttp-sse 4.12.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles okhttp-urlconnection 3.12.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles okio 3.9.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles okio-fakefilesystem 3.4.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles okio-fakefilesystem-jvm 3.4.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles okio-jvm 3.6.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles okio-jvm 3.9.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles openai4j 0.17.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles opencensus-api 0.31.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles opencensus-contrib-http-util 0.31.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles opencensus-proto 0.2.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles opencsv 5.7.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles opennlp-tools 1.9.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles opentelemetry-api 1.42.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles opentelemetry-api-incubator 1.42.1-alpha, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles opentelemetry-context 1.42.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles opentelemetry-gcp-resources 1.37.0-alpha, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles opentelemetry-sdk 1.42.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles opentelemetry-sdk-common 1.42.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles opentelemetry-sdk-extension-autoconfigure-spi 1.42.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles opentelemetry-sdk-logs 1.42.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles opentelemetry-sdk-metrics 1.42.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles opentelemetry-sdk-trace 1.42.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles opentelemetry-semconv 1.25.0-alpha, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles org.eclipse.jgit 6.10.0.202406032230-r, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles org.eclipse.jgit.http.apache 6.10.0.202406032230-r, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles org.osgi.core 4.3.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles org.osgi.enterprise 4.2.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles osgi-resource-locator 1.0.3, which is available under a
"Eclipse Public 2.0" license. For details, see licenses/LICENSE-EPL20.

This product bundles parquet-avro 1.14.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles parquet-column 1.14.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles parquet-common 1.14.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles parquet-encoding 1.14.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles parquet-format-structures 1.14.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles parquet-hadoop 1.14.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles parquet-jackson 1.14.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles parso 2.0.13, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles pdfbox 2.0.31, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles pdfbox-tools 2.0.31, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles perfmark-api 0.27.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles picocli 4.6.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles poi 5.3.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles poi-ooxml 5.3.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles poi-ooxml-lite 5.3.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles poi-scratchpad 5.2.5, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles postgresql 42.7.5, which is available under a
"2-clause BSD" license. For details, see licenses/LICENSE-BSD2.

This product bundles proto-google-analytics-data-v1alpha 0.63.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles proto-google-analytics-data-v1beta 0.63.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles proto-google-cloud-bigquerystorage-v1 3.9.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles proto-google-cloud-bigquerystorage-v1alpha 3.9.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles proto-google-cloud-bigquerystorage-v1beta1 0.181.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles proto-google-cloud-bigquerystorage-v1beta2 0.181.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles proto-google-cloud-bigtable-admin-v2 2.45.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles proto-google-cloud-bigtable-v2 2.45.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles proto-google-cloud-monitoring-v3 3.53.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles proto-google-cloud-pubsublite-v1 1.14.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles proto-google-cloud-secretmanager-v1 2.52.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles proto-google-cloud-secretmanager-v1beta2 2.52.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles proto-google-cloud-storage-control-v2 2.43.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles proto-google-cloud-storage-v2 2.43.2-beta, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles proto-google-common-protos 2.46.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles proto-google-iam-v1 1.41.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles protobuf-java 3.25.5, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles protobuf-java-util 3.25.5, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles proton-j 0.33.8, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles psjava 0.1.19, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles qpid-proton-j-extensions 1.2.4, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles re2j 1.6, which is available under a
"Go License" license. For details, see licenses/LICENSE-GO.

This product bundles re2j 1.7, which is available under a
"Go License" license. For details, see licenses/LICENSE-GO.

This product bundles reactive-streams 1.0.4, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles reactor-core 3.4.29, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles reactor-core 3.4.38, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles reactor-netty-core 1.2.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles reactor-netty-http 1.2.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles redshift-jdbc42 2.1.0.32, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles reload4j 1.2.22, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles reporter-config-base 3.0.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles reporter-config3 3.0.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles retrofit 2.7.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles retrofit 2.9.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles rhino 1.7.14, which is available under a
"Mozilla Public 2.0" license. For details, see licenses/LICENSE-MPL20.

This product bundles rome 2.1.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles rome-utils 2.1.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles rxjava 1.3.8, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles saxon 8.7, which is available under a
"Mozilla Public 1.0" license. For details, see licenses/LICENSE-MPL10.

This product bundles saxon-dom 8.7, which is available under a
"Mozilla Public 1.0" license. For details, see licenses/LICENSE-MPL10.

This product bundles shared-resourcemapping 0.32.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles sigar 1.6.4, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles sjk-cli 0.14, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles sjk-core 0.14, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles sjk-json 0.14, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles sjk-stacktrace 0.14, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles slf4j-api 2.0.4, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles slf4j-nop 2.0.4, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles snakeyaml 2.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles snakeyaml-engine 2.6, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles snappy-java 1.1.10.7, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles snmp4j 1.9.1f, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles snowball-stemmer 1.3.0.581.1, which is available under a
"2-clause BSD" license. For details, see licenses/LICENSE-BSD2.

This product bundles snowflake-jdbc 3.23.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles SparseBitSet 1.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles splunk 1.6.5.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles spring-core 5.3.27, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles spring-expression 5.3.27, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles spring-jcl 5.3.27, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles sqlite-jdbc 3.49.1.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles sshlib 2.2.23, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles ST4 4.0.8, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles ST4 4.3.1, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles stax2-api 4.2.2, which is available under a
"2-clause BSD" license. For details, see licenses/LICENSE-BSD2.

This product bundles stream 2.5.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles swagger-annotations 2.1.10, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles swiftpoet 1.3.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles swt.jar , which is available under a
"Eclipse Public 1.0" license. For details, see licenses/LICENSE-EPL10.

This product bundles tagsoup 1.2.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles threeten-extra 1.8.0, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles threetenbp 1.7.0, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles tika-core 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-apple-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-audiovideo-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-cad-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-code-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-crypto-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-digest-commons 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-font-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-html-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-image-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-mail-commons 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-mail-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-microsoft-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-miscoffice-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-news-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-ocr-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-pdf-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-pkg-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-text-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-webarchive-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-xml-module 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-xmp-commons 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parser-zip-commons 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles tika-parsers-standard-package 2.9.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles txw2 2.3.6, which is available under a
"Eclipse Public 1.0" license. For details, see licenses/LICENSE-EPL10.

This product bundles ucanaccess 5.1.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles util 2.2.26, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles vault-java-driver 2.0.0, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles vorbis-java-core 0.8, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles vorbis-java-tika 0.8, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles waffle-jna 1.7.5, which is available under a
"The MIT" license. For details, see licenses/LICENSE-MIT.

This product bundles webservices-api 2.3.1, which is available under a
"Common Development and Distribution 1.1" license. For details, see licenses/LICENSE-CDDL11.

This product bundles wire-compiler 4.5.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles wire-grpc-client-jvm 4.5.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles wire-grpc-server 4.5.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles wire-grpc-server-generator 4.5.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles wire-java-generator 4.5.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles wire-kotlin-generator 4.5.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles wire-runtime 4.8.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles wire-runtime-jvm 4.9.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles wire-schema 4.8.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles wire-schema-jvm 4.9.3, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles wire-swift-generator 4.5.0, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles woodstox-core 6.5.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles wsdl4j 1.6.2, which is available under a
"COMMON PUBLIC 1.0" license. For details, see licenses/LICENSE-CPL10.

This product bundles xercesImpl 2.12.2, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles xml-apis 1.4.01, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles xml-apis-ext 1.3.04, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles xmlbeans 5.2.1, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles xmlgraphics-commons 2.9, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles xmlpull 1.1.3.1, which is available under a
"Public Domain" license. For details, see Public Domain.

This product bundles xmpbox 2.0.31, which is available under a
"Apache 2.0" license. For details, see LICENSE.

This product bundles xmpcore 6.1.11, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles xstream 1.4.20, which is available under a
"3-clause BSD" license. For details, see licenses/LICENSE-BSD3.

This product bundles xz 1.9, which is available under a
"Public Domain" license. For details, see Public Domain.

This product bundles zstd-jni 1.5.6-3, which is available under a
"2-clause BSD" license. For details, see licenses/LICENSE-BSD2.
