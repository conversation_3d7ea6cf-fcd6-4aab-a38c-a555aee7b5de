<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>get-variables-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/02/22 10:27:40.052</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/02/22 10:27:40.052</modified_date>
  </info>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Noto Sans</fontname>
      <fontsize>10</fontsize>
      <height>27</height>
      <xloc>64</xloc>
      <yloc>32</yloc>
      <note>get a number of different variables from the current Apache Hop project, os, java and user environment</note>
      <width>585</width>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>generate 1 row</from>
      <to>get variables</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>get variables</from>
      <to>log variable values</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>generate 1 row</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
</fields>
    <interval_in_ms>5000</interval_in_ms>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <limit>1</limit>
    <never_ending>N</never_ending>
    <row_time_field>now</row_time_field>
    <attributes/>
    <GUI>
      <xloc>96</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>get variables</name>
    <type>GetVariable</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>filename</name>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <type>String</type>
        <variable>${PROJECT_HOME}/files/customers-100.txt</variable>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>java_version</name>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <type>String</type>
        <variable>${java.runtime.version}</variable>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>hop_config_folder</name>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <type>String</type>
        <variable>${HOP_CONFIG_FOLDER}</variable>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>hop_platform_os</name>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <type>String</type>
        <variable>${HOP_PLATFORM_OS}</variable>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>user_home</name>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <type>String</type>
        <variable>${user.home}</variable>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>user_dir</name>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <type>String</type>
        <variable>${user.dir}</variable>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>304</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>log variable values</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage/>
    <fields>
      <field>
        <name>filename</name>
      </field>
      <field>
        <name>java_version</name>
      </field>
      <field>
        <name>hop_config_folder</name>
      </field>
      <field>
        <name>hop_platform_os</name>
      </field>
      <field>
        <name>user_home</name>
      </field>
      <field>
        <name>user_dir</name>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>512</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
