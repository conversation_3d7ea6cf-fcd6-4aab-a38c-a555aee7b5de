<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>json-output-generate-nested-structure</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/01/03 09:01:40.388</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/01/03 09:01:40.388</modified_date>
  </info>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Segoe UI</fontname>
      <fontsize>11</fontsize>
      <height>78</height>
      <xloc>64</xloc>
      <yloc>32</yloc>
      <note>This sample gets a table of values and generates a complex nested JSON structure by looping over a predefined key.
The second 'Enhanced JSON Output' transform uses a JSON fragment
from the first 'Enhanced JSON Output' transform and uses it as value of an attribute of the resulting final
JSON value.</note>
      <width>655</width>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Input data</from>
      <to>Sort the fields</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Sort the fields</from>
      <to>Intermediate Structures</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Intermediate Structures</from>
      <to>Final Complex Data structure</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Final Complex Data structure</name>
    <type>EnhancedJsonOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <outputValue>details</outputValue>
    <jsonBloc>results</jsonBloc>
    <operation_type>outputvalue</operation_type>
    <use_arrays_with_single_instance>N</use_arrays_with_single_instance>
    <json_prittified>N</json_prittified>
    <encoding>UTF-8</encoding>
    <addtoresult>N</addtoresult>
    <file>
      <name/>
      <split_output_after>0</split_output_after>
      <extention>js</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <create_parent_folder>N</create_parent_folder>
      <doNotOpenNewFileInit>N</doNotOpenNewFileInit>
    </file>
    <additional_fields>
      <json_size_field/>
    </additional_fields>
    <key_fields>
    </key_fields>
    <fields>
      <field>
        <name>field1</name>
        <element>campo1</element>
        <json_fragment>N</json_fragment>
        <is_without_enclosing>N</is_without_enclosing>
        <remove_if_blank>N</remove_if_blank>
      </field>
      <field>
        <name>lvl1Details</name>
        <element>details</element>
        <json_fragment>Y</json_fragment>
        <is_without_enclosing>N</is_without_enclosing>
        <remove_if_blank>N</remove_if_blank>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>560</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Input data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>A</item>
        <item>B</item>
        <item>1</item>
      </line>
      <line>
        <item>A</item>
        <item>B</item>
        <item>2</item>
      </line>
      <line>
        <item>B</item>
        <item>C</item>
        <item>1</item>
      </line>
      <line>
        <item>B</item>
        <item>C</item>
        <item>2</item>
      </line>
      <line>
        <item>B</item>
        <item>D</item>
        <item>4</item>
      </line>
      <line>
        <item>C</item>
        <item>F</item>
        <item>5</item>
      </line>
      <line>
        <item>C</item>
        <item>F</item>
        <item>6</item>
      </line>
      <line>
        <item>C</item>
        <item>V</item>
        <item>6</item>
      </line>
      <line>
        <item>C</item>
        <item>B</item>
        <item>7</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>field1</name>
        <type>String</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>field2</name>
        <type>String</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>field3</name>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>80</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Intermediate Structures</name>
    <type>EnhancedJsonOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <outputValue>lvl1Details</outputValue>
    <jsonBloc/>
    <operation_type>outputvalue</operation_type>
    <use_arrays_with_single_instance>N</use_arrays_with_single_instance>
    <json_prittified>Y</json_prittified>
    <encoding>UTF-8</encoding>
    <addtoresult>N</addtoresult>
    <file>
      <name/>
      <split_output_after>0</split_output_after>
      <extention>js</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <create_parent_folder>N</create_parent_folder>
      <doNotOpenNewFileInit>N</doNotOpenNewFileInit>
    </file>
    <additional_fields>
      <json_size_field/>
    </additional_fields>
    <key_fields>
      <key_field>
        <key_field_name>field1</key_field_name>
        <key_field_element>key2</key_field_element>
      </key_field>
    </key_fields>
    <fields>
      <field>
        <name>field2</name>
        <element>campo2</element>
        <json_fragment>N</json_fragment>
        <is_without_enclosing>N</is_without_enclosing>
        <remove_if_blank>N</remove_if_blank>
      </field>
      <field>
        <name>field3</name>
        <element>campo3</element>
        <json_fragment>N</json_fragment>
        <is_without_enclosing>N</is_without_enclosing>
        <remove_if_blank>N</remove_if_blank>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>384</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Sort the fields</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>${java.io.tmpdir}</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>field1</name>
        <ascending>N</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>field2</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>224</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
