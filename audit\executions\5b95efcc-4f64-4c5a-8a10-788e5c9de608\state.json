{"executionType": "Pipeline", "parentId": null, "id": "5b95efcc-4f64-4c5a-8a10-788e5c9de608", "name": "filter-rows-mdi-child", "copyNr": null, "loggingText": "2025/09/03 16:39:50 - filter-rows-mdi-child - Executing this pipeline using the Local Pipeline Engine with run configuration 'local'\r\n2025/09/03 16:39:50 - filter-rows-mdi-child - 为了 Pipeline 解除补丁开始 [filter-rows-mdi-child]\r\n2025/09/03 16:39:50 - files/customers-100.txt.0 - ERROR: Unexpected error\r\n2025/09/03 16:39:50 - files/customers-100.txt.0 - ERROR: org.apache.hop.core.exception.HopFileException: \r\n2025/09/03 16:39:50 - files/customers-100.txt.0 - \r\n2025/09/03 16:39:50 - files/customers-100.txt.0 - 创建字段映射时意外出错\r\n2025/09/03 16:39:50 - files/customers-100.txt.0 - Could not read from \"file:///F:/hu/H-Documentation/database/新建文件夹/hop/config/projects/default/files/customers-100.txt\" because it is not a file.\r\n2025/09/03 16:39:50 - files/customers-100.txt.0 - \r\n2025/09/03 16:39:50 - files/customers-100.txt.0 - Could not read from \"file:///F:/hu/H-Documentation/database/新建文件夹/hop/config/projects/default/files/customers-100.txt\" because it is not a file.\r\n2025/09/03 16:39:50 - files/customers-100.txt.0 - \r\n2025/09/03 16:39:50 - files/customers-100.txt.0 - \tat org.apache.hop.pipeline.transforms.csvinput.CsvInput.readFieldNamesFromFile(CsvInput.java:495)\r\n2025/09/03 16:39:50 - files/customers-100.txt.0 - \tat org.apache.hop.pipeline.transforms.csvinput.CsvInput.createFieldMapping(CsvInput.java:449)\r\n2025/09/03 16:39:50 - files/customers-100.txt.0 - \tat org.apache.hop.pipeline.transforms.csvinput.CsvInput.openNextFile(CsvInput.java:330)\r\n2025/09/03 16:39:50 - files/customers-100.txt.0 - \tat org.apache.hop.pipeline.transforms.csvinput.CsvInput.processRow(CsvInput.java:122)\r\n2025/09/03 16:39:50 - files/customers-100.txt.0 - \tat org.apache.hop.pipeline.transform.RunThread.run(RunThread.java:54)\r\n2025/09/03 16:39:50 - files/customers-100.txt.0 - \tat java.base/java.lang.Thread.run(Thread.java:842)\r\n2025/09/03 16:39:50 - files/customers-100.txt.0 - Caused by: org.apache.commons.vfs2.FileNotFoundException: Could not read from \"file:///F:/hu/H-Documentation/database/新建文件夹/hop/config", "lastLogLineNr": 13, "metrics": [{"componentName": "files/customers-100.txt", "componentCopy": "0", "metrics": {"Read": 0, "Buffers Output": 0, "Errors": 1, "Input": 0, "Written": 0, "Updated": 0, "Output": 0, "Rejected": 0, "Buffers Input": 0}}, {"componentName": "FL and housenr>100", "componentCopy": "0", "metrics": {"Read": 0, "Buffers Output": 0, "Errors": 0, "Input": 0, "Written": 0, "Updated": 0, "Output": 0, "Rejected": 0, "Buffers Input": 0}}, {"componentName": "True", "componentCopy": "0", "metrics": {"Read": 0, "Buffers Output": 0, "Errors": 0, "Input": 0, "Written": 0, "Updated": 0, "Output": 0, "Rejected": 0, "Buffers Input": 0}}, {"componentName": "False", "componentCopy": "0", "metrics": {"Read": 0, "Buffers Output": 0, "Errors": 0, "Input": 0, "Written": 0, "Updated": 0, "Output": 0, "Rejected": 0, "Buffers Input": 0}}], "statusDescription": "Finished (with errors)", "updateTime": 1756888790469, "childIds": ["5112c15e-923a-446a-8da8-c0f854deb2d2", "ce632fbf-cf77-4158-a798-99e72f8e549b", "2ce68001-8c6e-4623-b04a-4ef03a8fa359", "5308977a-35bb-4b25-9dac-e55bd579af00"], "details": {}, "failed": true, "executionEndDate": 1756888790456, "containerId": "266e2140-15ab-493f-a651-bce4316d9164"}