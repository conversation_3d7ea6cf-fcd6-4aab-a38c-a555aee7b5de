<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>switch-case-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/07/14 16:44:48.606</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/07/14 16:44:48.606</modified_date>
    <key_for_session_key/>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Switch on id, follow different streams for values 1-4 or use the default for all other values (id 5 in this example). </note>
      <xloc>144</xloc>
      <yloc>96</yloc>
      <width>624</width>
      <heigth>27</heigth>
      <fontname>Noto Sans</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Test Data</from>
      <to>Switch id</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch id</from>
      <to>Output 1</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch id</from>
      <to>Output 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch id</from>
      <to>Output 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch id</from>
      <to>Output 4</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch id</from>
      <to>Output default</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Test Data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>id</name>
        <precision>-1</precision>
        <type>Integer</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>desc</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
    </fields>
    <data>
      <line>
        <item>1</item>
        <item>one</item>
      </line>
      <line>
        <item>2</item>
        <item>two</item>
      </line>
      <line>
        <item>3</item>
        <item>three</item>
      </line>
      <line>
        <item>4</item>
        <item>for</item>
      </line>
      <line>
        <item>5</item>
        <item>five</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>160</xloc>
      <yloc>256</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Switch id</name>
    <type>SwitchCase</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <cases>
      <case>
        <target_transform>Output 1</target_transform>
        <value>1</value>
      </case>
      <case>
        <target_transform>Output 2</target_transform>
        <value>2</value>
      </case>
      <case>
        <target_transform>Output 3</target_transform>
        <value>3</value>
      </case>
      <case>
        <target_transform>Output 4</target_transform>
        <value>4</value>
      </case>
    </cases>
    <case_value_decimal/>
    <case_value_format/>
    <case_value_group/>
    <case_value_type/>
    <default_target_transform>Output default</default_target_transform>
    <fieldname>id</fieldname>
    <use_contains>N</use_contains>
    <attributes/>
    <GUI>
      <xloc>304</xloc>
      <yloc>256</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output 1</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>496</xloc>
      <yloc>176</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output default</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>304</xloc>
      <yloc>416</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output 4</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>496</xloc>
      <yloc>416</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output 3</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>496</xloc>
      <yloc>336</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output 2</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>496</xloc>
      <yloc>256</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
