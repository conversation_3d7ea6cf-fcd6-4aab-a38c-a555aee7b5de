2025/09/04 09:25:12 - simple_mapping_test - Executing this pipeline using the Local Pipeline Engine with run configuration 'local'
2025/09/04 09:25:12 - simple_mapping_test - 为了 Pipeline 解除补丁开始 [simple_mapping_test]
2025/09/04 09:25:12 - CSV输入.0 - ERROR: Unexpected error
2025/09/04 09:25:12 - CSV输入.0 - ERROR: org.apache.hop.core.exception.HopFileException: 
2025/09/04 09:25:12 - CSV输入.0 - 
2025/09/04 09:25:12 - CSV输入.0 - 创建字段映射时意外出错
2025/09/04 09:25:12 - CSV输入.0 - Could not read from "file:///F:/hu/H-Documentation/database/新建文件夹/hop/datasets/source_table.csv" because it is not a file.
2025/09/04 09:25:12 - CSV输入.0 - 
2025/09/04 09:25:12 - CSV输入.0 - Could not read from "file:///F:/hu/H-Documentation/database/新建文件夹/hop/datasets/source_table.csv" because it is not a file.
2025/09/04 09:25:12 - CSV输入.0 - 
2025/09/04 09:25:12 - CSV输入.0 - 	at org.apache.hop.pipeline.transforms.csvinput.CsvInput.readFieldNamesFromFile(CsvInput.java:495)
2025/09/04 09:25:12 - CSV输入.0 - 	at org.apache.hop.pipeline.transforms.csvinput.CsvInput.createFieldMapping(CsvInput.java:449)
2025/09/04 09:25:12 - CSV输入.0 - 	at org.apache.hop.pipeline.transforms.csvinput.CsvInput.openNextFile(CsvInput.java:330)
2025/09/04 09:25:12 - CSV输入.0 - 	at org.apache.hop.pipeline.transforms.csvinput.CsvInput.processRow(CsvInput.java:122)
2025/09/04 09:25:12 - CSV输入.0 - 	at org.apache.hop.pipeline.transform.RunThread.run(RunThread.java:54)
2025/09/04 09:25:12 - CSV输入.0 - 	at java.base/java.lang.Thread.run(Thread.java:842)
2025/09/04 09:25:12 - CSV输入.0 - Caused by: org.apache.commons.vfs2.FileNotFoundException: Could not read from "file:///F:/hu/H-Documentation/database/新建文件夹/hop/datasets/source_table.csv" because it is not a file.
2025/09/04 09:25:12 - CSV输入.0 - 	at org.apache.commons.vfs2.provider.AbstractFileObject.getInputStream(AbstractFileObject.java:1202)
2025/09/04 09:25:12 - CSV输入.0 - 	at org.apache.commons.vfs2.provider.AbstractFileObject.getInputStream(AbstractFileObject.java:1187)
2025/09/04 09:25:12 - CSV输入.0 - 	at org.apache.commons.vfs2.provider.DefaultFileContent.buildInputStream(DefaultFileContent.java:257)
2025/09/04 09:25:12 - CSV输入.0 - 	at org.apache.commons.vfs2.provider.DefaultFileContent.getInputStream(DefaultFileContent.java:525)
2025/09/04 09:25:12 - CSV输入.0 - 	at org.apache.hop.core.vfs.HopVfs.getInputStream(HopVfs.java:381)
2025/09/04 09:25:12 - CSV输入.0 - 	at org.apache.hop.pipeline.transforms.csvinput.CsvInput.readFieldNamesFromFile(CsvInput.java:467)
2025/09/04 09:25:12 - CSV输入.0 - 	... 5 more
2025/09/04 09:25:12 - CSV输入.0 - Caused by: java.io.FileNotFoundException: F:\hu\H-Documentation\database\新建文件夹\hop\datasets\source_table.csv (系统找不到指定的文件。)
2025/09/04 09:25:12 - CSV输入.0 - 	at java.base/java.io.FileInputStream.open0(Native Method)
2025/09/04 09:25:12 - CSV输入.0 - 	at java.base/java.io.FileInputStream.open(FileInputStream.java:216)
2025/09/04 09:25:12 - CSV输入.0 - 	at java.base/java.io.FileInputStream.<init>(FileInputStream.java:157)
2025/09/04 09:25:12 - CSV输入.0 - 	at org.apache.commons.vfs2.provider.local.LocalFile.doGetInputStream(LocalFile.java:105)
2025/09/04 09:25:12 - CSV输入.0 - 	at org.apache.commons.vfs2.provider.AbstractFileObject.getInputStream(AbstractFileObject.java:1200)
2025/09/04 09:25:12 - CSV输入.0 - 	... 10 more
2025/09/04 09:25:12 - CSV输入.0 - 完成处理 (I=0, O=0, R=0, W=0, U=0, E=1)
2025/09/04 09:25:12 - simple_mapping_test - Pipeline 被检测 
2025/09/04 09:25:12 - simple_mapping_test - Pipeline 正在杀死其他 Transform!
2025/09/04 09:25:12 - simple_mapping_test - Pipeline duration : 0.11 seconds [  0.110" ]
