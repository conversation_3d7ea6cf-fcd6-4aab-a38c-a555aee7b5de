<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>beers-wikipedia-query-paths</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/08/02 11:36:38.413</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/08/02 11:36:38.413</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>How are 2 beers related?</from>
      <to>get relationships</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>How are 2 beers related?</from>
      <to>get nodes</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>get nodes</from>
      <to>get node data</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>get relationships</from>
      <to>get relationship data</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Lookup source node label</from>
      <to>Lookup target node label</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>node data</from>
      <to>Lookup source node label</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>node data</from>
      <to>Lookup target node label</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>get relationship data</from>
      <to>relationship data</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>relationship data</from>
      <to>Lookup source node label</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Beers</from>
      <to>How are 2 beers related?</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>get node data</from>
      <to>node data</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Lookup target node label</from>
      <to>Output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>How are 2 beers related?</name>
    <type>Neo4jCypherOutput</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>demo</connection>
    <cypher>MATCH (s:Brand { name : $beer1 } ), 
      (t:Brand { name : $beer2 } ),
     p=shortestpath((s)-[*..5]-(t))
return p as beerPaths</cypher>
    <batch_size>1</batch_size>
    <read_only>Y</read_only>
    <nr_retries_on_error/>
    <retry>Y</retry>
    <cypher_from_field>N</cypher_from_field>
    <cypher_field/>
    <unwind>N</unwind>
    <unwind_map/>
    <returning_graph>N</returning_graph>
    <return_graph_field/>
    <mappings>
      <mapping>
        <parameter>beer1</parameter>
        <field>beer1</field>
        <type>String</type>
      </mapping>
      <mapping>
        <parameter>beer2</parameter>
        <field>beer2</field>
        <type>String</type>
      </mapping>
    </mappings>
    <returns>
      <return>
        <name>beerPaths</name>
        <type>String</type>
        <source_type>Path</source_type>
      </return>
    </returns>
    <attributes/>
    <GUI>
      <xloc>208</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Lookup source node label</name>
    <type>StreamLookup</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <from>node data</from>
    <input_sorted>N</input_sorted>
    <preserve_memory>Y</preserve_memory>
    <sorted_list>N</sorted_list>
    <integer_pair>N</integer_pair>
    <lookup>
      <key>
        <name>sourceNodeId</name>
        <field>nodeId</field>
      </key>
      <value>
        <name>nodeLabel</name>
        <rename>sourceNodeLabel</rename>
        <default/>
        <type>String</type>
      </value>
    </lookup>
    <attributes/>
    <GUI>
      <xloc>784</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Lookup target node label</name>
    <type>StreamLookup</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <from>node data</from>
    <input_sorted>N</input_sorted>
    <preserve_memory>Y</preserve_memory>
    <sorted_list>N</sorted_list>
    <integer_pair>N</integer_pair>
    <lookup>
      <key>
        <name>targetNodeId</name>
        <field>nodeId</field>
      </key>
      <value>
        <name>nodeLabel</name>
        <rename>targetNodeLabel</rename>
        <default/>
        <type>String</type>
      </value>
    </lookup>
    <attributes/>
    <GUI>
      <xloc>960</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>get node data</name>
    <type>JsonInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <include>N</include>
    <include_field/>
    <rownum>N</rownum>
    <addresultfile>N</addresultfile>
    <readurl>N</readurl>
    <removeSourceField>N</removeSourceField>
    <IsIgnoreEmptyFile>N</IsIgnoreEmptyFile>
    <doNotFailIfNoFile>Y</doNotFailIfNoFile>
    <ignoreMissingPath>Y</ignoreMissingPath>
    <defaultPathLeafToNull>Y</defaultPathLeafToNull>
    <rownum_field/>
    <file>
      <name/>
      <filemask/>
      <exclude_filemask/>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
    </file>
    <fields>
      <field>
        <name>nodeLabel</name>
        <path>labels[0]</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>nodeId</name>
        <path>id</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>nodeValue</name>
        <path>properties[0].value</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
    </fields>
    <limit>0</limit>
    <IsInFields>Y</IsInFields>
    <IsAFile>N</IsAFile>
    <valueField>node</valueField>
    <shortFileFieldName/>
    <pathFieldName/>
    <hiddenFieldName/>
    <lastModificationTimeFieldName/>
    <uriNameFieldName/>
    <rootUriNameFieldName/>
    <extensionFieldName/>
    <sizeFieldName/>
    <attributes/>
    <GUI>
      <xloc>512</xloc>
      <yloc>192</yloc>
    </GUI>
  </transform>
  <transform>
    <name>get nodes</name>
    <type>JsonInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <include>N</include>
    <include_field/>
    <rownum>N</rownum>
    <addresultfile>N</addresultfile>
    <readurl>N</readurl>
    <removeSourceField>N</removeSourceField>
    <IsIgnoreEmptyFile>N</IsIgnoreEmptyFile>
    <doNotFailIfNoFile>Y</doNotFailIfNoFile>
    <ignoreMissingPath>Y</ignoreMissingPath>
    <defaultPathLeafToNull>Y</defaultPathLeafToNull>
    <rownum_field/>
    <file>
      <name/>
      <filemask/>
      <exclude_filemask/>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
    </file>
    <fields>
      <field>
        <name>node</name>
        <path>nodes[*]</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
    </fields>
    <limit>0</limit>
    <IsInFields>Y</IsInFields>
    <IsAFile>N</IsAFile>
    <valueField>beerPaths</valueField>
    <shortFileFieldName/>
    <pathFieldName/>
    <hiddenFieldName/>
    <lastModificationTimeFieldName/>
    <uriNameFieldName/>
    <rootUriNameFieldName/>
    <extensionFieldName/>
    <sizeFieldName/>
    <attributes/>
    <GUI>
      <xloc>368</xloc>
      <yloc>192</yloc>
    </GUI>
  </transform>
  <transform>
    <name>get relationship data</name>
    <type>JsonInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <include>N</include>
    <include_field/>
    <rownum>N</rownum>
    <addresultfile>N</addresultfile>
    <readurl>N</readurl>
    <removeSourceField>N</removeSourceField>
    <IsIgnoreEmptyFile>N</IsIgnoreEmptyFile>
    <doNotFailIfNoFile>Y</doNotFailIfNoFile>
    <ignoreMissingPath>Y</ignoreMissingPath>
    <defaultPathLeafToNull>Y</defaultPathLeafToNull>
    <rownum_field/>
    <file>
      <name/>
      <filemask/>
      <exclude_filemask/>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
    </file>
    <fields>
      <field>
        <name>label</name>
        <path>label</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>id</name>
        <path>id</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>sourceNodeId</name>
        <path>sourceNodeId</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>targetNodeId</name>
        <path>targetNodeId</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
    </fields>
    <limit>0</limit>
    <IsInFields>Y</IsInFields>
    <IsAFile>N</IsAFile>
    <valueField>relationship</valueField>
    <shortFileFieldName/>
    <pathFieldName/>
    <hiddenFieldName/>
    <lastModificationTimeFieldName/>
    <uriNameFieldName/>
    <rootUriNameFieldName/>
    <extensionFieldName/>
    <sizeFieldName/>
    <attributes/>
    <GUI>
      <xloc>512</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>get relationships</name>
    <type>JsonInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <include>N</include>
    <include_field/>
    <rownum>N</rownum>
    <addresultfile>N</addresultfile>
    <readurl>N</readurl>
    <removeSourceField>N</removeSourceField>
    <IsIgnoreEmptyFile>N</IsIgnoreEmptyFile>
    <doNotFailIfNoFile>Y</doNotFailIfNoFile>
    <ignoreMissingPath>Y</ignoreMissingPath>
    <defaultPathLeafToNull>Y</defaultPathLeafToNull>
    <rownum_field/>
    <file>
      <name/>
      <filemask/>
      <exclude_filemask/>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
    </file>
    <fields>
      <field>
        <name>relationship</name>
        <path>relationships[*]</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
    </fields>
    <limit>0</limit>
    <IsInFields>Y</IsInFields>
    <IsAFile>N</IsAFile>
    <valueField>beerPaths</valueField>
    <shortFileFieldName/>
    <pathFieldName/>
    <hiddenFieldName/>
    <lastModificationTimeFieldName/>
    <uriNameFieldName/>
    <rootUriNameFieldName/>
    <extensionFieldName/>
    <sizeFieldName/>
    <attributes/>
    <GUI>
      <xloc>368</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>node data</name>
    <type>SelectValues</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>nodeId</name>
        <rename/>
      </field>
      <field>
        <name>nodeLabel</name>
        <rename/>
      </field>
      <field>
        <name>nodeValue</name>
        <rename/>
      </field>
      <select_unspecified>N</select_unspecified>
      <meta>
        <name>nodeId</name>
        <rename>nodeId</rename>
        <type>Integer</type>
        <length>-2</length>
        <precision>-2</precision>
        <conversion_mask>#</conversion_mask>
        <date_format_lenient>false</date_format_lenient>
        <date_format_locale/>
        <date_format_timezone/>
        <lenient_string_to_number>false</lenient_string_to_number>
        <encoding/>
        <decimal_symbol/>
        <grouping_symbol/>
        <currency_symbol/>
        <storage_type/>
      </meta>
    </fields>
    <attributes/>
    <GUI>
      <xloc>880</xloc>
      <yloc>192</yloc>
    </GUI>
  </transform>
  <transform>
    <name>relationship data</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>label</name>
      </field>
      <field>
        <name>sourceNodeId</name>
      </field>
      <field>
        <name>targetNodeId</name>
      </field>
      <select_unspecified>N</select_unspecified>
      <meta>
        <name>id</name>
        <rename>id</rename>
        <type>Integer</type>
        <length>-2</length>
        <precision>-2</precision>
        <conversion_mask>#</conversion_mask>
        <date_format_lenient>false</date_format_lenient>
        <date_format_locale/>
        <date_format_timezone/>
        <lenient_string_to_number>false</lenient_string_to_number>
        <encoding/>
        <decimal_symbol/>
        <grouping_symbol/>
        <currency_symbol/>
        <storage_type/>
      </meta>
      <meta>
        <name>sourceNodeId</name>
        <rename>sourceNodeId</rename>
        <type>Integer</type>
        <length>-2</length>
        <precision>-2</precision>
        <conversion_mask>#</conversion_mask>
        <date_format_lenient>false</date_format_lenient>
        <date_format_locale/>
        <date_format_timezone/>
        <lenient_string_to_number>false</lenient_string_to_number>
        <encoding/>
        <decimal_symbol/>
        <grouping_symbol/>
        <currency_symbol/>
        <storage_type/>
      </meta>
      <meta>
        <name>targetNodeId</name>
        <rename>targetNodeId</rename>
        <type>Integer</type>
        <length>-2</length>
        <precision>-2</precision>
        <conversion_mask>#</conversion_mask>
        <date_format_lenient>false</date_format_lenient>
        <date_format_locale/>
        <date_format_timezone/>
        <lenient_string_to_number>false</lenient_string_to_number>
        <encoding/>
        <decimal_symbol/>
        <grouping_symbol/>
        <currency_symbol/>
        <storage_type/>
      </meta>
    </fields>
    <attributes/>
    <GUI>
      <xloc>640</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Beers</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>beer1</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>beer2</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
    </fields>
    <data>
      <line>
        <item>Duvel</item>
        <item>Hoegaarden Grand Cru</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>80</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>1136</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
