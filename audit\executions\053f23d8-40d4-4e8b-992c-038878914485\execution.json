{"name": "write-read-huge-file", "filename": "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\workflows\\write-read-huge-file.hwf", "id": "053f23d8-40d4-4e8b-992c-038878914485", "parentId": null, "executionType": "Workflow", "executorXml": "<workflow>\n  <name>write-read-huge-file</name>\n  <name_sync_with_filename>Y</name_sync_with_filename>\n  <description/>\n  <extended_description/>\n  <workflow_version/>\n  <created_user>-</created_user>\n  <created_date>2022/10/27 10:34:32.295</created_date>\n  <modified_user>-</modified_user>\n  <modified_date>2022/10/27 10:34:32.295</modified_date>\n  <parameters>\n    </parameters>\n  <actions>\n    <action>\n      <name>Start</name>\n      <description/>\n      <type>SPECIAL</type>\n      <attributes/>\n      <DayOfMonth>1</DayOfMonth>\n      <hour>12</hour>\n      <intervalMinutes>60</intervalMinutes>\n      <intervalSeconds>0</intervalSeconds>\n      <minutes>0</minutes>\n      <repeat>N</repeat>\n      <schedulerType>0</schedulerType>\n      <weekDay>1</weekDay>\n      <parallel>N</parallel>\n      <xloc>64</xloc>\n      <yloc>112</yloc>\n      <attributes_hac/>\n    </action>\n    <action>\n      <name>textfileoutput-huge-file.hpl</name>\n      <description/>\n      <type>PIPELINE</type>\n      <attributes/>\n      <add_date>N</add_date>\n      <add_time>N</add_time>\n      <clear_files>N</clear_files>\n      <clear_rows>N</clear_rows>\n      <create_parent_folder>N</create_parent_folder>\n      <exec_per_row>N</exec_per_row>\n      <filename>${PROJECT_HOME}/workflows/textfileoutput-huge-file.hpl</filename>\n      <loglevel>Basic</loglevel>\n      <parameters>\n        <pass_all_parameters>Y</pass_all_parameters>\n      </parameters>\n      <params_from_previous>N</params_from_previous>\n      <run_configuration>local</run_configuration>\n      <set_append_logfile>N</set_append_logfile>\n      <set_logfile>N</set_logfile>\n      <wait_until_finished>Y</wait_until_finished>\n      <parallel>N</parallel>\n      <xloc>224</xloc>\n      <yloc>112</yloc>\n      <attributes_hac/>\n    </action>\n    <action>\n      <name>csvinput-huge-file.hpl</name>\n      <description/>\n      <type>PIPELINE</type>\n      <attributes/>\n      <add_date>N</add_date>\n      <add_time>N</add_time>\n      <clear_files>N</clear_files>\n      <clear_rows>N</clear_rows>\n      <create_parent_folder>N</create_parent_folder>\n      <exec_per_row>N</exec_per_row>\n      <filename>${PROJECT_HOME}/workflows/csvinput-huge-file.hpl</filename>\n      <loglevel>Basic</loglevel>\n      <parameters>\n        <pass_all_parameters>Y</pass_all_parameters>\n      </parameters>\n      <params_from_previous>N</params_from_previous>\n      <run_configuration>local</run_configuration>\n      <set_append_logfile>N</set_append_logfile>\n      <set_logfile>N</set_logfile>\n      <wait_until_finished>Y</wait_until_finished>\n      <parallel>N</parallel>\n      <xloc>416</xloc>\n      <yloc>112</yloc>\n      <attributes_hac/>\n    </action>\n  </actions>\n  <hops>\n    <hop>\n      <from>Start</from>\n      <to>textfileoutput-huge-file.hpl</to>\n      <enabled>Y</enabled>\n      <evaluation>Y</evaluation>\n      <unconditional>Y</unconditional>\n    </hop>\n    <hop>\n      <from>textfileoutput-huge-file.hpl</from>\n      <to>csvinput-huge-file.hpl</to>\n      <enabled>Y</enabled>\n      <evaluation>Y</evaluation>\n      <unconditional>N</unconditional>\n    </hop>\n  </hops>\n  <notepads>\n    <notepad>\n      <backgroundcolorblue>251</backgroundcolorblue>\n      <backgroundcolorgreen>232</backgroundcolorgreen>\n      <backgroundcolorred>201</backgroundcolorred>\n      <bordercolorblue>90</bordercolorblue>\n      <bordercolorgreen>58</bordercolorgreen>\n      <bordercolorred>14</bordercolorred>\n      <fontbold>N</fontbold>\n      <fontcolorblue>90</fontcolorblue>\n      <fontcolorgreen>58</fontcolorgreen>\n      <fontcolorred>14</fontcolorred>\n      <fontitalic>N</fontitalic>\n      <fontname>Noto Sans</fontname>\n      <fontsize>10</fontsize>\n      <height>27</height>\n      <xloc>64</xloc>\n      <yloc>48</yloc>\n      <note>Create a 25M rows CSV file and read it back in using the CSV Input parrallel processing. </note>\n      <width>544</width>\n    </notepad>\n  </notepads>\n  <attributes/>\n</workflow>\n", "metadataJson": "{\"server\":[],\"pipeline-probe\":[],\"execution-info-location\":[{\"virtualPath\":null,\"executionInfoLocation\":{\"local-folder\":{\"pluginName\":\"File location\",\"pluginId\":\"local-folder\",\"rootFolder\":\"${HOP_AUDIT_FOLDER}\\/executions\\/\"}},\"dataLoggingDelay\":\"2000\",\"name\":\"local-audit\",\"description\":\"\",\"dataLoggingInterval\":\"5000\"}],\"workflow-log\":[],\"GoogleStorageConnectionDefinition\":[],\"schema-definition\":[],\"cassandra-connection\":[],\"neo4j-graph-model\":[],\"mongodb-connection\":[],\"execution-data-profile\":[{\"virtualPath\":null,\"name\":\"first-last\",\"description\":\"capture the first and last 100 rows of every Hop transform\",\"sampler\":[{\"FirstRowsExecutionDataSampler\":{\"sampleSize\":\"100\"}},{\"LastRowsExecutionDataSampler\":{\"sampleSize\":\"100\"}}]}],\"partition\":[],\"workflow-run-configuration\":[{\"engineRunConfiguration\":{\"Local\":{\"safe_mode\":false,\"transactional\":false}},\"virtualPath\":null,\"defaultSelection\":true,\"name\":\"local\",\"description\":\"\",\"executionInfoLocationName\":\"local-audit\"}],\"restconnection\":[],\"unit-test\":[],\"rdbms\":[],\"AzureConnectionDefinition\":[],\"web-service\":[],\"pipeline-run-configuration\":[{\"engineRunConfiguration\":{\"Local\":{\"feedback_size\":\"50000\",\"sample_size\":\"100\",\"sample_type_in_gui\":\"Last\",\"wait_time\":\"20\",\"rowset_size\":\"10000\",\"safe_mode\":false,\"show_feedback\":false,\"topo_sort\":false,\"gather_metrics\":false,\"transactional\":false}},\"virtualPath\":null,\"defaultSelection\":true,\"configurationVariables\":[],\"name\":\"local\",\"description\":\"\",\"dataProfile\":\"first-last\",\"executionInfoLocationName\":\"local-audit\"}],\"neo4j-connection\":[],\"async-web-service\":[],\"pipeline-log\":[],\"file-definition\":[],\"splunk\":[],\"dataset\":[],\"variable-resolver\":[]}", "runConfigurationName": "local", "logLevel": "BASIC", "parameterValues": {}, "environmentDetails": {"JavaUser": "Yeheng.Hu", "ContainerId": null, "AvailableProcessors": "20", "TotalMemory": "244318208", "JavaVersion": "17.0.14", "HostAddress": "************", "HostName": "CN-D-6L0MKZ3", "MaxMemory": "2147483648", "FreeMemory": "68972296"}, "registrationDate": 1756877474669, "executionStartDate": 1756877474666, "copyNr": null}