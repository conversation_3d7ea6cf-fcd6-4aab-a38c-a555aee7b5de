<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>pipeline-probe-example</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/01/21 10:32:39.015</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/01/21 10:32:39.015</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>Pipeline Data Probe</from>
      <to>book data to fields</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>book data to fields</from>
      <to>get nb_books_in_genre</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>get nb_books_in_genre</from>
      <to>sort pipeline, genre</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>sort pipeline, genre</from>
      <to>${PROJECT_HOME}/books-per-genre/probe-data.csv out</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>${PROJECT_HOME}/books-per-genre/probe-data.csv out</name>
    <type>TextFileOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <separator>;</separator>
    <enclosure>"</enclosure>
    <enclosure_forced>N</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>Y</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding/>
    <endedLine/>
    <fileNameInField>N</fileNameInField>
    <fileNameField/>
    <create_parent_folder>Y</create_parent_folder>
    <file>
      <name>${PROJECT_HOME}/reflection/output/books-per-genre</name>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>Y</do_not_open_new_file_init>
      <extention>csv</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format/>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>N</fast_dump>
      <splitevery/>
    </file>
    <fields>
      <field>
        <name>sourcePipelineName</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>genre</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>nb_books_in_genre</name>
        <type>Integer</type>
        <format>0</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>784</xloc>
      <yloc>272</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Pipeline Data Probe</name>
    <type>PipelineDataProbe</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <log_transforms>Y</log_transforms>
    <attributes/>
    <GUI>
      <xloc>200</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>book data to fields</name>
    <type>Denormaliser</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <field_name>value</field_name>
        <key_value>genre</key_value>
        <target_aggregation_type>-</target_aggregation_type>
        <target_currency_symbol/>
        <target_decimal_symbol/>
        <target_format/>
        <target_grouping_symbol/>
        <target_length>255</target_length>
        <target_name>genre</target_name>
        <target_null_string/>
        <target_precision>-1</target_precision>
        <target_type>String</target_type>
      </field>
      <field>
        <field_name>value</field_name>
        <key_value>author</key_value>
        <target_aggregation_type>-</target_aggregation_type>
        <target_currency_symbol/>
        <target_decimal_symbol/>
        <target_format/>
        <target_grouping_symbol/>
        <target_length>-1</target_length>
        <target_name>author</target_name>
        <target_null_string/>
        <target_precision>-1</target_precision>
        <target_type>String</target_type>
      </field>
      <field>
        <field_name>value</field_name>
        <key_value>title</key_value>
        <target_aggregation_type>-</target_aggregation_type>
        <target_currency_symbol/>
        <target_decimal_symbol/>
        <target_format/>
        <target_grouping_symbol/>
        <target_length>-1</target_length>
        <target_name>title</target_name>
        <target_null_string/>
        <target_precision>-1</target_precision>
        <target_type>String</target_type>
      </field>
    </fields>
    <group>
      <field>
        <name>sourcePipelineName</name>
      </field>
      <field>
        <name>rowNr</name>
      </field>
    </group>
    <key_field>fieldName</key_field>
    <attributes/>
    <GUI>
      <xloc>394</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>get nb_books_in_genre</name>
    <type>MemoryGroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <aggregate>nb_books_in_genre</aggregate>
        <subject>genre</subject>
        <type>COUNT_ALL</type>
      </field>
    </fields>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>sourcePipelineName</name>
      </field>
      <field>
        <name>genre</name>
      </field>
    </group>
    <attributes/>
    <GUI>
      <xloc>588</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>sort pipeline, genre</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>${java.io.tmpdir}</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>sourcePipelineName</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>genre</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>782</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
