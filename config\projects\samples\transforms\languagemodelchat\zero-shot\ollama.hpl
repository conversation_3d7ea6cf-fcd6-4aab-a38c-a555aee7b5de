<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>languagemodelchat-ollama</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <pipeline_status>0</pipeline_status>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/12/18 15:00:31.032</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/12/18 15:00:31.032</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>prompt</from>
      <to>ollama:phi3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>ollama:phi3</from>
      <to>Text output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Text output</name>
    <type>TextFileOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <schema_definition/>
    <separator>;</separator>
    <enclosure>"</enclosure>
    <enclosure_forced>N</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>Y</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding>UTF-8</encoding>
    <endedLine/>
    <fileNameInField>N</fileNameInField>
    <fileNameField/>
    <create_parent_folder>Y</create_parent_folder>
    <file>
      <name>languagemodelchat-ollama-output</name>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>Y</do_not_open_new_file_init>
      <extention>csv</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format/>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>N</fast_dump>
      <splitevery/>
    </file>
    <fields>
      <field>
        <name>prompt</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_identifier</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_model_type</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_model_name</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_finish_reason</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_input_token_count</name>
        <type>Integer</type>
        <format>0</format>
        <currency>£</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_output_token_count</name>
        <type>Integer</type>
        <format>0</format>
        <currency>£</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_total_token_count</name>
        <type>Integer</type>
        <format>0</format>
        <currency>£</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_inference_time</name>
        <type>Integer</type>
        <format>0</format>
        <currency>£</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_output</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>896</xloc>
      <yloc>208</yloc>
    </GUI>
  </transform>
  <transform>
    <name>ollama:phi3</name>
    <type>LanguageModelChat</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <inputField>prompt</inputField>
    <inputChatJson>false</inputChatJson>
    <outputChatJson>false</outputChatJson>
    <mock>false</mock>
    <outputFieldNamePrefix>llm_</outputFieldNamePrefix>
    <modelType>OLLAMA</modelType>
    <parallelism>1</parallelism>
    <openAiUseProxy>false</openAiUseProxy>
    <openAiLogRequests>false</openAiLogRequests>
    <openAiLogResponses>false</openAiLogResponses>
    <huggingFaceReturnFullText>false</huggingFaceReturnFullText>
    <huggingFaceWaitForModel>false</huggingFaceWaitForModel>
    <mistralSafePrompt>false</mistralSafePrompt>
    <mistralLogRequests>false</mistralLogRequests>
    <mistralLogResponses>false</mistralLogResponses>
    <ollamaImageEndpoint>http://localhost:11434</ollamaImageEndpoint>
    <ollamaModelName>phi3</ollamaModelName>
    <ollamaTemperature>0.1</ollamaTemperature>
    <ollamaNumPredict>100</ollamaNumPredict>
    <ollamaTimeout>60</ollamaTimeout>
    <ollamaMaxRetries>3</ollamaMaxRetries>
    <anthropicLogRequests>false</anthropicLogRequests>
    <anthropicLogResponses>false</anthropicLogResponses>
    <attributes/>
    <GUI>
      <xloc>736</xloc>
      <yloc>208</yloc>
    </GUI>
  </transform>
  <transform>
    <name>prompt</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>In one sentence, explain with the term 'digital native' in the context of job descriptions is considered age biased.</item>
      </line>
      <line>
        <item>In one sentence, explain with the term 'geezer' and it is considered age biased.</item>
      </line>
      <line>
        <item>In one sentence, explain with the term 'native' in the context of job descriptions and languages can be considered racial biased.</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>prompt</name>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>544</xloc>
      <yloc>208</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
