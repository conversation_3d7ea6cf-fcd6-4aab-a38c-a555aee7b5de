<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
<pipeline>
  <info>
    <name>javascript-sum-over-all-rows</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2022/11/21 15:10:05.076</created_date>
    <modified_user>-</modified_user>
    <modified_date>2022/11/21 15:10:05.076</modified_date>
  </info>
  <notepads>
    <notepad>
      <backgroundcolorblue>210</backgroundcolorblue>
      <backgroundcolorgreen>136</backgroundcolorgreen>
      <backgroundcolorred>15</backgroundcolorred>
      <bordercolorblue>250</bordercolorblue>
      <bordercolorgreen>231</bordercolorgreen>
      <bordercolorred>200</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>250</fontcolorblue>
      <fontcolorgreen>231</fontcolorgreen>
      <fontcolorred>200</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Noto Sans</fontname>
      <fontsize>12</fontsize>
      <height>100</height>
      <xloc>64</xloc>
      <yloc>160</yloc>
      <note>This pipeline uses JavaScript to calculate the sum of all values.
3 types of scripts are used: Start, End and Transform.
- Start: set the sum to 0 and decide not to output rows
- Transform: add to the sum
- End: output the sum in a single row</note>
      <width>367</width>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Data grid</from>
      <to>JavaScript</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Data grid</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>1</item>
      </line>
      <line>
        <item>2</item>
      </line>
      <line>
        <item>3</item>
      </line>
      <line>
        <item>4</item>
      </line>
      <line>
        <item>5</item>
      </line>
      <line>
        <item>6</item>
      </line>
      <line>
        <item>7</item>
      </line>
      <line>
        <item>8</item>
      </line>
      <line>
        <item>9</item>
      </line>
      <line>
        <item>10</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>value</name>
        <type>Integer</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>96</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>JavaScript</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>AddToSum</jsScript_name>
        <jsScript_script>
sum+=value;

</jsScript_script>
      </jsScript>
      <jsScript>
        <jsScript_type>1</jsScript_type>
        <jsScript_name>StartScript</jsScript_name>
        <jsScript_script>
var sum = 0;
var pipeline_Status = SKIP_PIPELINE;

</jsScript_script>
      </jsScript>
      <jsScript>
        <jsScript_type>2</jsScript_type>
        <jsScript_name>EndScript</jsScript_name>
        <jsScript_script>
var outputRow = createRowCopy(getOutputRowMeta().size());
outputRow[1] = java.lang.Long.valueOf(""+sum);
putRow(outputRow);


</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>sum</name>
        <rename>sum</rename>
        <type>Integer</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>304</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
