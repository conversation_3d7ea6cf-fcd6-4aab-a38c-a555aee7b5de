<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at
      http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
<pipeline>
  <info>
    <name>fake-data-generate-person-record</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/04/28 15:29:02.042</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/04/28 15:29:02.042</modified_date>
  </info>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Segoe UI</fontname>
      <fontsize>11</fontsize>
      <height>41</height>
      <xloc>80</xloc>
      <yloc>48</yloc>
      <note>Fake Data transform generates data based on categories
</note>
      <width>305</width>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Generate 1000 rows</from>
      <to>Generate random person record</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Generate random person record</from>
      <to>Results</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Generate 1000 rows</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
</fields>
    <interval_in_ms>5000</interval_in_ms>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <limit>1000</limit>
    <never_ending>N</never_ending>
    <row_time_field>now</row_time_field>
    <attributes/>
    <GUI>
      <xloc>96</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Generate random person record</name>
    <type>Fake</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>fullName</name>
        <topic>fullName</topic>
        <type>Name</type>
      </field>
      <field>
        <name>Birthday</name>
        <topic>birthday</topic>
        <type>DateAndTime</type>
      </field>
      <field>
        <name>bloodGroup</name>
        <topic>bloodGroup</topic>
        <type>Name</type>
      </field>
      <field>
        <name>creditCardNumber</name>
        <topic>creditCardNumber</topic>
        <type>Business</type>
      </field>
      <field>
        <name>cellNumber</name>
        <topic>cellPhone</topic>
        <type>PhoneNumber</type>
      </field>
      <field>
        <name>favouriteBook</name>
        <topic>title</topic>
        <type>Book</type>
      </field>
      <field>
        <name>workplace</name>
        <topic>name</topic>
        <type>Company</type>
      </field>
      <field>
        <name>programmingLanguage</name>
        <topic>name</topic>
        <type>ProgrammingLanguage</type>
      </field>
      <field>
        <name>Digit</name>
        <topic>digit</topic>
        <type>Number</type>
      </field>
      <field>
        <name>Number</name>
        <topic>randomNumber</topic>
        <type>Number</type>
      </field>
    </fields>
    <locale>en</locale>
    <attributes/>
    <GUI>
      <xloc>288</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Results</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>480</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
