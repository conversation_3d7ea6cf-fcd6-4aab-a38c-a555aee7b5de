<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>add-value-fields-changing-sequence</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/02/05 14:14:43.052</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/02/05 14:14:43.052</modified_date>
  </info>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Noto Sans</fontname>
      <fontsize>10</fontsize>
      <height>27</height>
      <xloc>496</xloc>
      <yloc>32</yloc>
      <note>reset change_counter for each multiple of 10</note>
      <width>259</width>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>generate 100 rows</from>
      <to>add counter</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>add counter</from>
      <to>counter/10</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>counter/10</from>
      <to>add change_counter</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>add change_counter</name>
    <type>FieldsChangeSequence</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>divider</name>
      </field>
    </fields>
    <increment>1</increment>
    <resultfieldName>change_counter</resultfieldName>
    <start>1</start>
    <attributes/>
    <GUI>
      <xloc>560</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>add counter</name>
    <type>Sequence</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>hop-samples</connection>
    <increment_by>1</increment_by>
    <max_value>999999999</max_value>
    <seqname>SEQ_</seqname>
    <start_at>1</start_at>
    <use_counter>Y</use_counter>
    <use_database>N</use_database>
    <valuename>counter</valuename>
    <attributes/>
    <GUI>
      <xloc>224</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>counter/10</name>
    <type>Calculator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <calculation>
      <calc_type>CONSTANT</calc_type>
      <field_a>10.0</field_a>
      <field_name>ten</field_name>
      <remove>Y</remove>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <value_type>Integer</value_type>
    </calculation>
    <calculation>
      <calc_type>DIVIDE</calc_type>
      <field_a>counter</field_a>
      <field_b>ten</field_b>
      <field_name>divider</field_name>
      <remove>N</remove>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <value_type>Integer</value_type>
    </calculation>
    <failIfNoFile>Y</failIfNoFile>
    <attributes/>
    <GUI>
      <xloc>368</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>generate 100 rows</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
</fields>
    <interval_in_ms>5000</interval_in_ms>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <limit>100</limit>
    <never_ending>N</never_ending>
    <row_time_field>now</row_time_field>
    <attributes/>
    <GUI>
      <xloc>80</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
