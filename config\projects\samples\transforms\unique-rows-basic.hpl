<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>unique-rows-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/04/30 15:29:16.347</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/04/30 15:29:16.347</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Obtain unique values from a data set with duplicate values.</note>
      <xloc>96</xloc>
      <yloc>48</yloc>
      <width>314</width>
      <heigth>26</heigth>
      <fontname>Segoe UI</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Sort by value</from>
      <to>Unique by value</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Test data</from>
      <to>Sort by value</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Test data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>Title</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>value</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <data>
      <line>
        <item>One</item>
        <item>1</item>
      </line>
      <line>
        <item>Two</item>
        <item>2</item>
      </line>
      <line>
        <item>Three</item>
        <item>3</item>
      </line>
      <line>
        <item>Four</item>
        <item>4</item>
      </line>
      <line>
        <item>Two</item>
        <item>2</item>
      </line>
      <line>
        <item>Three</item>
        <item>3</item>
      </line>
      <line>
        <item>Four</item>
        <item>4</item>
      </line>
      <line>
        <item>Six</item>
        <item>6</item>
      </line>
      <line>
        <item>One</item>
        <item>1</item>
      </line>
      <line>
        <item>Seven</item>
        <item>7</item>
      </line>
      <line>
        <item>Six</item>
        <item>6</item>
      </line>
      <line>
        <item>Three</item>
        <item>3</item>
      </line>
      <line>
        <item>Four</item>
        <item>4</item>
      </line>
      <line>
        <item>Seven</item>
        <item>7</item>
      </line>
      <line>
        <item>Two</item>
        <item>2</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>96</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Sort by value</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>${java.io.tmpdir}</directory>
    <prefix>out</prefix>
    <sort_size>5000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>value</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>208</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Unique by value</name>
    <type>Unique</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <count_rows>Y</count_rows>
    <count_field>Distinct_Count</count_field>
    <reject_duplicate_row>N</reject_duplicate_row>
    <error_description/>
    <fields>
      <field>
        <name>value</name>
        <case_insensitive>N</case_insensitive>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>336</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
