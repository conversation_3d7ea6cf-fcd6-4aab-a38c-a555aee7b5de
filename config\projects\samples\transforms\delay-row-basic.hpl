<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>delay-row-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/04/27 10:21:33.291</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/04/27 10:21:33.291</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Delay Row

Pauze for the specified time whenever a new row is received.
When you run this example you'll see a log line appear every 5s.

You can use this to slow down output for example to prevent 
an overload on certain services like mail servers, REST services and so on. </note>
      <xloc>96</xloc>
      <yloc>32</yloc>
      <width>381</width>
      <heigth>120</heigth>
      <fontname>Segoe UI</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>10 empty rows</from>
      <to>Wait 5s</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Wait 5s</from>
      <to>Log every 5s</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Wait 5s</name>
    <type>Delay</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <timeout>5</timeout>
    <scaletime>seconds</scaletime>
    <attributes/>
    <GUI>
      <xloc>256</xloc>
      <yloc>176</yloc>
    </GUI>
  </transform>
  <transform>
    <name>10 empty rows</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
</fields>
    <interval_in_ms>5000</interval_in_ms>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <never_ending>N</never_ending>
    <limit>10</limit>
    <row_time_field>now</row_time_field>
    <attributes/>
    <GUI>
      <xloc>128</xloc>
      <yloc>176</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Log every 5s</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>N</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage>A log line</logmessage>
    <fields>
      </fields>
    <attributes/>
    <GUI>
      <xloc>416</xloc>
      <yloc>176</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
