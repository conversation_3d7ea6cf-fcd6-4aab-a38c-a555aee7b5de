# Apache Hop 测试项目

这个项目演示了如何使用Apache Hop创建管道和工作流进行数据映射测试。

## 项目结构

```
test-project/
├── project-config.json          # 项目配置文件
├── datasets/                    # 测试数据集
│   ├── source_table.csv        # 源数据表
│   └── department_table.csv    # 部门数据表
├── pipelines/                  # 管道文件
│   └── data_mapping_pipeline.hpl # 数据映射管道
├── workflows/                  # 工作流文件
│   └── data_processing_workflow.hwf # 数据处理工作流
└── metadata/                   # 元数据目录
    ├── execution-data-profile/
    ├── execution-info-location/
    ├── pipeline-run-configuration/
    └── workflow-run-configuration/
```

## 数据映射管道说明

### 管道功能
`data_mapping_pipeline.hpl` 管道执行以下操作：

1. **CSV文件输入**: 读取 `source_table.csv` 文件
2. **字段选择和重命名**: 将字段映射为新的名称
   - `id` → `employee_id`
   - `name` → `employee_name`
   - `age` → `employee_age`
   - `city` → `work_city`
   - `salary` → `monthly_salary`
3. **文本文件输出**: 将映射后的数据输出到 `mapped_output.csv`

### 工作流功能
`data_processing_workflow.hwf` 工作流执行以下步骤：

1. **检查输入文件**: 验证源数据文件是否存在
2. **执行数据映射管道**: 运行数据映射管道
3. **验证输出文件**: 检查输出文件是否成功生成
4. **成功完成**: 标记工作流完成

## 在Hop GUI中使用

### 1. 添加项目到Hop
1. 打开Hop GUI
2. 点击 "File" → "Project" → "Add Project"
3. 设置项目名称为 "test-project"
4. 设置项目主目录为当前的 `test-project` 文件夹路径
5. 点击 "OK" 保存

### 2. 打开管道
1. 在项目浏览器中导航到 `pipelines` 文件夹
2. 双击 `data_mapping_pipeline.hpl` 打开管道
3. 查看管道的数据流：CSV输入 → 字段选择 → 文本输出

### 3. 运行管道
1. 点击工具栏中的运行按钮（绿色播放图标）
2. 选择运行配置（使用默认配置）
3. 点击 "Launch" 开始执行
4. 查看执行日志和结果

### 4. 打开工作流
1. 在项目浏览器中导航到 `workflows` 文件夹
2. 双击 `data_processing_workflow.hwf` 打开工作流
3. 查看工作流的执行步骤

### 5. 运行工作流
1. 点击工具栏中的运行按钮
2. 选择运行配置
3. 点击 "Launch" 开始执行
4. 观察每个步骤的执行状态

## 预期结果

执行成功后，您应该在 `datasets` 文件夹中看到一个新的 `mapped_output.csv` 文件，其中包含重命名后的字段和相同的数据。

## 扩展练习

1. **添加数据转换**: 在管道中添加计算字段、过滤器等转换
2. **连接多个表**: 使用 `department_table.csv` 进行表连接操作
3. **添加错误处理**: 在工作流中添加错误处理步骤
4. **数据验证**: 添加数据质量检查步骤

这个项目为您提供了一个完整的Hop数据处理示例，您可以基于此进行更复杂的数据集成和ETL操作。
