<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
<pipeline>
  <info>
    <name>javascript-create-xml-with-e4x</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2022/06/28 11:04:25.576</created_date>
    <modified_user>-</modified_user>
    <modified_date>2022/06/28 11:04:25.576</modified_date>
    <key_for_session_key/>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>The E4X transform creates a simple XML document for every input row.
It is using EMCAScript for XML: E4X

https://www.ecma-international.org/publications-and-standards/standards/ecma-357/
</note>
      <xloc>128</xloc>
      <yloc>224</yloc>
      <width>518</width>
      <heigth>98</heigth>
      <fontname>Noto Sans</fontname>
      <fontsize>12</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>200</fontcolorred>
      <fontcolorgreen>231</fontcolorgreen>
      <fontcolorblue>250</fontcolorblue>
      <backgroundcolorred>15</backgroundcolorred>
      <backgroundcolorgreen>136</backgroundcolorgreen>
      <backgroundcolorblue>210</backgroundcolorblue>
      <bordercolorred>200</bordercolorred>
      <bordercolorgreen>231</bordercolorgreen>
      <bordercolorblue>250</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Data grid</from>
      <to>E4X</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Data grid</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>id</name>
        <precision>-1</precision>
        <type>Integer</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>firstName</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>lastName</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>age</name>
        <precision>-1</precision>
        <type>Integer</type>
      </field>
    </fields>
    <data>
      <line>
        <item>1</item>
        <item>Apache</item>
        <item>Hop</item>
        <item>3</item>
      </line>
      <line>
        <item>2</item>
        <item>Apache</item>
        <item>Beam</item>
        <item>8</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>144</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform>
    <name>E4X</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>process</jsScript_name>
        <jsScript_script>
var xml = &lt;person/>;

xml.id = id;
xml.fn = firstName;
xml.ln = lastName;
xml.age = age;

var xmlString = xml.toXMLString();
</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>xmlString</name>
        <rename>xmlString</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>272</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
