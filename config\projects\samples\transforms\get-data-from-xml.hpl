<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>get-data-from-xml</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/07/17 19:26:28.962</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/07/17 19:26:28.962</modified_date>
  </info>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Noto Sans</fontname>
      <fontsize>10</fontsize>
      <height>27</height>
      <xloc>144</xloc>
      <yloc>96</yloc>
      <note>Sample XML taken from https://www.javatpoint.com/xml-example</note>
      <width>371</width>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>sample xml</from>
      <to>Get data from XML</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Get data from XML</from>
      <to>clean</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>clean</from>
      <to>preview</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Get data from XML</name>
    <type>getXMLData</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <include>N</include>
    <include_field/>
    <rownum>N</rownum>
    <addresultfile>N</addresultfile>
    <namespaceaware>N</namespaceaware>
    <ignorecomments>N</ignorecomments>
    <readurl>N</readurl>
    <validating>N</validating>
    <usetoken>N</usetoken>
    <IsIgnoreEmptyFile>N</IsIgnoreEmptyFile>
    <doNotFailIfNoFile>Y</doNotFailIfNoFile>
    <rownum_field/>
    <encoding>UTF-8</encoding>
    <file>
      <name>${Internal.Pipeline.Filename.Name}</name>
      <filemask/>
      <exclude_filemask/>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
    </file>
    <fields>
      <field>
        <name>category</name>
        <xpath>@category</xpath>
        <element_type>attribut</element_type>
        <result_type>valueof</result_type>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>language</name>
        <xpath>title/@lang</xpath>
        <element_type>attribut</element_type>
        <result_type>valueof</result_type>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>title</name>
        <xpath>title</xpath>
        <element_type>node</element_type>
        <result_type>valueof</result_type>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>author</name>
        <xpath>author</xpath>
        <element_type>node</element_type>
        <result_type>valueof</result_type>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>year</name>
        <xpath>year</xpath>
        <element_type>node</element_type>
        <result_type>valueof</result_type>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>price</name>
        <xpath>price</xpath>
        <element_type>node</element_type>
        <result_type>valueof</result_type>
        <type>Number</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>element</name>
        <xpath>.</xpath>
        <element_type>node</element_type>
        <result_type>singlenode</result_type>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
    </fields>
    <limit>0</limit>
    <loopxpath>/bookstore/book</loopxpath>
    <IsInFields>Y</IsInFields>
    <IsAFile>N</IsAFile>
    <XmlField>xml</XmlField>
    <prunePath/>
    <shortFileFieldName/>
    <pathFieldName/>
    <hiddenFieldName/>
    <lastModificationTimeFieldName/>
    <uriNameFieldName/>
    <rootUriNameFieldName/>
    <extensionFieldName/>
    <sizeFieldName/>
    <attributes/>
    <GUI>
      <xloc>304</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>sample xml</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>&lt;bookstore>  
  &lt;book category="COOKING">  
    &lt;title lang="en">Everyday Italian&lt;/title>  
    &lt;author>Giada De Laurentiis&lt;/author>  
    &lt;year>2005&lt;/year>  
    &lt;price>30.00&lt;/price>  
  &lt;/book>  
  &lt;book category="CHILDREN">  
    &lt;title lang="en">Harry Potter&lt;/title>  
    &lt;author>J K. Rowling&lt;/author>  
    &lt;year>2005&lt;/year>  
    &lt;price>29.99&lt;/price>  
  &lt;/book>  
  &lt;book category="WEB">  
    &lt;title lang="en">Learning XML&lt;/title>  
    &lt;author>Erik T. Ray&lt;/author>  
    &lt;year>2003&lt;/year>  
    &lt;price>39.95&lt;/price>  
  &lt;/book>  
&lt;/bookstore></item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>xml</name>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>144</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>clean</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>category</name>
        <rename/>
      </field>
      <field>
        <name>language</name>
        <rename/>
      </field>
      <field>
        <name>title</name>
        <rename/>
      </field>
      <field>
        <name>author</name>
        <rename/>
      </field>
      <field>
        <name>year</name>
        <rename/>
      </field>
      <field>
        <name>price</name>
        <rename/>
      </field>
      <field>
        <name>element</name>
        <rename/>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <GUI>
      <xloc>480</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>preview</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>640</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
