<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>standardizephonenumber</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/10/08 20:16:07.061</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/10/08 20:16:07.061</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>Data grid</from>
      <to>Standardize phone number</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Data grid</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>Input</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>Country</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
    </fields>
    <data>
      <line>
        <item>+32495400200</item>
        <item>FR</item>
      </line>
      <line>
        <item>0032495400200</item>
        <item>BE</item>
      </line>
      <line>
        <item>0499500158</item>
        <item>BE</item>
      </line>
      <line>
        <item>0499500158</item>
        <item/>
      </line>
      <line>
        <item>0388902406</item>
        <item>FR</item>
      </line>
      <line>
        <item>888.555.1212 x123</item>
        <item>US</item>
      </line>
      <line>
        <item>999</item>
        <item>FR</item>
      </line>
      <line>
        <item/>
        <item>FR</item>
      </line>
      <line>
        <item>(908)123-4567</item>
        <item>US</item>
      </line>
      <line>
        <item>#(*************</item>
        <item/>
      </line>
      <line>
        <item>Direct: (*************</item>
        <item>US</item>
      </line>
      <line>
        <item>************</item>
        <item/>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>112</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Standardize phone number</name>
    <type>StandardizePhoneNumber</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <country>Country</country>
        <defaultCountry>BE</defaultCountry>
        <input>Input</input>
        <format>RFC3966</format>
        <output>RFC3966</output>
      </field>
      <field>
        <country>Country</country>
        <defaultCountry>BE</defaultCountry>
        <input>Input</input>
        <format>INTERNATIONAL</format>
        <output>INTERNATIONAL_BE</output>
      </field>
      <field>
        <country>Country</country>
        <defaultCountry>FR</defaultCountry>
        <input>Input</input>
        <format>INTERNATIONAL</format>
        <output>INTERNATIONAL_FR</output>
      </field>
      <field>
        <country>Country</country>
        <defaultCountry>US</defaultCountry>
        <input>Input</input>
        <format>INTERNATIONAL</format>
        <output>INTERNATIONAL_US</output>
      </field>
      <field>
        <country>Country</country>
        <defaultCountry>FR</defaultCountry>
        <input>Input</input>
        <format>E164</format>
        <output>E164</output>
      </field>
      <field>
        <country>Country</country>
        <defaultCountry>FR</defaultCountry>
        <input>Input</input>
        <isvalidnumber>ISVALID</isvalidnumber>
        <format>NATIONAL</format>
        <numbertype>TYPE</numbertype>
        <output>NATIONAL</output>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>272</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
