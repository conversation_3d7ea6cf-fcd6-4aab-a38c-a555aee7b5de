<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>mapping-use</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2020/09/09 16:18:45.749</created_date>
    <modified_user>-</modified_user>
    <modified_date>2020/09/09 16:18:45.749</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>Data grid</from>
      <to>id</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Simple Mapping</from>
      <to>Results</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>id</from>
      <to>Simple Mapping</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Data grid</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>first</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>last</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <data>
      <line>
        <item>Johnny</item>
        <item>Walker</item>
      </line>
      <line>
        <item>Captain</item>
        <item>Morgan</item>
      </line>
      <line>
        <item>Jim</item>
        <item>Bean</item>
      </line>
      <line>
        <item>Evan</item>
        <item>Williams</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>160</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Results</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>608</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Simple Mapping</name>
    <type>SimpleMapping</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <filename>${PROJECT_HOME}/transforms/simple-mapping-child.hpl</filename>
    <mappings>
      <input>
        <mapping>
          <input_step/>
          <output_step/>
          <main_path>Y</main_path>
          <rename_on_output>Y</rename_on_output>
          <description/>
          <connector>
            <parent>first</parent>
            <child>firstName</child>
          </connector>
          <connector>
            <parent>last</parent>
            <child>lastName</child>
          </connector>
        </mapping>
      </input>
      <output>
        <mapping>
          <input_step/>
          <output_step/>
          <main_path>Y</main_path>
          <rename_on_output>N</rename_on_output>
          <description/>
          <connector>
            <parent>name</parent>
            <child>resultName</child>
          </connector>
        </mapping>
      </output>
      <parameters>
        <inherit_all_vars>Y</inherit_all_vars>
      </parameters>
    </mappings>
    <attributes/>
    <GUI>
      <xloc>432</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>id</name>
    <type>Sequence</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <valuename>id</valuename>
    <use_database>N</use_database>
    <connection/>
    <schema/>
    <seqname>SEQ_</seqname>
    <use_counter>Y</use_counter>
    <counter_name/>
    <start_at>1</start_at>
    <increment_by>1</increment_by>
    <max_value>999999999</max_value>
    <attributes/>
    <GUI>
      <xloc>288</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
