<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<workflow>
  <name>parallel-workflow</name>
  <name_sync_with_filename>Y</name_sync_with_filename>
  <description/>
  <extended_description/>
  <workflow_version/>
  <created_user>-</created_user>
  <created_date>2023/05/01 09:10:07.560</created_date>
  <modified_user>-</modified_user>
  <modified_date>2023/05/01 09:10:07.560</modified_date>
  <parameters>
    </parameters>
  <actions>
    <action>
      <name>Start</name>
      <description/>
      <type>SPECIAL</type>
      <attributes/>
      <DayOfMonth>1</DayOfMonth>
      <hour>12</hour>
      <intervalMinutes>60</intervalMinutes>
      <intervalSeconds>0</intervalSeconds>
      <minutes>0</minutes>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <weekDay>1</weekDay>
      <parallel>Y</parallel>
      <xloc>48</xloc>
      <yloc>96</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>sample-pipeline.hpl 1</name>
      <description/>
      <type>PIPELINE</type>
      <attributes/>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <clear_files>N</clear_files>
      <clear_rows>N</clear_rows>
      <create_parent_folder>N</create_parent_folder>
      <exec_per_row>N</exec_per_row>
      <filename>${PROJECT_HOME}/workflows/parallel/sample-pipeline.hpl</filename>
      <logext/>
      <logfile/>
      <loglevel>Basic</loglevel>
      <parameters>
        <pass_all_parameters>Y</pass_all_parameters>
      </parameters>
      <params_from_previous>N</params_from_previous>
      <run_configuration>local</run_configuration>
      <set_append_logfile>N</set_append_logfile>
      <set_logfile>N</set_logfile>
      <wait_until_finished>Y</wait_until_finished>
      <parallel>N</parallel>
      <xloc>256</xloc>
      <yloc>48</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>sample-pipeline.hpl 2</name>
      <description/>
      <type>PIPELINE</type>
      <attributes/>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <clear_files>N</clear_files>
      <clear_rows>N</clear_rows>
      <create_parent_folder>N</create_parent_folder>
      <exec_per_row>N</exec_per_row>
      <filename>${PROJECT_HOME}/workflows/parallel/sample-pipeline.hpl</filename>
      <logext/>
      <logfile/>
      <loglevel>Basic</loglevel>
      <parameters>
        <pass_all_parameters>Y</pass_all_parameters>
      </parameters>
      <params_from_previous>N</params_from_previous>
      <run_configuration>local</run_configuration>
      <set_append_logfile>N</set_append_logfile>
      <set_logfile>N</set_logfile>
      <wait_until_finished>Y</wait_until_finished>
      <parallel>N</parallel>
      <xloc>256</xloc>
      <yloc>160</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>Write to log 1</name>
      <description/>
      <type>WRITE_TO_LOG</type>
      <attributes/>
      <logmessage/>
      <loglevel>Basic</loglevel>
      <logsubject/>
      <parallel>N</parallel>
      <xloc>464</xloc>
      <yloc>48</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>Write to log 2</name>
      <description/>
      <type>WRITE_TO_LOG</type>
      <attributes/>
      <logmessage/>
      <loglevel>Basic</loglevel>
      <logsubject/>
      <parallel>N</parallel>
      <xloc>464</xloc>
      <yloc>160</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>Dummy 1</name>
      <description/>
      <type>DUMMY</type>
      <attributes/>
      <parallel>N</parallel>
      <xloc>640</xloc>
      <yloc>48</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>Dummy 2</name>
      <description/>
      <type>DUMMY</type>
      <attributes/>
      <parallel>N</parallel>
      <xloc>640</xloc>
      <yloc>160</yloc>
      <attributes_hac/>
    </action>
  </actions>
  <hops>
    <hop>
      <from>Start</from>
      <to>sample-pipeline.hpl 1</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>Y</unconditional>
    </hop>
    <hop>
      <from>Start</from>
      <to>sample-pipeline.hpl 2</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>Y</unconditional>
    </hop>
    <hop>
      <from>sample-pipeline.hpl 1</from>
      <to>Write to log 1</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
    <hop>
      <from>sample-pipeline.hpl 2</from>
      <to>Write to log 2</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
    <hop>
      <from>Write to log 1</from>
      <to>Dummy 1</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
    <hop>
      <from>Write to log 2</from>
      <to>Dummy 2</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
  </hops>
  <notepads>
  </notepads>
  <attributes/>
</workflow>
