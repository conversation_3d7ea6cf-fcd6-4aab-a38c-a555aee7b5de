<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>databaselookup</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/07/19 20:04:53.662</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/07/19 20:04:53.662</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Looks up descriptions for an id in a database table </note>
      <xloc>128</xloc>
      <yloc>112</yloc>
      <width>291</width>
      <heigth>27</heigth>
      <fontname>Noto Sans</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Test Data</from>
      <to>Database lookup</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Database lookup</from>
      <to>Output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Database lookup</name>
    <type>DBLookup</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <cache_size>0</cache_size>
    <cache>N</cache>
    <connection>hop-samples</connection>
    <cache_load_all>N</cache_load_all>
    <lookup>
      <eat_row_on_failure>N</eat_row_on_failure>
      <fail_on_multiple>N</fail_on_multiple>
      <key>
        <condition>=</condition>
        <name>id</name>
        <field>id</field>
      </key>
      <value>
        <type>String</type>
        <rename>description</rename>
        <name>DESCRIPTION</name>
        <trim_type>both</trim_type>
      </value>
      <table>DBLOOKUP</table>
    </lookup>
    <attributes/>
    <GUI>
      <xloc>368</xloc>
      <yloc>176</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>592</xloc>
      <yloc>176</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Test Data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>id</name>
        <precision>-1</precision>
        <type>Integer</type>
      </field>
    </fields>
    <data>
      <line>
        <item>1</item>
      </line>
      <line>
        <item>2</item>
      </line>
      <line>
        <item>3</item>
      </line>
      <line>
        <item>4</item>
      </line>
      <line>
        <item>5</item>
      </line>
      <line>
        <item>6</item>
      </line>
      <line>
        <item>7</item>
      </line>
      <line>
        <item>8</item>
      </line>
      <line>
        <item>9</item>
      </line>
      <line>
        <item>10</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>144</xloc>
      <yloc>176</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
