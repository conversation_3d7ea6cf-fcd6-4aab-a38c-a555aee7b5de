<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>add-constants</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/02/05 11:09:40.926</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/02/05 11:09:40.926</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>generate 1 row</from>
      <to>add constants</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>add constants</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>const_big_number</name>
        <nullif>546435.3453453</nullif>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <type>BigNumber</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>const_boolean</name>
        <nullif>Y</nullif>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <type>Boolean</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format>yyyy-MM-dd</format>
        <group/>
        <length>-1</length>
        <name>const_date</name>
        <nullif>2023-02-05</nullif>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <type>Date</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>const_integer</name>
        <nullif>34</nullif>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <type>Integer</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>const_number</name>
        <nullif>45.67</nullif>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <type>Number</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>const_string</name>
        <nullif>abcdefgh</nullif>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <type>String</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>const_timestamp</name>
        <nullif>2023-02-05 11:10:34.456456</nullif>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <type>Timestamp</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>304</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>generate 1 row</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
</fields>
    <interval_in_ms>5000</interval_in_ms>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <limit>1</limit>
    <never_ending>N</never_ending>
    <row_time_field>now</row_time_field>
    <attributes/>
    <GUI>
      <xloc>128</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
