<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>split-fields-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/04/30 16:35:28.614</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/04/30 16:35:28.614</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Split Delimited rows from input dataset</note>
      <xloc>80</xloc>
      <yloc>48</yloc>
      <width>211</width>
      <heigth>26</heigth>
      <fontname>Segoe UI</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Test data</from>
      <to>Split record into fields</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Split record into fields</from>
      <to>Output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Test data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>id</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>record</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <data>
      <line>
        <item>1</item>
        <item>Harry Whittaker,2018,McCarren House,312,3.75</item>
      </line>
      <line>
        <item>2</item>
        <item>Gary Johnson,2017,Cushing House,148,3.52</item>
      </line>
      <line>
        <item>3</item>
        <item>Jack Daniels,2018,Prescott House,17-D,3.20</item>
      </line>
      <line>
        <item>4</item>
        <item>Michael Chor,2019,Oliver House,108,3.48</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>96</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>480</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Split record into fields</name>
    <type>FieldSplitter</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <splitfield>record</splitfield>
    <delimiter>,</delimiter>
    <enclosure/>
    <escape_string/>
    <fields>
      <field>
        <name>Name</name>
        <id/>
        <idrem>N</idrem>
        <type>String</type>
        <format/>
        <group/>
        <decimal/>
        <currency/>
        <length>-1</length>
        <precision>-1</precision>
        <nullif/>
        <ifnull/>
        <trimtype>none</trimtype>
      </field>
      <field>
        <name>Class</name>
        <id/>
        <idrem>N</idrem>
        <type>Integer</type>
        <format/>
        <group/>
        <decimal/>
        <currency/>
        <length>-1</length>
        <precision>-1</precision>
        <nullif/>
        <ifnull/>
        <trimtype>none</trimtype>
      </field>
      <field>
        <name>Dorm</name>
        <id/>
        <idrem>N</idrem>
        <type>String</type>
        <format/>
        <group/>
        <decimal/>
        <currency/>
        <length>-1</length>
        <precision>-1</precision>
        <nullif/>
        <ifnull/>
        <trimtype>none</trimtype>
      </field>
      <field>
        <name>Room</name>
        <id/>
        <idrem>N</idrem>
        <type>String</type>
        <format/>
        <group/>
        <decimal/>
        <currency/>
        <length>-1</length>
        <precision>-1</precision>
        <nullif/>
        <ifnull/>
        <trimtype>none</trimtype>
      </field>
      <field>
        <name>GPA</name>
        <id/>
        <idrem>N</idrem>
        <type>Number</type>
        <format/>
        <group/>
        <decimal/>
        <currency/>
        <length>-1</length>
        <precision>-1</precision>
        <nullif/>
        <ifnull/>
        <trimtype>none</trimtype>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>288</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
