<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>switch-case</name>
    <description />
    <extended_description />
    <pipeline_version />
    <pipeline_type>Normal</pipeline_type>
    <pipeline_status>0</pipeline_status>
    <directory>/</directory>
    <parameters>
    </parameters>
    <maxdate>
      <connection />
      <table />
      <field />
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file />
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2018/11/29 13:30:22.901</created_date>
    <modified_user>-</modified_user>
    <modified_date>2018/11/29 13:30:22.901</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>Collect</from>
      <to>switch-case</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Customers</from>
      <to>Switch / case</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch / case</from>
      <to>CA</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch / case</from>
      <to>FL</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch / case</from>
      <to>NY</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch / case</from>
      <to>Default</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>CA</from>
      <to>Collect</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>FL</from>
      <to>Collect</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>NY</from>
      <to>Collect</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Default</from>
      <to>Collect</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>CA</name>
    <type>Constant</type>
    <description />
    <distribute>Y</distribute>
    <custom_distribution />
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name />
    </partitioning>
    <fields>
      <field>
        <name>Comment</name>
        <type>String</type>
        <format />
        <currency />
        <decimal />
        <group />
        <nullif>Some comment about California</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <cluster_schema />
    <GUI>
      <xloc>544</xloc>
      <yloc>80</yloc>
      <draw>Y</draw>
    </GUI>
  </transform>
  <transform>
    <name>Collect</name>
    <type>Dummy</type>
    <description />
    <distribute>Y</distribute>
    <custom_distribution />
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name />
    </partitioning>
    <cluster_schema />
    <GUI>
      <xloc>720</xloc>
      <yloc>176</yloc>
      <draw>Y</draw>
    </GUI>
  </transform>
  <transform>
    <name>Customers</name>
    <type>BeamInput</type>
    <description />
    <distribute>Y</distribute>
    <custom_distribution />
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name />
    </partitioning>
    <input_location>${DATA_INPUT}</input_location>
    <file_description_name>Customers</file_description_name>
    <cluster_schema />
    <GUI>
      <xloc>224</xloc>
      <yloc>176</yloc>
      <draw>Y</draw>
    </GUI>
  </transform>
  <transform>
    <name>Default</name>
    <type>Constant</type>
    <description />
    <distribute>Y</distribute>
    <custom_distribution />
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name />
    </partitioning>
    <fields>
      <field>
        <name>Comment</name>
        <type>String</type>
        <format />
        <currency />
        <decimal />
        <group />
        <nullif>no comment.</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <cluster_schema />
    <GUI>
      <xloc>544</xloc>
      <yloc>272</yloc>
      <draw>Y</draw>
    </GUI>
  </transform>
  <transform>
    <name>FL</name>
    <type>Constant</type>
    <description />
    <distribute>Y</distribute>
    <custom_distribution />
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name />
    </partitioning>
    <fields>
      <field>
        <name>Comment</name>
        <type>String</type>
        <format />
        <currency />
        <decimal />
        <group />
        <nullif>Some remark on Floridians</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <cluster_schema />
    <GUI>
      <xloc>544</xloc>
      <yloc>144</yloc>
      <draw>Y</draw>
    </GUI>
  </transform>
  <transform>
    <name>NY</name>
    <type>Constant</type>
    <description />
    <distribute>Y</distribute>
    <custom_distribution />
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name />
    </partitioning>
    <fields>
      <field>
        <name>Comment</name>
        <type>String</type>
        <format />
        <currency />
        <decimal />
        <group />
        <nullif>New York rocks!</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <cluster_schema />
    <GUI>
      <xloc>544</xloc>
      <yloc>208</yloc>
      <draw>Y</draw>
    </GUI>
  </transform>
  <transform>
    <name>Switch / case</name>
    <type>SwitchCase</type>
    <description />
    <distribute>Y</distribute>
    <custom_distribution />
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name />
    </partitioning>
    <fieldname>stateCode</fieldname>
    <use_contains>N</use_contains>
    <case_value_type>String</case_value_type>
    <case_value_format />
    <case_value_decimal />
    <case_value_group />
    <default_target_transform>Default</default_target_transform>
    <cases>
      <case>
        <value>CA</value>
        <target_transform>CA</target_transform>
      </case>
      <case>
        <value>FL</value>
        <target_transform>FL</target_transform>
      </case>
      <case>
        <value>NY</value>
        <target_transform>NY</target_transform>
      </case>
    </cases>
    <cluster_schema />
    <GUI>
      <xloc>368</xloc>
      <yloc>176</yloc>
      <draw>Y</draw>
    </GUI>
  </transform>
  <transform>
    <name>switch-case</name>
    <type>BeamOutput</type>
    <description />
    <distribute>Y</distribute>
    <custom_distribution />
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name />
    </partitioning>
    <output_location>${DATA_OUTPUT}</output_location>
    <file_description_name />
    <file_prefix>switch-case</file_prefix>
    <file_suffix>.csv</file_suffix>
    <windowed>N</windowed>
    <cluster_schema />
    <GUI>
      <xloc>864</xloc>
      <yloc>176</yloc>
      <draw>Y</draw>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
</pipeline>
