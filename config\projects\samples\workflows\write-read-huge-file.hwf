<?xml version="1.0" encoding="UTF-8"?>
<workflow>
  <name>write-read-huge-file</name>
  <name_sync_with_filename>Y</name_sync_with_filename>
  <description/>
  <extended_description/>
  <workflow_version/>
  <created_user>-</created_user>
  <created_date>2022/10/27 10:34:32.295</created_date>
  <modified_user>-</modified_user>
  <modified_date>2022/10/27 10:34:32.295</modified_date>
  <parameters>
    </parameters>
  <actions>
    <action>
      <name>Start</name>
      <description/>
      <type>SPECIAL</type>
      <attributes/>
      <DayOfMonth>1</DayOfMonth>
      <hour>12</hour>
      <intervalMinutes>60</intervalMinutes>
      <intervalSeconds>0</intervalSeconds>
      <minutes>0</minutes>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <weekDay>1</weekDay>
      <parallel>N</parallel>
      <xloc>64</xloc>
      <yloc>112</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>textfileoutput-huge-file.hpl</name>
      <description/>
      <type>PIPELINE</type>
      <attributes/>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <clear_files>N</clear_files>
      <clear_rows>N</clear_rows>
      <create_parent_folder>N</create_parent_folder>
      <exec_per_row>N</exec_per_row>
      <filename>${PROJECT_HOME}/workflows/textfileoutput-huge-file.hpl</filename>
      <logext/>
      <logfile/>
      <loglevel>Basic</loglevel>
      <parameters>
        <pass_all_parameters>Y</pass_all_parameters>
      </parameters>
      <params_from_previous>N</params_from_previous>
      <run_configuration>local</run_configuration>
      <set_append_logfile>N</set_append_logfile>
      <set_logfile>N</set_logfile>
      <wait_until_finished>Y</wait_until_finished>
      <parallel>N</parallel>
      <xloc>224</xloc>
      <yloc>112</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>csvinput-huge-file.hpl</name>
      <description/>
      <type>PIPELINE</type>
      <attributes/>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <clear_files>N</clear_files>
      <clear_rows>N</clear_rows>
      <create_parent_folder>N</create_parent_folder>
      <exec_per_row>N</exec_per_row>
      <filename>${PROJECT_HOME}/workflows/csvinput-huge-file.hpl</filename>
      <loglevel>Basic</loglevel>
      <parameters>
        <pass_all_parameters>Y</pass_all_parameters>
      </parameters>
      <params_from_previous>N</params_from_previous>
      <run_configuration>local</run_configuration>
      <set_append_logfile>N</set_append_logfile>
      <set_logfile>N</set_logfile>
      <wait_until_finished>Y</wait_until_finished>
      <parallel>N</parallel>
      <xloc>416</xloc>
      <yloc>112</yloc>
      <attributes_hac/>
    </action>
  </actions>
  <hops>
    <hop>
      <from>Start</from>
      <to>textfileoutput-huge-file.hpl</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>Y</unconditional>
    </hop>
    <hop>
      <from>textfileoutput-huge-file.hpl</from>
      <to>csvinput-huge-file.hpl</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
  </hops>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Noto Sans</fontname>
      <fontsize>10</fontsize>
      <height>27</height>
      <xloc>64</xloc>
      <yloc>48</yloc>
      <note>Create a 25M rows CSV file and read it back in using the CSV Input parrallel processing. </note>
      <width>544</width>
    </notepad>
  </notepads>
  <attributes/>
</workflow>
