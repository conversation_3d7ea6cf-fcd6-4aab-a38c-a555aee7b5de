{"executionType": "Transform", "dataSetMeta": null, "finished": true, "collectionDate": 1756888790509, "parentId": "5b95efcc-4f64-4c5a-8a10-788e5c9de608", "ownerId": "all-transforms", "setMetaData": {"FirstOutput/True.0": {"setKey": "FirstOutput/True.0", "logChannelId": "2ce68001-8c6e-4623-b04a-4ef03a8fa359", "name": "True", "fieldName": null, "sampleDescription": null, "copyNr": "0", "description": "First rows of True.0"}, "LastOutput/files/customers-100.txt.0": {"setKey": "LastOutput/files/customers-100.txt.0", "logChannelId": "5112c15e-923a-446a-8da8-c0f854deb2d2", "name": "files/customers-100.txt", "fieldName": null, "sampleDescription": null, "copyNr": "0", "description": "Last rows of files/customers-100.txt.0"}, "LastOutput/True.0": {"setKey": "LastOutput/True.0", "logChannelId": "2ce68001-8c6e-4623-b04a-4ef03a8fa359", "name": "True", "fieldName": null, "sampleDescription": null, "copyNr": "0", "description": "Last rows of True.0"}, "FirstOutput/files/customers-100.txt.0": {"setKey": "FirstOutput/files/customers-100.txt.0", "logChannelId": "5112c15e-923a-446a-8da8-c0f854deb2d2", "name": "files/customers-100.txt", "fieldName": null, "sampleDescription": null, "copyNr": "0", "description": "First rows of files/customers-100.txt.0"}, "LastOutput/False.0": {"setKey": "LastOutput/False.0", "logChannelId": "5308977a-35bb-4b25-9dac-e55bd579af00", "name": "False", "fieldName": null, "sampleDescription": null, "copyNr": "0", "description": "Last rows of False.0"}, "FirstOutput/False.0": {"setKey": "FirstOutput/False.0", "logChannelId": "5308977a-35bb-4b25-9dac-e55bd579af00", "name": "False", "fieldName": null, "sampleDescription": null, "copyNr": "0", "description": "First rows of False.0"}, "FirstOutput/FL and housenr>100.0": {"setKey": "FirstOutput/FL and housenr>100.0", "logChannelId": "ce632fbf-cf77-4158-a798-99e72f8e549b", "name": "FL and housenr>100", "fieldName": null, "sampleDescription": null, "copyNr": "0", "description": "First rows of FL and housenr>100.0"}, "LastOutput/FL and housenr>100.0": {"setKey": "LastOutput/FL and housenr>100.0", "logChannelId": "ce632fbf-cf77-4158-a798-99e72f8e549b", "name": "FL and housenr>100", "fieldName": null, "sampleDescription": null, "copyNr": "0", "description": "Last rows of FL and housenr>100.0"}}, "rowsBinaryGzipBase64Encoded": "H4sIAAAAAAAA/2NgYOBgEHLLLCou8S8tKSgt0Q8pKk3VM2CAARWfRLhUWmZOarF+cmlxSX5ualGxrqGBgV5JRQmSakEk1WgGqSJbQtgkISST3BJzipGNEkY2Cl1SAUXSRyExL0UhI7+0ODWvyA5kDUKlPLIVeBQCALWpJKMkAQAA"}