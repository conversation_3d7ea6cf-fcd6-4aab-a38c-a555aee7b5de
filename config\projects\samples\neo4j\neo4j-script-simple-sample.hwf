<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<workflow>
  <name>neo4j-script-simple-sample</name>
  <name_sync_with_filename>Y</name_sync_with_filename>
  <description/>
  <extended_description/>
  <workflow_version/>
  <created_user>-</created_user>
  <created_date>2021/08/03 18:04:47.008</created_date>
  <modified_user>-</modified_user>
  <modified_date>2021/08/03 18:04:47.008</modified_date>
  <parameters>
    </parameters>
  <actions>
    <action>
      <name>Start</name>
      <description/>
      <type>SPECIAL</type>
      <attributes/>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <intervalSeconds>0</intervalSeconds>
      <intervalMinutes>60</intervalMinutes>
      <hour>12</hour>
      <minutes>0</minutes>
      <weekDay>1</weekDay>
      <DayOfMonth>1</DayOfMonth>
      <parallel>N</parallel>
      <xloc>128</xloc>
      <yloc>96</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>Cypher Script: create colors</name>
      <description/>
      <type>NEO4J_CYPHER_SCRIPT</type>
      <attributes/>
      <connection>demo</connection>
      <script>MERGE (red:Color {label:'red', rgb: "#ff0000" })
;
MERGE (green:Color {label:'green', rgb: "#00ff00" })
;
MERGE (blue:Color {label:'blue', rgb: "#0000ff" })
;



</script>
      <replace_variables>Y</replace_variables>
      <parallel>N</parallel>
      <xloc>384</xloc>
      <yloc>96</yloc>
      <attributes_hac/>
    </action>
  </actions>
  <hops>
    <hop>
      <from>Start</from>
      <to>Cypher Script: create colors</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>Y</unconditional>
    </hop>
  </hops>
  <notepads>
  </notepads>
  <attributes/>
</workflow>
