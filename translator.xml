<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<translator-config>
    <reference-locale>en_US</reference-locale>
    <locale-list>
        <locale>en_US</locale>
        <locale>de_DE</locale>
        <locale>fr_FR</locale>
        <locale>it_IT</locale>
        <locale>zh_CN</locale>
        <locale>ja_JP</locale>
        <locale>ko_KR</locale>
        <locale>nl_NL</locale>
        <locale>pt_PT</locale>
        <locale>pt_BR</locale>
        <locale>no_NO</locale>
        <locale>es_ES</locale>
        <locale>es_AR</locale>
    </locale-list>

    <root-folder>../../../</root-folder>
    <folders-to-avoid>.*archive.*</folders-to-avoid>

    <files-to-avoid>
        <filename>MessagesSourceCrawler.java</filename>
        <filename>BaseTransformMeta.java</filename>
        <filename>BasePluginType.java</filename>
        <filename>ConfigurationDialog.java</filename>
        <filename>Const.java</filename>
        <filename>DatabaseFactory.java</filename>
    </files-to-avoid>

</translator-config>