<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>group-by-median-and-percentile</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/04/28 16:20:07.575</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/04/28 16:20:07.575</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>These simple median and percentiles are calculated in group by transformation
</note>
      <xloc>416</xloc>
      <yloc>32</yloc>
      <width>426</width>
      <heigth>41</heigth>
      <fontname>Segoe UI</fontname>
      <fontsize>9</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Generate rowsx10</from>
      <to>Add sequence</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Add sequence</from>
      <to>Join rows (cartesian product)</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>data input</from>
      <to>Join rows (cartesian product)</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Join rows (cartesian product)</from>
      <to>random integer</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>random integer</from>
      <to>Sort rows</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Join rows (cartesian product)</from>
      <to>Group by</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Sort rows</from>
      <to>Memory group by</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Group by</from>
      <to>Dummy (do nothing)</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Memory group by</from>
      <to>Dummy (do nothing) 2</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Add sequence</name>
    <type>Sequence</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <use_counter>Y</use_counter>
    <use_database>N</use_database>
    <increment_by>1</increment_by>
    <max_value>999999999</max_value>
    <seqname>SEQ_</seqname>
    <start_at>1</start_at>
    <valuename>group</valuename>
    <attributes/>
    <GUI>
      <xloc>512</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Dummy (do nothing)</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>1008</xloc>
      <yloc>256</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Dummy (do nothing) 2</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>1008</xloc>
      <yloc>400</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Generate rowsx10</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
</fields>
    <interval_in_ms>5000</interval_in_ms>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <never_ending>N</never_ending>
    <limit>10</limit>
    <row_time_field>now</row_time_field>
    <attributes/>
    <GUI>
      <xloc>256</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Group by</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>${java.io.tmpdir}</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>group</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>median</aggregate>
        <subject>value</subject>
        <type>MEDIAN</type>
        <valuefield/>
      </field>
      <field>
        <aggregate>avg</aggregate>
        <subject>value</subject>
        <type>AVERAGE</type>
        <valuefield/>
      </field>
      <field>
        <aggregate>percentile90</aggregate>
        <subject>value</subject>
        <type>PERCENTILE</type>
        <valuefield>90</valuefield>
      </field>
      <field>
        <aggregate>percentile75</aggregate>
        <subject>value</subject>
        <type>PERCENTILE</type>
        <valuefield>75</valuefield>
      </field>
      <field>
        <aggregate>percentile50</aggregate>
        <subject>value</subject>
        <type>PERCENTILE</type>
        <valuefield>50</valuefield>
      </field>
      <field>
        <aggregate>percentile25</aggregate>
        <subject>value</subject>
        <type>PERCENTILE</type>
        <valuefield>25</valuefield>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>832</xloc>
      <yloc>256</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Join rows (cartesian product)</name>
    <type>JoinRows</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>Add sequence</main>
    <compare>
      <condition>
        <negated>N</negated>
        <leftvalue/>
        <function>=</function>
        <rightvalue/>
      </condition>
    </compare>
    <attributes/>
    <GUI>
      <xloc>512</xloc>
      <yloc>256</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Memory group by</name>
    <type>MemoryGroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>group</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>median</aggregate>
        <subject>value</subject>
        <type>MEDIAN</type>
        <valuefield/>
      </field>
      <field>
        <aggregate>avg</aggregate>
        <subject>value</subject>
        <type>AVERAGE</type>
        <valuefield/>
      </field>
      <field>
        <aggregate>percentile90</aggregate>
        <subject>value</subject>
        <type>PERCENTILE</type>
        <valuefield>90</valuefield>
      </field>
      <field>
        <aggregate>percentile75</aggregate>
        <subject>value</subject>
        <type>PERCENTILE</type>
        <valuefield>75</valuefield>
      </field>
      <field>
        <aggregate>percentile50</aggregate>
        <subject>value</subject>
        <type>PERCENTILE</type>
        <valuefield>50</valuefield>
      </field>
      <field>
        <aggregate>percentile25</aggregate>
        <subject>value</subject>
        <type>PERCENTILE</type>
        <valuefield>25</valuefield>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>832</xloc>
      <yloc>400</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Sort rows</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>${java.io.tmpdir}</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>rand_int</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>640</xloc>
      <yloc>400</yloc>
    </GUI>
  </transform>
  <transform>
    <name>data input</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>value</name>
        <type>Number</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <data>
      <line>
        <item>9</item>
      </line>
      <line>
        <item>8</item>
      </line>
      <line>
        <item>3</item>
      </line>
      <line>
        <item>8</item>
      </line>
      <line>
        <item>7</item>
      </line>
      <line>
        <item>6</item>
      </line>
      <line>
        <item>3</item>
      </line>
      <line>
        <item>2</item>
      </line>
      <line>
        <item>7</item>
      </line>
      <line>
        <item>10</item>
      </line>
      <line>
        <item>6</item>
      </line>
      <line>
        <item>5</item>
      </line>
      <line>
        <item>8</item>
      </line>
      <line>
        <item>1</item>
      </line>
      <line>
        <item>4</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>256</xloc>
      <yloc>256</yloc>
    </GUI>
  </transform>
  <transform>
    <name>random integer</name>
    <type>RandomValue</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>rand_int</name>
        <type>random integer</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>512</xloc>
      <yloc>400</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
