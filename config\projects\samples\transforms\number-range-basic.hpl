<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>number-range-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/07/14 20:16:50.796</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/07/14 20:16:50.796</modified_date>
    <key_for_session_key/>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Specify number ranges and check in which range each value for a field belongs</note>
      <xloc>160</xloc>
      <yloc>128</yloc>
      <width>447</width>
      <heigth>27</heigth>
      <fontname>Noto Sans</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Test Data</from>
      <to>Age Groups</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Age Groups</from>
      <to>Output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Test Data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>age</name>
        <precision>-1</precision>
        <type>Integer</type>
      </field>
    </fields>
    <data>
      <line>
        <item>0</item>
      </line>
      <line>
        <item>3</item>
      </line>
      <line>
        <item>5</item>
      </line>
      <line>
        <item>7</item>
      </line>
      <line>
        <item>9</item>
      </line>
      <line>
        <item>11</item>
      </line>
      <line>
        <item>13</item>
      </line>
      <line>
        <item>14</item>
      </line>
      <line>
        <item>16</item>
      </line>
      <line>
        <item>18</item>
      </line>
      <line>
        <item>20</item>
      </line>
      <line>
        <item>27</item>
      </line>
      <line>
        <item>32</item>
      </line>
      <line>
        <item>38</item>
      </line>
      <line>
        <item>42</item>
      </line>
      <line>
        <item>45</item>
      </line>
      <line>
        <item>53</item>
      </line>
      <line>
        <item>57</item>
      </line>
      <line>
        <item>59</item>
      </line>
      <line>
        <item>61</item>
      </line>
      <line>
        <item>68</item>
      </line>
      <line>
        <item>74</item>
      </line>
      <line>
        <item>81</item>
      </line>
      <line>
        <item>87</item>
      </line>
      <line>
        <item>92</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>160</xloc>
      <yloc>208</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Age Groups</name>
    <type>NumberRange</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <inputField>age</inputField>
    <outputField>range</outputField>
    <fallBackValue>unknown</fallBackValue>
    <rules>
      <rule>
        <lower_bound>-1.7976931348623157E308</lower_bound>
        <upper_bound>12.0</upper_bound>
        <value>Child</value>
      </rule>
      <rule>
        <lower_bound>13.0</lower_bound>
        <upper_bound>19.0</upper_bound>
        <value>Adolescence</value>
      </rule>
      <rule>
        <lower_bound>19.0</lower_bound>
        <upper_bound>60.0</upper_bound>
        <value>Adulthood</value>
      </rule>
      <rule>
        <lower_bound>60.0</lower_bound>
        <upper_bound>1.7976931348623157E308</upper_bound>
        <value>Senior Adulthood</value>
      </rule>
    </rules>
    <attributes/>
    <GUI>
      <xloc>320</xloc>
      <yloc>208</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>480</xloc>
      <yloc>208</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
