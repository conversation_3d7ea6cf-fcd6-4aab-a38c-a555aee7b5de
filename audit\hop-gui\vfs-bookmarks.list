{"names": ["config/projects/default", "config/projects/default", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\workflows", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\workflows", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\workflows", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\workflows", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\workflows\\parallel", "C:\\Users\\<USER>", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\workflows\\parallel", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\pipelines", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\pipelines\\input", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\pipelines", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\pipelines", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\metadata", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\metadata\\pipeline-run-configuration", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\metadata", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\metadata\\unit-test", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\metadata", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\metadata-injection", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\metadata-injection", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\metadata-injection", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\test-project", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\test-project\\pipelines", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\test-project\\pipelines", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\test-project", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\test-project\\workflows", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\test-project\\workflows\\data_processing_workflow.hwf", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\test-project\\workflows", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\default", "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\default\\pipelines"]}