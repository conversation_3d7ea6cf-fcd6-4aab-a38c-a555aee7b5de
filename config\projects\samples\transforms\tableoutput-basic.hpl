<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>tableoutput-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/07/19 17:05:26.231</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/07/19 17:05:26.231</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Writes 5 lines of test data to a table</note>
      <xloc>128</xloc>
      <yloc>80</yloc>
      <width>206</width>
      <heigth>27</heigth>
      <fontname>Noto Sans</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Test Data</from>
      <to>Table output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Table output</name>
    <type>TableOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <commit>1000</commit>
    <connection>hop-samples</connection>
    <fields>
</fields>
    <ignore_errors>N</ignore_errors>
    <partitioning_daily>N</partitioning_daily>
    <partitioning_enabled>N</partitioning_enabled>
    <partitioning_monthly>Y</partitioning_monthly>
    <return_keys>N</return_keys>
    <specify_fields>N</specify_fields>
    <table>tableoutput_basic</table>
    <tablename_in_field>N</tablename_in_field>
    <tablename_in_table>Y</tablename_in_table>
    <truncate>Y</truncate>
    <use_batch>Y</use_batch>
    <attributes/>
    <GUI>
      <xloc>336</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Test Data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>id</name>
        <precision>-1</precision>
        <type>Integer</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>col_bool</name>
        <precision>-1</precision>
        <type>Boolean</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>col_string</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <format>yyyy-MM-dd</format>
        <length>-1</length>
        <name>col_date</name>
        <precision>-1</precision>
        <type>Date</type>
      </field>
    </fields>
    <data>
      <line>
        <item>1</item>
        <item>Y</item>
        <item>col1</item>
        <item>2021-01-01</item>
      </line>
      <line>
        <item>2</item>
        <item>N</item>
        <item>col2</item>
        <item>2022-01-01</item>
      </line>
      <line>
        <item>3</item>
        <item>N</item>
        <item>col3</item>
        <item>2023-01-01</item>
      </line>
      <line>
        <item>4</item>
        <item>Y</item>
        <item>col4</item>
        <item>2024-01-01</item>
      </line>
      <line>
        <item>5</item>
        <item>Y</item>
        <item>col5</item>
        <item>2025-01-01</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>128</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
