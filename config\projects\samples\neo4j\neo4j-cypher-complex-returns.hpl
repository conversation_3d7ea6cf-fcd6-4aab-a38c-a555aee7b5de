<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>neo4j-cypher-complex-returns</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/08/03 17:53:48.809</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/08/03 17:53:48.809</modified_date>
    <key_for_session_key/>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Please run sample : neo4j-output-parallel-load first to see actual data returned.</note>
      <xloc>96</xloc>
      <yloc>224</yloc>
      <width>492</width>
      <heigth>29</heigth>
      <fontname>Noto Sans</fontname>
      <fontsize>13</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Cypher query returning Node, Map and List</from>
      <to>Process further with JSON Input</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Cypher query returning Node, Map and List</name>
    <type>Neo4jCypherOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>demo</connection>
    <cypher>MATCH(c:Customer)
RETURN 
  c as customerNode, 
  c { .id, .firstName, .lastName } as nameMap,
  [ c.firstName, c.lastName ] as namesList</cypher>
    <batch_size/>
    <read_only>Y</read_only>
    <nr_retries_on_error/>
    <retry>Y</retry>
    <cypher_from_field>N</cypher_from_field>
    <cypher_field/>
    <unwind>N</unwind>
    <unwind_map/>
    <returning_graph>N</returning_graph>
    <return_graph_field/>
    <mappings/>
    <returns>
      <return>
        <name>customerNode</name>
        <type>String</type>
        <source_type>Node</source_type>
      </return>
      <return>
        <name>nameMap</name>
        <type>String</type>
        <source_type>Map</source_type>
      </return>
      <return>
        <name>namesList</name>
        <type>String</type>
        <source_type>List</source_type>
      </return>
    </returns>
    <attributes/>
    <GUI>
      <xloc>160</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Process further with JSON Input</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>448</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
