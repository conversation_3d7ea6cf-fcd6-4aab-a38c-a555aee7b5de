<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>user-defined-java-class-variables</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <pipeline_status>0</pipeline_status>
    <parameters>
      <parameter>
        <name>SAMPLE_PARAMETER</name>
        <default_value>abcd</default_value>
        <description>Just a variable that gets set with default value abcd</description>
      </parameter>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/09/24 10:46:40.725</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/09/24 10:46:40.725</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>10M rows</from>
      <to>UDJC: get variable sample</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>10M rows</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <length>-1</length>
        <name>staticValue</name>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <type>String</type>
        <nullif>Some static value</nullif>
      </field>
    </fields>
    <interval_in_ms>5000</interval_in_ms>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <never_ending>N</never_ending>
    <limit>10000000</limit>
    <row_time_field>now</row_time_field>
    <attributes/>
    <GUI>
      <xloc>112</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>UDJC: get variable sample</name>
    <type>UserDefinedJavaClass</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <definitions>
      <definition>
        <class_type>TRANSFORM_CLASS</class_type>
        <class_name>Processor</class_name>
        <class_source>
private String sampleParameter;

public boolean init() {
  sampleParameter = getVariable("SAMPLE_PARAMETER");

  return super.init();
}

public boolean processRow() throws HopException 
{
  Object[] r=getRow();
  if (r==null)
  {
    setOutputDone();
    return false;
  }
 
  Object[] outputRowData = createOutputRow(r, data.outputRowMeta.size());
  int outputIndex = getInputRowMeta().size();

  outputRowData[outputIndex++] = sampleParameter;

  putRow(data.outputRowMeta, outputRowData);

  return true;
}
</class_source>
      </definition>
    </definitions>
    <fields>
      <field>
        <field_name>sampleParameter</field_name>
        <field_type>String</field_type>
        <field_length>-1</field_length>
        <field_precision>-1</field_precision>
      </field>
    </fields>
    <clear_result_fields>N</clear_result_fields>
    <info_transforms/>
    <target_transforms/>
    <usage_parameters/>
    <attributes/>
    <GUI>
      <xloc>320</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
