<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>cleanup-remove-all-relationships</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <pipeline_status>0</pipeline_status>
    <parameters>
      <parameter>
        <name>BATCH_SIZE</name>
        <default_value>1000</default_value>
        <description>Number of relationships to delete at once</description>
      </parameter>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/08/03 18:27:03.628</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/08/03 18:27:03.628</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>batchSize</from>
      <to>build cypher</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>build cypher</from>
      <to>totalCount</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>nrBatches</from>
      <to>x nrBatches</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>totalCount</from>
      <to>totalCount>0?</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>totalCount>0?</from>
      <to>nrBatches</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>x nrBatches</from>
      <to>DELETE</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>DELETE</name>
    <type>Neo4jCypherOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>demo</connection>
    <cypher>MATCH ()-[rel]-() 
WITH rel LIMIT ${BATCH_SIZE} 
DELETE rel
RETURN count(*) as deleteCount
;
</cypher>
    <batch_size>1</batch_size>
    <read_only>N</read_only>
    <nr_retries_on_error/>
    <retry>Y</retry>
    <cypher_from_field>N</cypher_from_field>
    <cypher_field>cypherDelete</cypher_field>
    <unwind>N</unwind>
    <unwind_map/>
    <returning_graph>N</returning_graph>
    <return_graph_field/>
    <mappings/>
    <returns>
      <return>
        <name>deleteCount</name>
        <type>Integer</type>
        <source_type/>
      </return>
    </returns>
    <attributes/>
    <GUI>
      <xloc>688</xloc>
      <yloc>192</yloc>
    </GUI>
  </transform>
  <transform>
    <name>batchSize</name>
    <type>GetVariable</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>batchSize</name>
        <variable>${BATCH_SIZE}</variable>
        <type>Integer</type>
        <format>#</format>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>112</xloc>
      <yloc>64</yloc>
    </GUI>
  </transform>
  <transform>
    <name>build cypher</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>
var cypherDelete = "MATCH (n) WITH n LIMIT "+batchSize;
cypherDelete+=" DETACH DELETE n RETURN count(*) as deleteCount;";


</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>cypherDelete</name>
        <rename>cypherDelete</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>272</xloc>
      <yloc>64</yloc>
    </GUI>
  </transform>
  <transform>
    <name>nrBatches</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>
var nrBatches = totalCount / batchSize
</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>nrBatches</name>
        <rename>nrBatches</rename>
        <type>Integer</type>
        <length>7</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>320</xloc>
      <yloc>192</yloc>
    </GUI>
  </transform>
  <transform>
    <name>totalCount</name>
    <type>Neo4jCypherOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>demo</connection>
    <cypher>MATCH ()-[rel]-() 
RETURN count(rel) AS totalCount;</cypher>
    <batch_size>1</batch_size>
    <read_only>N</read_only>
    <nr_retries_on_error/>
    <retry>Y</retry>
    <cypher_from_field>N</cypher_from_field>
    <cypher_field/>
    <unwind>N</unwind>
    <unwind_map/>
    <returning_graph>N</returning_graph>
    <return_graph_field/>
    <mappings/>
    <returns>
      <return>
        <name>totalCount</name>
        <type>Integer</type>
        <source_type/>
      </return>
    </returns>
    <attributes/>
    <GUI>
      <xloc>432</xloc>
      <yloc>64</yloc>
    </GUI>
  </transform>
  <transform>
    <name>totalCount>0?</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <send_true_to/>
    <send_false_to/>
    <compare>
      <condition>
        <negated>N</negated>
        <leftvalue>totalCount</leftvalue>
        <function>&gt;</function>
        <rightvalue/>
        <value>
          <name>constant</name>
          <type>Integer</type>
          <text>0</text>
          <length>-1</length>
          <precision>0</precision>
          <isnull>N</isnull>
          <mask>####0;-####0</mask>
        </value>
      </condition>
    </compare>
    <attributes/>
    <GUI>
      <xloc>592</xloc>
      <yloc>64</yloc>
    </GUI>
  </transform>
  <transform>
    <name>x nrBatches</name>
    <type>CloneRow</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <addcloneflag>N</addcloneflag>
    <addclonenum>Y</addclonenum>
    <clonenumfield>batchNr</clonenumfield>
    <nrclonefield>nrBatches</nrclonefield>
    <nrcloneinfield>Y</nrcloneinfield>
    <nrclones>0</nrclones>
    <attributes/>
    <GUI>
      <xloc>496</xloc>
      <yloc>192</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
