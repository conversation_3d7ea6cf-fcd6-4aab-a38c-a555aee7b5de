<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>unbounded-synthetic-data</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2020/07/29 18:58:38.932</created_date>
    <modified_user>-</modified_user>
    <modified_date>2020/07/29 18:58:38.932</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>oo rows</from>
      <to>random data</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>random data</from>
      <to>60s window</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>60s window</from>
      <to>unbounded-synthetic-data</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>60s window</name>
    <type>BeamWindow</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <window_type>Fixed</window_type>
    <duration>60</duration>
    <every/>
    <max_window_field>maxWindow</max_window_field>
    <start_window_field>startWindow</start_window_field>
    <end_window_field>endWindow</end_window_field>
    <attributes/>
    <GUI>
      <xloc>400</xloc>
      <yloc>64</yloc>
    </GUI>
  </transform>
  <transform>
    <name>oo rows</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>static</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>Apache Beam</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <limit>10000</limit>
    <never_ending>Y</never_ending>
    <interval_in_ms>10</interval_in_ms>
    <row_time_field>now</row_time_field>
    <last_time_field>previous</last_time_field>
    <attributes/>
    <GUI>
      <xloc>80</xloc>
      <yloc>64</yloc>
    </GUI>
  </transform>
  <transform>
    <name>random data</name>
    <type>RandomValue</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>num</name>
        <type>random number</type>
      </field>
      <field>
        <name>int</name>
        <type>random integer</type>
      </field>
      <field>
        <name>str</name>
        <type>random string</type>
      </field>
      <field>
        <name>uuid</name>
        <type>random uuid</type>
      </field>
      <field>
        <name>mac</name>
        <type>random machmacmd5</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>240</xloc>
      <yloc>64</yloc>
    </GUI>
  </transform>
  <transform>
    <name>unbounded-synthetic-data</name>
    <type>BeamOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <output_location>${DATA_OUTPUT}</output_location>
    <file_description_name/>
    <file_prefix>unbounded-synthetic-data</file_prefix>
    <file_suffix/>
    <windowed>Y</windowed>
    <attributes/>
    <GUI>
      <xloc>576</xloc>
      <yloc>64</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
