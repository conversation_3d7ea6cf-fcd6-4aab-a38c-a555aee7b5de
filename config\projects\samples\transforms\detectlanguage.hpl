<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>detectlanguage</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <pipeline_status>0</pipeline_status>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/12/18 15:00:31.032</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/12/18 15:00:31.032</modified_date>
  </info>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Ubuntu</fontname>
      <fontsize>11</fontsize>
      <height>346</height>
      <xloc>624</xloc>
      <yloc>160</yloc>
      <note>The Detect Language plugin uses Lingua to identify the language of documents, with support for 75 languages.

While generally accurate, occasional misclassifications may still occur, particularly with less-resourced languages.
Other reasons could be due to:

- Shared vocabulary or similar structures
- Short or ambiguous text
- Language overlaps.
- Code switching (e.g. text contains one language and then switches to another).

This sample demostrates 74 successful detections and 1 misclassification: a Latin phrase incorrectly identified as Catalan.

English: Seize the day, because life is short and time flies.
Latin: Carpe diem, quia vita brevis est et tempus fugit.
Catalan: Aprofita el dia, perquè la vida és curta i el temps passa volant.

(Note: Google translate was used to verify misclassification)

Reference:
https://github.com/pemistahl/lingua
</note>
      <width>733</width>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Corpus</from>
      <to>Detect Language</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Detect Language</from>
      <to>Select values</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Select values</from>
      <to>Match</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Match</from>
      <to>Text output (Fail)</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Match</from>
      <to>Text output (Pass)</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Corpus</name>
    <type>DataGrid</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>AFRIKAANS</item>
        <item>Die son skyn helder vandag.</item>
      </line>
      <line>
        <item>ALBANIAN</item>
        <item>Unë dua të lexoj një libër.</item>
      </line>
      <line>
        <item>ARABIC</item>
        <item>السماء صافية هذا المساء.</item>
      </line>
      <line>
        <item>ARMENIAN</item>
        <item>Ես սիրում եմ սուրճ խմել առավոտյան։</item>
      </line>
      <line>
        <item>AZERBAIJANI</item>
        <item>Bu gün hava çox gözəldir.</item>
      </line>
      <line>
        <item>BASQUE</item>
        <item>Mendira joatea gustatzen zait.</item>
      </line>
      <line>
        <item>BELARUSIAN</item>
        <item>Я люблю чытаць кнігі вечарамі.</item>
      </line>
      <line>
        <item>BENGALI</item>
        <item>আমি প্রতিদিন সকালে হাঁটতে যাই।</item>
      </line>
      <line>
        <item>BOKMAL</item>
        <item>Jeg liker å lage mat hjemme.</item>
      </line>
      <line>
        <item>BOSNIAN</item>
        <item>Volim da šetam pored rijeke.</item>
      </line>
      <line>
        <item>BULGARIAN</item>
        <item>Днес времето е слънчево и топло.</item>
      </line>
      <line>
        <item>CATALAN</item>
        <item>M'agrada escoltar música clàssica.</item>
      </line>
      <line>
        <item>CHINESE</item>
        <item>今天天气很好。</item>
      </line>
      <line>
        <item>CROATIAN</item>
        <item>Učim hrvatski jezik.</item>
      </line>
      <line>
        <item>CZECH</item>
        <item>Rád chodím do kina s přáteli.</item>
      </line>
      <line>
        <item>DANISH</item>
        <item>Vi skal på ferie i næste uge.</item>
      </line>
      <line>
        <item>DUTCH</item>
        <item>Het is leuk om nieuwe dingen te leren.</item>
      </line>
      <line>
        <item>ENGLISH</item>
        <item>She enjoys reading novels in her free time.</item>
      </line>
      <line>
        <item>ESPERANTO</item>
        <item>La suno brilas hele hodiaŭ.</item>
      </line>
      <line>
        <item>ESTONIAN</item>
        <item>Ma armastan jalgrattaga sõita.</item>
      </line>
      <line>
        <item>FINNISH</item>
        <item>Pidän kahvin juomisesta aamuisin.</item>
      </line>
      <line>
        <item>FRENCH</item>
        <item>Il fait beau aujourd'hui.</item>
      </line>
      <line>
        <item>GANDA</item>
        <item>Njagala okulaba mikwano gyange.</item>
      </line>
      <line>
        <item>GEORGIAN</item>
        <item>მე მიყვარს მუსიკის მოსმენა.</item>
      </line>
      <line>
        <item>GERMAN</item>
        <item>Heute ist ein schöner Tag.</item>
      </line>
      <line>
        <item>GREEK</item>
        <item>Μου αρέσει να διαβάζω βιβλία.</item>
      </line>
      <line>
        <item>GUJARATI</item>
        <item>હું ગુજરાતી ભાષા શીખી રહ્યો છું.</item>
      </line>
      <line>
        <item>HEBREW</item>
        <item>אני אוהב לטייל בטבע.</item>
      </line>
      <line>
        <item>HINDI</item>
        <item>मुझे सिनेमा देखना पसंद है।</item>
      </line>
      <line>
        <item>HUNGARIAN</item>
        <item>Szeretek úszni a tengerben.</item>
      </line>
      <line>
        <item>ICELANDIC</item>
        <item>Veðrið er gott í dag.</item>
      </line>
      <line>
        <item>INDONESIAN</item>
        <item>Saya suka makan bakso di warung.</item>
      </line>
      <line>
        <item>IRISH</item>
        <item>Is breá liom ceol traidisiúnta.</item>
      </line>
      <line>
        <item>ITALIAN</item>
        <item>Mi piace cucinare per la mia famiglia.</item>
      </line>
      <line>
        <item>JAPANESE</item>
        <item>私は日本語を勉強しています。</item>
      </line>
      <line>
        <item>KAZAKH</item>
        <item>Мен Астана қаласында тұрамын.</item>
      </line>
      <line>
        <item>KOREAN</item>
        <item>나는 한국 음식을 좋아합니다.</item>
      </line>
      <line>
        <item>LATIN</item>
        <item>Carpe diem, quia vita brevis est et tempus fugit.</item>
      </line>
      <line>
        <item>LATVIAN</item>
        <item>Es mīlu staigāt pa mežu.</item>
      </line>
      <line>
        <item>LITHUANIAN</item>
        <item>Aš mėgstu keliauti į užsienį.</item>
      </line>
      <line>
        <item>MACEDONIAN</item>
        <item>Денес е убав сончев ден.</item>
      </line>
      <line>
        <item>MALAY</item>
        <item>Saya suka membaca buku cerita.</item>
      </line>
      <line>
        <item>MAORI</item>
        <item>Kei te pai ahau i tēnei rā.</item>
      </line>
      <line>
        <item>MARATHI</item>
        <item>मला चित्रपट पाहायला आवडतात.</item>
      </line>
      <line>
        <item>MONGOLIAN</item>
        <item>Би өглөөний цайнд кофе уудаг.</item>
      </line>
      <line>
        <item>NYNORSK</item>
        <item>Eg likar å lese bøker om kvelden.</item>
      </line>
      <line>
        <item>PERSIAN</item>
        <item>من دوست دارم موسیقی گوش کنم.</item>
      </line>
      <line>
        <item>POLISH</item>
        <item>Lubię spacerować po parku.</item>
      </line>
      <line>
        <item>PORTUGUESE</item>
        <item>Estamos aprendendo novas línguas.</item>
      </line>
      <line>
        <item>PUNJABI</item>
        <item>ਮੈਂ ਪੰਜਾਬੀ ਸਿੱਖ ਰਿਹਾ ਹਾਂ।</item>
      </line>
      <line>
        <item>ROMANIAN</item>
        <item>Astăzi este o zi frumoasă.</item>
      </line>
      <line>
        <item>RUSSIAN</item>
        <item>Я люблю готовить дома.</item>
      </line>
      <line>
        <item>SERBIAN</item>
        <item>Данас је добар дан.</item>
      </line>
      <line>
        <item>SHONA</item>
        <item>Ndinoda kuverenga mabhuku.</item>
      </line>
      <line>
        <item>SLOVAK</item>
        <item>Rád cestujem po svete.</item>
      </line>
      <line>
        <item>SLOVENE</item>
        <item>Všeč mi je poslušati glasbo.</item>
      </line>
      <line>
        <item>SOMALI</item>
        <item>Waxaan jeclahay inaan buugaag akhriyo.</item>
      </line>
      <line>
        <item>SOTHO</item>
        <item>Ke rata ho bapala bolo.</item>
      </line>
      <line>
        <item>SPANISH</item>
        <item>Me gusta aprender idiomas nuevos.</item>
      </line>
      <line>
        <item>SWAHILI</item>
        <item>Napenda kucheza mpira wa miguu.</item>
      </line>
      <line>
        <item>SWEDISH</item>
        <item>Vi ska laga middag tillsammans.</item>
      </line>
      <line>
        <item>TAGALOG</item>
        <item>Mahilig ako sa musika.</item>
      </line>
      <line>
        <item>TAMIL</item>
        <item>எனக்கு புத்தகங்கள் படிக்க விருப்பம்.</item>
      </line>
      <line>
        <item>TELUGU</item>
        <item>నాకు సినిమాలు చూడటం ఇష్టం.</item>
      </line>
      <line>
        <item>THAI</item>
        <item>ฉันชอบทานอาหารไทยมาก.</item>
      </line>
      <line>
        <item>TSONGA</item>
        <item>Ndzi rhandza ku hlaya tibuku.</item>
      </line>
      <line>
        <item>TSWANA</item>
        <item>Ke rata go ithuta dipuo tse disha.</item>
      </line>
      <line>
        <item>TURKISH</item>
        <item>Bugün hava çok güzel.</item>
      </line>
      <line>
        <item>UKRAINIAN</item>
        <item>Я люблю слухати музику.</item>
      </line>
      <line>
        <item>URDU</item>
        <item>مجھے کتابیں پڑھنا پسند ہے۔</item>
      </line>
      <line>
        <item>VIETNAMESE</item>
        <item>Tôi thích học tiếng Việt.</item>
      </line>
      <line>
        <item>WELSH</item>
        <item>Dw i'n hoffi mynd am dro ar y traeth.</item>
      </line>
      <line>
        <item>XHOSA</item>
        <item>Ndiyathanda ukufunda iincwadi.</item>
      </line>
      <line>
        <item>YORUBA</item>
        <item>Mo fẹ́ kọ́ ẹ̀kọ́ èdè tuntun.</item>
      </line>
      <line>
        <item>ZULU</item>
        <item>Ngiyathanda ukufunda izincwadi.</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>expected_language</name>
        <type>String</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>corpus</name>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>624</xloc>
      <yloc>576</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Detect Language</name>
    <type>DetectLanguage</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <corpusField>corpus</corpusField>
    <parallelism>N</parallelism>
    <attributes/>
    <GUI>
      <xloc>800</xloc>
      <yloc>576</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Match</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compare>
      <condition>
        <conditions>
</conditions>
        <function>=</function>
        <leftvalue>expected_language</leftvalue>
        <negated>N</negated>
        <operator>-</operator>
        <rightvalue>detected_language</rightvalue>
      </condition>
    </compare>
    <send_false_to>Text output (Fail)</send_false_to>
    <send_true_to>Text output (Pass)</send_true_to>
    <attributes/>
    <GUI>
      <xloc>1152</xloc>
      <yloc>576</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Select values</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>expected_language</name>
      </field>
      <field>
        <name>detected_language</name>
      </field>
      <field>
        <name>detected_language_confidence</name>
      </field>
      <field>
        <name>corpus</name>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <GUI>
      <xloc>976</xloc>
      <yloc>576</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Text output (Fail)</name>
    <type>TextFileOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <schema_definition/>
    <separator>;</separator>
    <enclosure>"</enclosure>
    <enclosure_forced>N</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>Y</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding>UTF-8</encoding>
    <endedLine/>
    <fileNameInField>N</fileNameInField>
    <fileNameField/>
    <create_parent_folder>Y</create_parent_folder>
    <file>
      <name>detectlanguage_fail</name>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>Y</do_not_open_new_file_init>
      <extention>csv</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format/>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>N</fast_dump>
      <splitevery/>
    </file>
    <fields>
    </fields>
    <attributes/>
    <GUI>
      <xloc>1328</xloc>
      <yloc>624</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Text output (Pass)</name>
    <type>TextFileOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <schema_definition/>
    <separator>;</separator>
    <enclosure>"</enclosure>
    <enclosure_forced>N</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>Y</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding>UTF-8</encoding>
    <endedLine/>
    <fileNameInField>N</fileNameInField>
    <fileNameField/>
    <create_parent_folder>Y</create_parent_folder>
    <file>
      <name>detectlanguage_pass</name>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>Y</do_not_open_new_file_init>
      <extention>csv</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format/>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>N</fast_dump>
      <splitevery/>
    </file>
    <fields>
    </fields>
    <attributes/>
    <GUI>
      <xloc>1328</xloc>
      <yloc>544</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
