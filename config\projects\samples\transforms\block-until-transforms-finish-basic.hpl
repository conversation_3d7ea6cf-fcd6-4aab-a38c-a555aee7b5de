<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>Block this transform until transforms finish </name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/04/28 15:29:02.042</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/04/28 15:29:02.042</modified_date>
    <key_for_session_key/>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Block transformation waits until Found GOD step is completely executed!
You can visualise that by dotted rectangle as both fake data and block transform transforms are running continuously.</note>
      <xloc>240</xloc>
      <yloc>32</yloc>
      <width>631</width>
      <heigth>42</heigth>
      <fontname>Segoe UI</fontname>
      <fontsize>9</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Generate rows</from>
      <to>Fake data</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Fake data</from>
      <to>Delay row</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Delay row</from>
      <to>Found GOD!</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>1 row </from>
      <to>Block this transform until transforms finish</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Block this transform until transforms finish</from>
      <to>Alert JS</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Fake data</name>
    <type>Fake</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <locale>en</locale>
    <fields>
      <field>
        <name>poki_name</name>
        <type>Ancient</type>
        <topic>god</topic>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>416</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Generate rows</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
    </fields>
    <limit>1000</limit>
    <never_ending>N</never_ending>
    <interval_in_ms>5000</interval_in_ms>
    <row_time_field>now</row_time_field>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <attributes/>
    <GUI>
      <xloc>288</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Delay row</name>
    <type>Delay</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <timeout>1</timeout>
    <scaletime>seconds</scaletime>
    <attributes/>
    <GUI>
      <xloc>544</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Found GOD!</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>672</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>1 row </name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
    </fields>
    <limit>1</limit>
    <never_ending>N</never_ending>
    <interval_in_ms>5000</interval_in_ms>
    <row_time_field>now</row_time_field>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <attributes/>
    <GUI>
      <xloc>288</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Block this transform until transforms finish</name>
    <type>BlockUntilTransformsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <transforms>
      <transform>
        <name>Found GOD!</name>
        <CopyNr>0</CopyNr>
      </transform>
    </transforms>
    <attributes/>
    <GUI>
      <xloc>448</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Alert JS</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>//Script here


Alert("Waited and Done!");</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>    </fields>
    <attributes/>
    <GUI>
      <xloc>656</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
