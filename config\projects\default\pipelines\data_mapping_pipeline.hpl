<?xml version="1.0" encoding="UTF-8"?>
<pipeline>
  <info>
    <name>data_mapping_pipeline</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description>数据映射测试管道</description>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2025/09/04 09:14:00.000</created_date>
    <modified_user>-</modified_user>
    <modified_date>2025/09/04 09:14:00.000</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>CSV file input</from>
      <to>Select values</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Select values</from>
      <to>Text file output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>CSV file input</name>
    <type>CSVInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <filename>${PROJECT_HOME}/datasets/source_table.csv</filename>
    <filename_field/>
    <rownum_field/>
    <include_filename>N</include_filename>
    <separator>,</separator>
    <enclosure>"</enclosure>
    <header>Y</header>
    <buffer_size>50000</buffer_size>
    <lazy_conversion>Y</lazy_conversion>
    <add_filename_result>N</add_filename_result>
    <parallel>N</parallel>
    <newline_possible>N</newline_possible>
    <encoding/>
    <fields>
      <field>
        <name>id</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>15</length>
        <precision>0</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>name</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>50</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>age</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>15</length>
        <precision>0</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>city</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>50</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>salary</name>
        <type>Number</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>15</length>
        <precision>2</precision>
        <trim_type>none</trim_type>
      </field>
    </fields>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>112</xloc>
      <yloc>112</yloc>
      <draw>Y</draw>
    </GUI>
  </transform>
  <transform>
    <name>Select values</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>id</name>
        <rename>employee_id</rename>
      </field>
      <field>
        <name>name</name>
        <rename>employee_name</rename>
      </field>
      <field>
        <name>age</name>
        <rename>employee_age</rename>
      </field>
      <field>
        <name>city</name>
        <rename>work_city</rename>
      </field>
      <field>
        <name>salary</name>
        <rename>monthly_salary</rename>
      </field>
    </fields>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>272</xloc>
      <yloc>112</yloc>
      <draw>Y</draw>
    </GUI>
  </transform>
  <transform>
    <name>Text file output</name>
    <type>TextFileOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <separator>,</separator>
    <enclosure>"</enclosure>
    <enclosure_forced>N</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>Y</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding/>
    <endedLine/>
    <fileNameInField>N</fileNameInField>
    <fileNameField/>
    <create_parent_folder>Y</create_parent_folder>
    <file>
      <name>${PROJECT_HOME}/datasets/mapped_output</name>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>N</do_not_open_new_file_init>
      <extention>csv</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format/>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>N</fast_dump>
      <splitevery>0</splitevery>
    </file>
    <fields>
      <field>
        <name>employee_id</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>employee_name</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>employee_age</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>work_city</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>monthly_salary</name>
        <type>Number</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
    </fields>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>432</xloc>
      <yloc>112</yloc>
      <draw>Y</draw>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
