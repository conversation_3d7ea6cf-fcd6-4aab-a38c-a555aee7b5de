<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>json-input-nested-elements</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/08/31 20:22:38.916</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/08/31 20:22:38.916</modified_date>
  </info>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Noto Sans</fontname>
      <fontsize>10</fontsize>
      <height>146</height>
      <xloc>160</xloc>
      <yloc>80</yloc>
      <note>This sample reads the JSON definition for the Dataflow pipeline run configuration metadata item.
This metadata item is defined in ${PROJECT_HOME}/metadata/metadata/pipeline-run-configuration/DataFlow.json

In the first JSON Input transform, we read the general run configuration elements. 
We also pick up the repeating element (array) of configuration variables as a String field. 

In the second JSON Input transform, we read and parse the configuration variables. 
This second transform uses the "configuration_variables" to read from, instead of reading from a file like we did in the first JSON Input transform. </note>
      <width>813</width>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>read dataflow run configuration</from>
      <to>read configuration variables 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>read configuration variables 2</from>
      <to>remove configuration_variables element</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>remove configuration_variables element</from>
      <to>preview</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>preview</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>976</xloc>
      <yloc>256</yloc>
    </GUI>
  </transform>
  <transform>
    <name>read configuration variables 2</name>
    <type>JsonInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <include>N</include>
    <include_field/>
    <rownum>N</rownum>
    <addresultfile>N</addresultfile>
    <readurl>N</readurl>
    <removeSourceField>N</removeSourceField>
    <IsIgnoreEmptyFile>N</IsIgnoreEmptyFile>
    <doNotFailIfNoFile>Y</doNotFailIfNoFile>
    <ignoreMissingPath>Y</ignoreMissingPath>
    <defaultPathLeafToNull>Y</defaultPathLeafToNull>
    <rownum_field/>
    <file>
      <name/>
      <filemask/>
      <exclude_filemask/>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
    </file>
    <fields>
      <field>
        <name>var_name</name>
        <path>$.*.name</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>Y</repeat>
      </field>
      <field>
        <name>var_description</name>
        <path>$.*.description</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>Y</repeat>
      </field>
      <field>
        <name>var_value</name>
        <path>$.*.value</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>Y</repeat>
      </field>
    </fields>
    <limit>0</limit>
    <IsInFields>Y</IsInFields>
    <IsAFile>N</IsAFile>
    <valueField>configuration_variables</valueField>
    <shortFileFieldName/>
    <pathFieldName/>
    <hiddenFieldName/>
    <lastModificationTimeFieldName/>
    <uriNameFieldName/>
    <rootUriNameFieldName/>
    <extensionFieldName/>
    <sizeFieldName/>
    <attributes/>
    <GUI>
      <xloc>432</xloc>
      <yloc>256</yloc>
    </GUI>
  </transform>
  <transform>
    <name>read dataflow run configuration</name>
    <type>JsonInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <include>N</include>
    <include_field/>
    <rownum>N</rownum>
    <addresultfile>N</addresultfile>
    <readurl>N</readurl>
    <removeSourceField>N</removeSourceField>
    <IsIgnoreEmptyFile>N</IsIgnoreEmptyFile>
    <doNotFailIfNoFile>Y</doNotFailIfNoFile>
    <ignoreMissingPath>Y</ignoreMissingPath>
    <defaultPathLeafToNull>Y</defaultPathLeafToNull>
    <rownum_field/>
    <file>
      <name>${PROJECT_HOME}/metadata/pipeline-run-configuration/DataFlow.json</name>
      <filemask/>
      <exclude_filemask/>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
    </file>
    <fields>
      <field>
        <name>run_config_name</name>
        <path>$.name</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>run_config_description</name>
        <path>$.description</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>gcp_worker_machine_type</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.gcpWorkerMachineType</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>streaming_hop_transforms_buffer_size</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.streamingHopTransformsBufferSize</path>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>gcp_max_nb_workers</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.gcpMaximumNumberOfWorkers</path>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>gcp_zone</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.gcpZone</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>gcp_project_id</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.gcpProjectId</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>user_agent</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.userAgent</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>gcp_region</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.gcpRegion</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>plugins_to_stage</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.pluginsToStage</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>temp_location</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.tempLocation</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>gcp_worker_disk_type</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.gcpWorkerDiskType</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>gcp_app_name</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.gcpAppName</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>gcp_initial_nb_workers</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.gcpInitialNumberOfWorkers</path>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>gcp_staging_location</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.gcpStagingLocation</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>fat_jar</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.fatJar</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>streaming_hop_transforms_flush_interval</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.streamingHopTransformsFlushInterval</path>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>transform_plugin_classes</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.transformPluginClasses</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>gcp_disk_size_gb</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.gcpDiskSizeGb</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>gcp_auto_scaling_algorithm</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.gcpAutoScalingAlgorithm</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>xp_plugin_classes</name>
        <path>$.engineRunConfiguration.BeamDataFlowPipelineEngine.xpPluginClasses</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>configuration_variables</name>
        <path>$.configurationVariables</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>Y</repeat>
      </field>
    </fields>
    <limit>0</limit>
    <IsInFields>N</IsInFields>
    <IsAFile>N</IsAFile>
    <valueField/>
    <shortFileFieldName/>
    <pathFieldName/>
    <hiddenFieldName/>
    <lastModificationTimeFieldName/>
    <uriNameFieldName/>
    <rootUriNameFieldName/>
    <extensionFieldName/>
    <sizeFieldName/>
    <attributes/>
    <GUI>
      <xloc>160</xloc>
      <yloc>256</yloc>
    </GUI>
  </transform>
  <transform>
    <name>remove configuration_variables element</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>run_config_name</name>
      </field>
      <field>
        <name>run_config_description</name>
      </field>
      <field>
        <name>gcp_worker_machine_type</name>
      </field>
      <field>
        <name>streaming_hop_transforms_buffer_size</name>
      </field>
      <field>
        <name>gcp_max_nb_workers</name>
      </field>
      <field>
        <name>gcp_zone</name>
      </field>
      <field>
        <name>gcp_project_id</name>
      </field>
      <field>
        <name>user_agent</name>
      </field>
      <field>
        <name>gcp_region</name>
      </field>
      <field>
        <name>plugins_to_stage</name>
      </field>
      <field>
        <name>temp_location</name>
      </field>
      <field>
        <name>gcp_worker_disk_type</name>
      </field>
      <field>
        <name>gcp_app_name</name>
      </field>
      <field>
        <name>gcp_initial_nb_workers</name>
      </field>
      <field>
        <name>gcp_staging_location</name>
      </field>
      <field>
        <name>fat_jar</name>
      </field>
      <field>
        <name>streaming_hop_transforms_flush_interval</name>
      </field>
      <field>
        <name>transform_plugin_classes</name>
      </field>
      <field>
        <name>gcp_disk_size_gb</name>
      </field>
      <field>
        <name>gcp_auto_scaling_algorithm</name>
      </field>
      <field>
        <name>xp_plugin_classes</name>
      </field>
      <field>
        <name>var_name</name>
      </field>
      <field>
        <name>var_description</name>
      </field>
      <field>
        <name>var_value</name>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <GUI>
      <xloc>704</xloc>
      <yloc>256</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
