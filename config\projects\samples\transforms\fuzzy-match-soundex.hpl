<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>fuzzy-match-soundex</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/04/30 17:23:29.449</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/04/30 17:23:29.449</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Fuzzy Match using Soundex function based on Similarly sounding phonetics
</note>
      <xloc>64</xloc>
      <yloc>32</yloc>
      <width>400</width>
      <heigth>41</heigth>
      <fontname>Segoe UI</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Fuzzy match</from>
      <to>Output</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Master Data</from>
      <to>Fuzzy match</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Reference Data</from>
      <to>Fuzzy match</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Output</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>432</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Fuzzy match</name>
    <type>FuzzyMatch</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <from>Reference Data</from>
    <lookupfield>Reference_Names</lookupfield>
    <mainstreamfield>Master Name</mainstreamfield>
    <outputmatchfield>match</outputmatchfield>
    <outputvaluefield>measure value</outputvaluefield>
    <caseSensitive>N</caseSensitive>
    <closervalue>Y</closervalue>
    <minimalValue>0</minimalValue>
    <maximalValue>1</maximalValue>
    <separator>,</separator>
    <algorithm>soundex</algorithm>
    <lookup>
    </lookup>
    <attributes/>
    <GUI>
      <xloc>240</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Master Data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>Master Name</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <data>
      <line>
        <item>Sure</item>
      </line>
      <line>
        <item>Dam</item>
      </line>
      <line>
        <item>Too</item>
      </line>
      <line>
        <item>Milton</item>
      </line>
      <line>
        <item>Colour</item>
      </line>
      <line>
        <item>Hole</item>
      </line>
      <line>
        <item>Hour</item>
      </line>
      <line>
        <item>Ay See Dee Ci</item>
      </line>
      <line>
        <item>Schafer</item>
      </line>
      <line>
        <item>Buda</item>
      </line>
      <line>
        <item>Raleigh</item>
      </line>
      <line>
        <item>Raliegh</item>
      </line>
      <line>
        <item>Ralehg</item>
      </line>
      <line>
        <item>Roleg</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>80</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Reference Data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>Reference_Names</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <data>
      <line>
        <item>Shore</item>
      </line>
      <line>
        <item>Damn</item>
      </line>
      <line>
        <item>Two</item>
      </line>
      <line>
        <item>Water</item>
      </line>
      <line>
        <item>Miltan</item>
      </line>
      <line>
        <item>Color</item>
      </line>
      <line>
        <item>Whole</item>
      </line>
      <line>
        <item>Our</item>
      </line>
      <line>
        <item>AC/DC</item>
      </line>
      <line>
        <item>Shaeffer</item>
      </line>
      <line>
        <item>Booda</item>
      </line>
      <line>
        <item>Raleigh</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>240</xloc>
      <yloc>240</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
