<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>beers-wikipedia-graph-output</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/07/30 11:36:17.713</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/07/30 11:36:17.713</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>/tmp/beers-wikipedia.hop</from>
      <to>Update Belgian Beers graph</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>/tmp/beers-wikipedia.hop</name>
    <type>CubeInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <file>
      <name>${java.io.tmpdir}/beers-wikipedia.hop</name>
    </file>
    <limit>0</limit>
    <addfilenameresult>N</addfilenameresult>
    <attributes/>
    <GUI>
      <xloc>128</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Update Belgian Beers graph</name>
    <type>Neo4jGraphOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>demo</connection>
    <model>Belgian Beers</model>
    <batch_size>1000</batch_size>
    <create_indexes>N</create_indexes>
    <returning_graph>N</returning_graph>
    <return_graph_field/>
    <validate_against_model>N</validate_against_model>
    <out_of_order_allowed>Y</out_of_order_allowed>
    <mappings>
      <mapping>
        <source_field>brand</source_field>
        <target_type>Node</target_type>
        <target_name>Brand</target_name>
        <target_property>name</target_property>
      </mapping>
      <mapping>
        <source_field>brewery</source_field>
        <target_type>Node</target_type>
        <target_name>Brewery</target_name>
        <target_property>name</target_property>
      </mapping>
      <mapping>
        <source_field>evaluation</source_field>
        <target_type>Node</target_type>
        <target_name>Evaluation</target_name>
        <target_property>label</target_property>
      </mapping>
      <mapping>
        <source_field>percentage</source_field>
        <target_type>Node</target_type>
        <target_name>Strength</target_name>
        <target_property>pct</target_property>
      </mapping>
      <mapping>
        <source_field>percentageLabel</source_field>
        <target_type>Relationship</target_type>
        <target_name>Strength - Evaluation</target_name>
        <target_property>percentage</target_property>
      </mapping>
      <mapping>
        <source_field>percentageLabel</source_field>
        <target_type>Node</target_type>
        <target_name>Strength</target_name>
        <target_property>label</target_property>
      </mapping>
      <mapping>
        <source_field>period</source_field>
        <target_type>Node</target_type>
        <target_name>Period</target_name>
        <target_property>description</target_property>
      </mapping>
      <mapping>
        <source_field>type</source_field>
        <target_type>Node</target_type>
        <target_name>Type</target_name>
        <target_property>name</target_property>
      </mapping>
    </mappings>
    <attributes/>
    <GUI>
      <xloc>368</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
