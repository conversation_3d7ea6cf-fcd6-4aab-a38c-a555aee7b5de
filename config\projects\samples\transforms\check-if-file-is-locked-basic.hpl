<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>check-if-file-is-locked-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/02/07 20:01:54.149</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/02/07 20:01:54.149</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>get customers-100.txt</from>
      <to>Check if file is locked</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Check if file is locked</from>
      <to>locked?</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>locked?</from>
      <to>log locked</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>locked?</from>
      <to>log not locked</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Check if file is locked</name>
    <type>FileLocked</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <addresultfilenames>N</addresultfilenames>
    <filenamefield>filename</filenamefield>
    <resultfieldname>result</resultfieldname>
    <attributes/>
    <GUI>
      <xloc>314</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>get customers-100.txt</name>
    <type>GetFileNames</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <doNotFailIfNoFile>N</doNotFailIfNoFile>
    <dynamic_include_subfolders>N</dynamic_include_subfolders>
    <exclude_wildcard_Field/>
    <file>
      <exclude_filemask/>
      <file_required>N</file_required>
      <filemask>customers-100.txt</filemask>
      <include_subfolders>Y</include_subfolders>
      <name>${PROJECT_HOME}</name>
    </file>
    <filefield>N</filefield>
    <filename_Field/>
    <filter>
      <filterfiletype>all_files</filterfiletype>
    </filter>
    <isaddresult>Y</isaddresult>
    <limit>0</limit>
    <raiseAnExceptionIfNoFile>N</raiseAnExceptionIfNoFile>
    <rownum>N</rownum>
    <rownum_field/>
    <wildcard_Field/>
    <attributes/>
    <GUI>
      <xloc>128</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>locked?</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compare>
      <condition>
        <conditions>
</conditions>
        <function>=</function>
        <leftvalue>result</leftvalue>
        <negated>N</negated>
        <operator>-</operator>
        <value>
          <isnull>N</isnull>
          <length>-1</length>
          <mask/>
          <name>constant</name>
          <precision>-1</precision>
          <text>Y</text>
          <type>Boolean</type>
        </value>
      </condition>
    </compare>
    <send_false_to>log not locked</send_false_to>
    <send_true_to>log locked</send_true_to>
    <attributes/>
    <GUI>
      <xloc>500</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>log locked</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage>the file is locked. </logmessage>
    <fields>
      </fields>
    <attributes/>
    <GUI>
      <xloc>686</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>log not locked</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage>the file is not locked
</logmessage>
    <fields>
      </fields>
    <attributes/>
    <GUI>
      <xloc>688</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
