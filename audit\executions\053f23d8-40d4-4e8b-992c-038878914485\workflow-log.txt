2025/09/03 13:31:14 - write-read-huge-file - Start of workflow execution
2025/09/03 13:31:14 - write-read-huge-file - Starting action [textfileoutput-huge-file.hpl]
2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - ERROR: Unable to run workflow write-read-huge-file. The textfileoutput-huge-file.hpl has an error. The pipeline path config/projects/default/workflows/textfileoutput-huge-file.hpl is invalid, and will not run successfully.
2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - ERROR: org.apache.hop.core.exception.HopXmlException: 
2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - The pipeline path config/projects/default/workflows/textfileoutput-huge-file.hpl is invalid, and will not run successfully.
2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - 
2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - 	at org.apache.hop.pipeline.PipelineMeta.loadXml(PipelineMeta.java:1676)
2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - 	at org.apache.hop.pipeline.PipelineMeta.<init>(PipelineMeta.java:1664)
2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - 	at org.apache.hop.workflow.actions.pipeline.ActionPipeline.getPipelineMeta(ActionPipeline.java:633)
2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - 	at org.apache.hop.workflow.actions.pipeline.ActionPipeline.execute(ActionPipeline.java:342)
2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - 	at org.apache.hop.workflow.Workflow.executeFromStart(Workflow.java:751)
2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - 	at org.apache.hop.workflow.Workflow.executeFromStart(Workflow.java:894)
2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - 	at org.apache.hop.workflow.Workflow.executeFromStart(Workflow.java:453)
2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - 	at org.apache.hop.workflow.Workflow.startExecution(Workflow.java:313)
2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - 	at org.apache.hop.workflow.engines.local.LocalWorkflowEngine.startExecution(LocalWorkflowEngine.java:248)
2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - 	at org.apache.hop.ui.hopgui.file.workflow.HopGuiWorkflowGraph.lambda$start$14(HopGuiWorkflowGraph.java:3714)
2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - 	at java.base/java.lang.Thread.run(Thread.java:842)
2025/09/03 13:31:14 - write-read-huge-file - Finished action [textfileoutput-huge-file.hpl] (result=[false])
2025/09/03 13:31:14 - write-read-huge-file - Workflow execution finished
