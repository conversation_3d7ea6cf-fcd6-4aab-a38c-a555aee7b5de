{"rdbms": {"H2": {"databaseName": "hop-samples", "pluginId": "H2", "accessType": 0, "hostname": "localhost", "password": "Encrypted ", "pluginName": "H2", "port": "8082", "attributes": {"SUPPORTS_TIMESTAMP_DATA_TYPE": "Y", "QUOTE_ALL_FIELDS": "N", "SUPPORTS_BOOLEAN_DATA_TYPE": "Y", "FORCE_IDENTIFIERS_TO_LOWERCASE": "N", "PRESERVE_RESERVED_WORD_CASE": "Y", "SQL_CONNECT": "CREATE TABLE IF NOT EXISTS PUBLIC.dim_sample\n(\n  scd_key IDENTITY\n, version INTEGER\n, date_from TIMESTAMP\n, date_to TIMESTAMP\n, id DOUBLE\n, name VA<PERSON>HA<PERSON>(50)\n, street VARCHAR(50)\n, number INT\n, zip VARCHAR(50)\n, city VARCHAR(50)\n)\n;\n\nCREATE TABLE IF NOT EXISTS PUBLIC.TABLEOUTPUT_BASIC\n(\n  ID INT\n, COL_BOOL BOOLEAN\n, COL_STRING TEXT\n, COL_DATE DATE\n)\n;\n\n\nCREATE TABLE IF NOT EXISTS PUBLIC.INSERTUPDATE\n(\n  ID INT\n, COL_BOOL BOOLEAN\n, COL_STRING TEXT\n, COL_DATE DATE\n)\n;\n\nCREATE TABLE IF NOT EXISTS PUBLIC.DBLOOKUP \n(\nID INT,\nDESCRIPTION TEXT\n);  \n\nTRUNCATE TABLE PUBLIC.DBLOOKUP; \n\nINSERT INTO PUBLIC.DBLOOKUP VALUES \n(1, 'value 01'),\n(2, 'value 02'),\n(3, 'value 03'),\n(4, 'value 04'),\n(5, 'value 05'),\n(6, 'value 06'),\n(7, 'value 07'),\n(8, 'value 08'),\n(9, 'value 09'),\n(10, 'value 10');\n\nCREATE TABLE IF NOT EXISTS PUBLIC.TABLEINPUT\n(\n  ID INT\n, COL_BOOL BOOLEAN\n, COL_STRING TEXT\n, COL_DATE DATE\n)\n;\n\nTRUNCATE TABLE PUBLIC.TABLEINPUT;\n\nINSERT INTO PUBLIC.TABLEINPUT VALUES \n(1, 'Y', 'value 01', '2021-01-01'),\n(2, 'Y', 'value 02', '2022-01-01'),\n(3, 'Y', 'value 03', '2023-01-01'),\n(4, 'Y', 'value 04', '2024-01-01'),\n(5, 'Y', 'value 05', '2025-01-01');\n\n", "FORCE_IDENTIFIERS_TO_UPPERCASE": "N", "PREFERRED_SCHEMA_NAME": ""}, "manualUrl": "jdbc:h2:mem:hop-samples;DB_CLOSE_DELAY=-1", "username": ""}}, "name": "hop-samples"}