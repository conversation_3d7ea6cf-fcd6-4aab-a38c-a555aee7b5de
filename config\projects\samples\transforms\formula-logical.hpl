<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>formula-logical</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2022/04/22 10:58:10.151</created_date>
    <modified_user>-</modified_user>
    <modified_date>2022/04/22 10:58:10.151</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>sample data</from>
      <to>Logical Formulas</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Logical Formulas</name>
    <type>Formula</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <formulas>
      <formula>
        <field_name>and_true</field_name>
        <formula>AND("true", TRUE())</formula>
        <replace_field/>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>4</value_type>
      </formula>
      <formula>
        <field_name>and_foo</field_name>
        <formula>AND([one], TRUE())</formula>
        <replace_field/>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>4</value_type>
      </formula>
      <formula>
        <field_name>one_equals_two</field_name>
        <formula>[one]=[two]</formula>
        <replace_field/>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>4</value_type>
      </formula>
      <formula>
        <field_name>not_one_equals_two</field_name>
        <formula>NOT([one]=[two])</formula>
        <replace_field/>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>4</value_type>
      </formula>
      <formula>
        <field_name>if_equals_then_else_simple</field_name>
        <formula>IF([one]=[two],[foo],[bar])</formula>
        <replace_field/>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>2</value_type>
      </formula>
      <formula>
        <field_name>or_two_comparisons</field_name>
        <formula>OR([one]=[two],[foo]=[bar])</formula>
        <replace_field/>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>4</value_type>
      </formula>
      <formula>
        <field_name>true</field_name>
        <formula>TRUE()</formula>
        <replace_field/>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>4</value_type>
      </formula>
      <formula>
        <field_name>false</field_name>
        <formula>FALSE()</formula>
        <replace_field/>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>4</value_type>
      </formula>
      <formula>
        <field_name>and_comparisons</field_name>
        <formula>AND([one] &lt;= [two], [foo] &lt;&gt; [bar])</formula>
        <replace_field/>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>4</value_type>
      </formula>
    </formulas>
    <attributes/>
    <GUI>
      <xloc>224</xloc>
      <yloc>64</yloc>
    </GUI>
  </transform>
  <transform>
    <name>sample data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>foo</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>bar</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>one</name>
        <precision>-1</precision>
        <type>Integer</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>two</name>
        <precision>-1</precision>
        <type>Integer</type>
      </field>
      <field>
        <set_empty_string>Y</set_empty_string>
        <length>-1</length>
        <name>empty</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
    </fields>
    <data>
      <line>
        <item>foo</item>
        <item>bar</item>
        <item>1</item>
        <item>2</item>
        <item/>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>80</xloc>
      <yloc>64</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
