<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>concat-fields-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/02/07 20:13:03.412</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/02/07 20:13:03.412</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>dummy data grid</from>
      <to>Concat Fields</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>dummy data grid</from>
      <to>Concat Fields Removed Source Fields</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>dummy data grid</name>
    <type>DataGrid</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>1</item>
        <item>two</item>
        <item>3</item>
        <item>four</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <currency/>
        <set_empty_string>N</set_empty_string>
        <name>one</name>
        <format/>
        <group/>
        <decimal/>
        <type>Integer</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <currency/>
        <set_empty_string>N</set_empty_string>
        <name>two</name>
        <format/>
        <group/>
        <decimal/>
        <type>String</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <currency/>
        <set_empty_string>N</set_empty_string>
        <name>three</name>
        <format/>
        <group/>
        <decimal/>
        <type>Integer</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <currency/>
        <set_empty_string>N</set_empty_string>
        <name>four</name>
        <format/>
        <group/>
        <decimal/>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>112</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Concat Fields</name>
    <type>ConcatFields</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <ConcatFields>
      <removeSelectedFields>N</removeSelectedFields>
      <targetFieldLength>0</targetFieldLength>
      <targetFieldName>concatenated</targetFieldName>
    </ConcatFields>
    <enclosure>"</enclosure>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <format>0</format>
        <group/>
        <length>-1</length>
        <name>one</name>
        <nullif/>
        <precision>-1</precision>
        <trim_type>both</trim_type>
        <type>Integer</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>two</name>
        <nullif/>
        <precision>-1</precision>
        <trim_type>both</trim_type>
        <type>String</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format>0</format>
        <group/>
        <length>-1</length>
        <name>three</name>
        <nullif/>
        <precision>-1</precision>
        <trim_type>both</trim_type>
        <type>Integer</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>four</name>
        <nullif/>
        <precision>-1</precision>
        <trim_type>both</trim_type>
        <type>String</type>
      </field>
    </fields>
    <separator>;</separator>
    <attributes/>
    <GUI>
      <xloc>448</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Concat Fields Removed Source Fields</name>
    <type>ConcatFields</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <ConcatFields>
      <removeSelectedFields>Y</removeSelectedFields>
      <targetFieldLength>0</targetFieldLength>
      <targetFieldName>concatenated_removed_source_fields</targetFieldName>
    </ConcatFields>
    <enclosure>"</enclosure>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <format>0</format>
        <group/>
        <length>-1</length>
        <name>one</name>
        <nullif/>
        <precision>-1</precision>
        <trim_type>both</trim_type>
        <type>Integer</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>two</name>
        <nullif/>
        <precision>-1</precision>
        <trim_type>both</trim_type>
        <type>String</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format>0</format>
        <group/>
        <length>-1</length>
        <name>three</name>
        <nullif/>
        <precision>-1</precision>
        <trim_type>both</trim_type>
        <type>Integer</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>four</name>
        <nullif/>
        <precision>-1</precision>
        <trim_type>both</trim_type>
        <type>String</type>
      </field>
    </fields>
    <separator>;</separator>
    <attributes/>
    <GUI>
      <xloc>448</xloc>
      <yloc>208</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
