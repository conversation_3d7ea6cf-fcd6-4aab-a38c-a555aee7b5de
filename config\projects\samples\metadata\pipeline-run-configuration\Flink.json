{"engineRunConfiguration": {"BeamFlinkPipelineEngine": {"streamingHopTransformsBufferSize": "", "flinkObjectReuse": "", "flinkMaxBundleSize": "", "flinkCheckpointingMode": "", "flinkExternalizedCheckpointsEnabled": "", "flinkCheckpointingInterval": "", "flinkShutdownSourcesAfterIdleMs": "", "flinkCheckpointTimeoutMillis": "", "flinkFailingOnCheckpointingErrors": "", "flinkAutoWatermarkInterval": "", "fatJar": "", "flinkMaxBundleTimeMills": "", "transformPluginClasses": "", "xpPluginClasses": "", "flinkNumberOfExecutionRetries": "", "flinkParallelism": "4", "flinkDisableMetrics": "", "userAgent": "Hop", "pluginsToStage": "", "flinkRetainExternalizedCheckpointsOnCancellation": "", "flinkExecutionModeForBatch": "", "tempLocation": "file:///tmp", "flinkMinPauseBetweenCheckpoints": "", "flinkMaster": "[auto]", "flinkExecutionRetryDelay": "", "streamingHopTransformsFlushInterval": "", "flinkLatencyTrackingInterval": ""}}, "configurationVariables": [{"name": "DATA_INPUT", "description": "", "value": "${PROJECT_HOME}/beam/input/customers-noheader-1k.txt"}, {"name": "STATE_INPUT", "description": "", "value": "${PROJECT_HOME}/beam/input/state-data.txt"}, {"name": "DATA_OUTPUT", "description": "", "value": "${PROJECT_HOME}/beam/output"}], "name": "Flink", "description": "", "executionInfoLocationName": "local-audit", "dataProfile": "first-last"}