<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>formula-datetime</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2022/04/22 14:00:46.314</created_date>
    <modified_user>-</modified_user>
    <modified_date>2022/04/22 14:00:46.314</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>generate 1 row</from>
      <to>get now</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>get now</from>
      <to>date time formulas</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>date time formulas</name>
    <type>Formula</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <formulas>
      <formula>
        <field_name>today</field_name>
        <formula>TODAY()</formula>
        <replace_field/>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>1</value_type>
      </formula>
      <formula>
        <field_name>tomorrow</field_name>
        <formula>TODAY()+1</formula>
        <replace_field/>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>1</value_type>
      </formula>
      <formula>
        <field_name>days_since_graduation</field_name>
        <formula>TODAY()-DATE(2021,12,17)</formula>
        <replace_field/>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>5</value_type>
      </formula>
      <formula>
        <field_name>workdays_since_graduation</field_name>
        <formula>NETWORKDAYS(DATE(2021,12,17), TODAY())</formula>
        <replace_field/>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>5</value_type>
      </formula>
      <formula>
        <field_name>todays_year</field_name>
        <formula>YEAR(TODAY())</formula>
        <replace_field/>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>5</value_type>
      </formula>
      <formula>
        <field_name>todays_month</field_name>
        <formula>MONTH(TODAY())</formula>
        <replace_field/>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>5</value_type>
      </formula>
      <formula>
        <field_name>current_month</field_name>
        <formula>MONTH([now])</formula>
        <replace_field>one</replace_field>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>5</value_type>
      </formula>
      <formula>
        <field_name>current_minute</field_name>
        <formula>MINUTE([now])</formula>
        <replace_field/>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>5</value_type>
      </formula>
      <formula>
        <field_name>current_second</field_name>
        <formula>SECOND([now])</formula>
        <replace_field/>
        <value_length>-1</value_length>
        <value_precision>-1</value_precision>
        <value_type>5</value_type>
      </formula>
    </formulas>
    <attributes/>
    <GUI>
      <xloc>384</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>generate 1 row</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <length>-1</length>
        <name>one</name>
        <nullif>1</nullif>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <type>Integer</type>
      </field>
      <field>
        <length>-1</length>
        <name>three</name>
        <nullif>3</nullif>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <type>Integer</type>
      </field>
    </fields>
    <interval_in_ms>5000</interval_in_ms>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <limit>1</limit>
    <never_ending>N</never_ending>
    <row_time_field>now</row_time_field>
    <attributes/>
    <GUI>
      <xloc>144</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>get now</name>
    <type>SystemInfo</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>now</name>
        <type>system date (variable)</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>256</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
