/*! *******************************************************************************
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 ******************************************************************************/

body {
    font-size: 14px;
    margin: 0;
    font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Sans serif;
}

.hop-page-background {
    background: #FFF;
}

#pucHeader {
    display: block;
    height: 50px;
    background: #c9e8fb;
    filter: none;
    -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
    -moz-box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
    padding: 30px 0 0;
    position: relative;
    border-top: 6px solid #5cc0c4;

    background: url(hop_logo.svg) no-repeat top right;
    background-size: contain;
}

.workspaceHeading {
    color: #0e3a5a;
    font-size: 24px;
    padding: 0 0 20px 0;
    cursor: default;
}

.toolbar, table.toolbar {
    width: 100%;
    height: 100%;
    vertical-align: middle;
    background-color: #c9e8fb;
    border-top: none;
    border-left: none;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: #0E3A5A;
}

.toolbar-button {
    background-color: transparent;
    border: 1px solid rgba(0, 0, 0, 0);
    margin: 0;
    width: 22px;
    height: 22px;
    -webkit-user-select: none;
    outline: none;
    margin-left: 5px !important;
}

.toolbar-button.toolbar-button-hovering {
    border: 1px solid #5cc0c4;
    background: none;
    margin: 0;
    width: 22px;
    height: 22px;
    outline: none;
}

.toolbar-button-disabled img {
    cursor: default !important;
}

.toolbar-button img {
    display: block;
}

.custom-dropdown-popup {
    z-index: 100;
    -webkit-box-shadow: 2px 3px 4px 0 rgba(0, 0, 0, .2);
    box-shadow: 2px 3px 4px 0 rgba(0, 0, 0, .2);
}

.custom-dropdown-popup .gwt-MenuBar-vertical {
    padding: 10px 0 0 0;
    border-left: 1px solid #0E3A5A;
    border-right: 1px solid #0E3A5A;
    border-bottom: 1px solid #0E3A5A;
    border-top: 0 solid transparent;
    background-color: #FFF;
    margin-top: 0;
    margin-left: 0;
}

.custom-dropdown-popup .gwt-MenuBar-vertical .gwt-MenuItem {
    padding: 8px 5px 8px 10px;
    text-align: left;
    width: 242px;
    font-size: 14px;
    color: #414141;
}

td.gwt-MenuItem {
    cursor: pointer;
}

.gwt-MenuBar-vertical tr:hover .gwt-MenuItem-selected,
.gwt-MenuBar-vertical .gwt-MenuItem-selected {
    background-color: #c9e8fb;
    color: #FFF;
}

.hop-dialog {
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.hop-dialog .Caption {
    background: none;
    font-size: 24px;
    line-height: 30px;
    color: #333;
    padding-bottom: 20px;
}

.hop-dialog .dialog-content {
    color: #333;
}

button.hop-button {
    background: white;
    filter: none;
    font-size: 14px;
    line-height: 20px;
    color: #cc0000;
    padding: 9px 14px;
    border-radius: 4px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    white-space: nowrap;
    cursor: pointer;
    margin: 0;
    outline: 0 none;
    border: 1px solid #cc0000;
}

button.hop-button:hover {
    background: #5cc0c4;
    border-color: #5cc0c4;
    color: #FFF;
}

.hop-table {
    border-spacing: 0;
    background-color: #c9e8fb;
}

.hop-table .cellTableHeader {
    padding: 3px 15px;
    text-align: left;
    color: #0e3a5a;
    overflow: hidden;
}

.hop-table th {
    font-size: 12px;
    border-spacing: 0;
    padding: 11px 8px 12px 8px;
    font-weight: normal;
    background-color: #c9e8fb;
    color: #0e3a5a;
    border: 1px solid #cccccc;
    border-right: 0 none;
}

.hop-table th:last-child {
    border-right: 1px solid #99BEDB;
}

.hop-table .cellTableEvenRow {
    background: #ffffff;
}

.hop-table .cellTableOddRow {
    background: #fafafa;
}

.hop-table .cellTableEvenRowCell, .hop-table .cellTableOddRowCell {
    border: 1px solid #dedede;
    border-right: 0 none;
    border-top: 0 none;
}

.hop-table .cellTableCell {
    padding: 8px 10px 10px 10px;
    overflow: hidden;
    text-align: left;
    font-size: 12px;
}

.hop-table .cellTableEvenRow.cellTableHoveredRow, .hop-table .cellTableOddRow.cellTableHoveredRow {
    background-color: #E0E0E0;
}

.hop-table .cellTableEvenRowCell.cellTableLastColumn, .hop-table .cellTableOddRowCell.cellTableLastColumn {
    border-right: 1px solid #dedede;
}

.hop-table .cellTableSelectedRow {
    background-color: rgba(204, 0, 0, 0.2);
    color: #000;
}

.hop-table td, tr.cellTableRow, td.gwt-MenuItem, .toolbar-button:not(.toolbar-button-disabled) {
    cursor: pointer;
}

.toolbar-button-disabled {
    opacity: 0.4;
}

div#messageDialogBody:first-letter {
    text-transform: capitalize;
}

a, a:visited {
    color: #1973bc;
}

a, a:link {
    color: #7C0B2B;
    text-decoration: none;
}

textarea {
    border: 1px solid #0E3A5A;
    padding: 0 10px;
    font-size: 14px;
    line-height: 17px;
    border-radius: 4px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    height: 33px;
    color: #333333;
}

