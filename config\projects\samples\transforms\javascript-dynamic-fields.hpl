<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>javascript-dynamic-fields</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/09/07 21:12:02.010</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/09/07 21:12:02.010</modified_date>
  </info>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Noto Sans</fontname>
      <fontsize>10</fontsize>
      <height>27</height>
      <xloc>112</xloc>
      <yloc>80</yloc>
      <note>read the field names and values in Javascript and combine field names and values as header + data rows that are written to a CSV file. </note>
      <width>753</width>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>dummy data</from>
      <to>read field values dynamically</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>dummy data</from>
      <to>read field names dynamically</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>read field names dynamically</from>
      <to>unique values (single row)</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>unique values (single row)</from>
      <to>rename field_names </to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>read field values dynamically</from>
      <to>rename field_values</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>rename field_names </from>
      <to>append streams</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>rename field_values</from>
      <to>append streams</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>append streams</from>
      <to>write csv</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>append streams</name>
    <type>Append</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <head_name>rename field_names </head_name>
    <tail_name>rename field_values</tail_name>
    <attributes/>
    <GUI>
      <xloc>1056</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform>
    <name>dummy data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>1</item>
        <item>foo 1</item>
        <item>bar 1</item>
      </line>
      <line>
        <item>2</item>
        <item>foo 2</item>
        <item>bar 2</item>
      </line>
      <line>
        <item>3</item>
        <item>foo 3</item>
        <item>bar 3</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>id</name>
        <type>Integer</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>foo</name>
        <type>String</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>bar</name>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>128</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform>
    <name>read field names dynamically</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>//Script here

var field_names_concat = '';
var rowMeta = getInputRowMeta();
var valueMetaList = rowMeta.getValueMetaList();
var iterator = valueMetaList.iterator();

while (iterator.hasNext()) {
    var valueMetaInterface = iterator.next();
    var fieldName = valueMetaInterface.getName();
    var typeIndex = valueMetaInterface.getType();
    var typeName = org.apache.hop.core.row.IValueMeta.typeCodes[typeIndex];

    field_names_concat += String(fieldName) + ';';
    // optionally add the field type. 
    // field_names += String(typeName);
}
</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>field_names_concat</name>
        <rename>field_names_concat</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>368</xloc>
      <yloc>160</yloc>
    </GUI>
  </transform>
  <transform>
    <name>read field values dynamically</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>

var fields_concat = ''; 
var nb_fields = getInputRowMeta().size(); 

for(i=0; i &lt; getInputRowMeta().size(); i++){
   var valueMeta = getInputRowMeta().getValueMeta(i);
  fields_concat += valueMeta.getString(row[i]) + ';'; 
}</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>nb_fields</name>
        <rename>nb_fields</rename>
        <type>Integer</type>
        <length>9</length>
        <precision>0</precision>
        <replace>N</replace>
      </field>
      <field>
        <name>fields_concat</name>
        <rename>fields_concat</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>368</xloc>
      <yloc>272</yloc>
    </GUI>
  </transform>
  <transform>
    <name>rename field_names </name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>field_names_concat</name>
        <rename>row</rename>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <GUI>
      <xloc>848</xloc>
      <yloc>160</yloc>
    </GUI>
  </transform>
  <transform>
    <name>rename field_values</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>fields_concat</name>
        <rename>row</rename>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <GUI>
      <xloc>848</xloc>
      <yloc>272</yloc>
    </GUI>
  </transform>
  <transform>
    <name>unique values (single row)</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>${java.io.tmpdir}</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>Y</unique_rows>
    <fields>
      <field>
        <name>field_names_concat</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>640</xloc>
      <yloc>160</yloc>
    </GUI>
  </transform>
  <transform>
    <name>write csv</name>
    <type>TextFileOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <separator/>
    <enclosure/>
    <enclosure_forced>N</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>N</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding>US-ASCII</encoding>
    <endedLine/>
    <fileNameInField>N</fileNameInField>
    <fileNameField/>
    <create_parent_folder>Y</create_parent_folder>
    <file>
      <name>${java.io.tmpdir}/dynamic-fields</name>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>Y</do_not_open_new_file_init>
      <extention>csv</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format/>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>N</fast_dump>
      <splitevery/>
    </file>
    <fields>
      <field>
        <name>row</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>1216</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
