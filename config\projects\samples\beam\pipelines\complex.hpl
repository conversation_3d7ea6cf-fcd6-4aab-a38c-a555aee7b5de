<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>complex</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2020/07/27 14:56:38.114</created_date>
    <modified_user>-</modified_user>
    <modified_date>2020/07/27 14:56:38.114</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>Customer data</from>
      <to>Merge join</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>State data</from>
      <to>uppercase state</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>uppercase state</from>
      <to>Merge join</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Customer data</from>
      <to>countPerState</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>countPerState</from>
      <to>Lookup count per state</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Merge join</from>
      <to>Lookup count per state</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Lookup count per state</from>
      <to>name&lt;n</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>name&lt;n</from>
      <to>Label: A-M</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>name&lt;n</from>
      <to>Label: N-Z</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Label: A-M</from>
      <to>Switch / case</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Label: N-Z</from>
      <to>Switch / case</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch / case</from>
      <to>CA</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch / case</from>
      <to>FL</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch / case</from>
      <to>NY</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Switch / case</from>
      <to>Default</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>CA</from>
      <to>Collect</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>FL</from>
      <to>Collect</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>NY</from>
      <to>Collect</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Default</from>
      <to>Collect</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Collect</from>
      <to>complex</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>CA</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>Comment</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>Some comment about California</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>864</xloc>
      <yloc>32</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Collect</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>1008</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Customer data</name>
    <type>BeamInput</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <input_location>${DATA_INPUT}</input_location>
    <file_description_name>Customers</file_description_name>
    <attributes/>
    <GUI>
      <xloc>144</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Default</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>Comment</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>no comment.</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>864</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform>
    <name>FL</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>Comment</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>Some remark on Floridians</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>864</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Label: A-M</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>label</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>A-M</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>608</xloc>
      <yloc>32</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Label: N-Z</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>label</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>N-Z</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>608</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Lookup count per state</name>
    <type>StreamLookup</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <from>countPerState</from>
    <input_sorted>N</input_sorted>
    <preserve_memory>N</preserve_memory>
    <sorted_list>N</sorted_list>
    <integer_pair>Y</integer_pair>
    <lookup>
      <key>
        <name>stateCode</name>
        <field>stateCode</field>
      </key>
      <value>
        <name>countPerState</name>
        <rename>nrPerState</rename>
        <default/>
        <type>Integer</type>
      </value>
    </lookup>
    <attributes/>
    <GUI>
      <xloc>400</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Merge join</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>LEFT OUTER</join_type>
    <transform1>Customer data</transform1>
    <transform2>uppercase state</transform2>
    <keys_1>
      <key>state</key>
    </keys_1>
    <keys_2>
      <key>state</key>
    </keys_2>
    <attributes/>
    <GUI>
      <xloc>288</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform>
    <name>NY</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>Comment</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>New York rocks!</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>864</xloc>
      <yloc>160</yloc>
    </GUI>
  </transform>
  <transform>
    <name>State data</name>
    <type>BeamInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <input_location>${STATE_INPUT}</input_location>
    <file_description_name>State population data</file_description_name>
    <attributes/>
    <GUI>
      <xloc>144</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Switch / case</name>
    <type>SwitchCase</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fieldname>stateCode</fieldname>
    <use_contains>N</use_contains>
    <case_value_type>String</case_value_type>
    <case_value_format/>
    <case_value_decimal/>
    <case_value_group/>
    <default_target_transform>Default</default_target_transform>
    <cases>
      <case>
        <value>CA</value>
        <target_transform>CA</target_transform>
      </case>
      <case>
        <value>FL</value>
        <target_transform>FL</target_transform>
      </case>
      <case>
        <value>NY</value>
        <target_transform>NY</target_transform>
      </case>
    </cases>
    <attributes/>
    <GUI>
      <xloc>704</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform>
    <name>complex</name>
    <type>BeamOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <output_location>${DATA_OUTPUT}</output_location>
    <file_description_name/>
    <file_prefix>complex</file_prefix>
    <file_suffix>.csv</file_suffix>
    <windowed>N</windowed>
    <attributes/>
    <GUI>
      <xloc>1152</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform>
    <name>countPerState</name>
    <type>MemoryGroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>stateCode</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>countPerState</aggregate>
        <subject>id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>288</xloc>
      <yloc>16</yloc>
    </GUI>
  </transform>
  <transform>
    <name>name&lt;n</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <send_true_to>Label: A-M</send_true_to>
    <send_false_to>Label: N-Z</send_false_to>
    <compare>
      <condition>
        <negated>N</negated>
        <leftvalue>Last name</leftvalue>
        <function>&lt;</function>
        <rightvalue/>
        <value>
          <name>constant</name>
          <type>String</type>
          <text>n</text>
          <length>-1</length>
          <precision>-1</precision>
          <isnull>N</isnull>
          <mask/>
        </value>
      </condition>
    </compare>
    <attributes/>
    <GUI>
      <xloc>512</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform>
    <name>uppercase state</name>
    <type>StringOperations</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <in_stream_name>state</in_stream_name>
        <out_stream_name/>
        <trim_type>none</trim_type>
        <lower_upper>upper</lower_upper>
        <padding_type>none</padding_type>
        <pad_char/>
        <pad_len/>
        <init_cap>no</init_cap>
        <mask_xml>none</mask_xml>
        <digits>none</digits>
        <remove_special_characters>none</remove_special_characters>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>288</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
