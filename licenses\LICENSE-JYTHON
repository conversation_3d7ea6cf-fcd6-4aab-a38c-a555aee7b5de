
<PERSON><PERSON> TERMS AND CONDITIONS FOR ACCESSING OR OTHERWISE USING JYTHON
=======================================================

PYTHON SOFTWARE FOUNDATION LICENSE VERSION 2
--------------------------------------------

1. This LICENSE AGREEMENT is between the Python Software Foundation
("PSF"), and the Individual or Organization ("Licensee") accessing and
otherwise using this software ("Jython") in source or binary form and
its associated documentation.

2. Subject to the terms and conditions of this License Agreement, PSF
hereby grants Licensee a nonexclusive, royalty-free, world-wide
license to reproduce, analyze, test, perform and/or display publicly,
prepare derivative works, distribute, and otherwise use Jython alone
or in any derivative version, provided, however, that PSF's License
Agreement and PSF's notice of copyright, i.e., "Copyright (c) 2007
Python Software Foundation; All Rights Reserved" are retained in
<PERSON>yt<PERSON> alone or in any derivative version prepared by Licensee.

3. In the event Licensee prepares a derivative work that is based on
or incorporates Jython or any part thereof, and wants to make
the derivative work available to others as provided herein, then
Licensee hereby agrees to include in any such work a brief summary of
the changes made to Jython.

4. PSF is making Jython available to Licensee on an "AS IS"
basis.  PSF MAKES NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR
IMPLIED.  BY WAY OF EXAMPLE, BUT NOT LIMITATION, PSF MAKES NO AND
DISCLAIMS ANY REPRESENTATION OR WARRANTY OF MERCHANTABILITY OR FITNESS
FOR ANY PARTICULAR PURPOSE OR THAT THE USE OF JYTHON WILL NOT
INFRINGE ANY THIRD PARTY RIGHTS.

5. PSF SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER USERS OF JYTHON
FOR ANY INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES OR LOSS AS
A RESULT OF MODIFYING, DISTRIBUTING, OR OTHERWISE USING JYTHON,
OR ANY DERIVATIVE THEREOF, EVEN IF ADVISED OF THE POSSIBILITY THEREOF.

6. This License Agreement will automatically terminate upon a material
breach of its terms and conditions.

7. Nothing in this License Agreement shall be deemed to create any
relationship of agency, partnership, or joint venture between PSF and
Licensee.  This License Agreement does not grant permission to use PSF
trademarks or trade name in a trademark sense to endorse or promote
products or services of Licensee, or any third party.

8. By copying, installing or otherwise using Jython, Licensee
agrees to be bound by the terms and conditions of this License
Agreement.
 
Jython 2.0, 2.1 License
================================

Copyright (c) 2000-2009 Jython Developers.
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

 - Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

 - Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in
   the documentation and/or other materials provided with the distribution.

 - Neither the name of the Jython Developers nor the names of
   its contributors may be used to endorse or promote products
   derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE REGENTS OR
CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.




JPython 1.1.x Software License.
=========================

______________________________________________________________________

IMPORTANT: PLEASE READ THE FOLLOWING AGREEMENT CAREFULLY.

BY CLICKING ON THE "ACCEPT" BUTTON WHERE INDICATED, OR BY INSTALLING,
COPYING OR OTHERWISE USING THE SOFTWARE, YOU ARE DEEMED TO HAVE AGREED TO
THE TERMS AND CONDITIONS OF THIS AGREEMENT.

______________________________________________________________________

JPython version 1.1.x

  1. This LICENSE AGREEMENT is between the Corporation for National Research
     Initiatives, having an office at 1895 Preston White Drive, Reston, VA
     20191 ("CNRI"), and the Individual or Organization ("Licensee")
     accessing and using JPython version 1.1.x in source or binary form and
     its associated documentation as provided herein ("Software").

  2. Subject to the terms and conditions of this License Agreement, CNRI
     hereby grants Licensee a non-exclusive, non-transferable, royalty-free,
     world-wide license to reproduce, analyze, test, perform and/or display
     publicly, prepare derivative works, distribute, and otherwise use the
     Software alone or in any derivative version, provided, however, that
     CNRI's License Agreement and CNRI's notice of copyright, i.e.,
     "Copyright (c)1996-1999 Corporation for National Research Initiatives;
     All Rights Reserved" are both retained in the Software, alone or in any
     derivative version prepared by Licensee.

     Alternatively, in lieu of CNRI's License Agreement, Licensee may
     substitute the following text (omitting the quotes), provided, however,
     that such text is displayed prominently in the Software alone or in any
     derivative version prepared by Licensee: "JPython (Version 1.1.x) is
     made available subject to the terms and conditions in CNRI's License
     Agreement. This Agreement may be located on the Internet using the
     following unique, persistent identifier (known as a handle):
     1895.22/1006. The License may also be obtained from a proxy server on
     the Web using the following URL: http://hdl.handle.net/1895.22/1006."

  3. In the event Licensee prepares a derivative work that is based on or
     incorporates the Software or any part thereof, and wants to make the
     derivative work available to the public as provided herein, then
     Licensee hereby agrees to indicate in any such work, in a prominently
     visible way, the nature of the modifications made to CNRI's Software.

  4. Licensee may not use CNRI trademarks or trade name, including JPython
     or CNRI, in a trademark sense to endorse or promote products or
     services of Licensee, or any third party. Licensee may use the mark
     JPython in connection with Licensee's derivative versions that are
     based on or incorporate the Software, but only in the form
     "JPython-based ___________________," or equivalent.

  5. CNRI is making the Software available to Licensee on an "AS IS" basis.
     CNRI MAKES NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR IMPLIED. BY WAY
     OF EXAMPLE, BUT NOT LIMITATION, CNRI MAKES NO AND DISCLAIMS ANY
     REPRESENTATION OR WARRANTY OF MERCHANTABILITY OR FITNESS FOR ANY
     PARTICULAR PURPOSE OR THAT THE USE OF THE SOFTWARE WILL NOT INFRINGE
     ANY THIRD PARTY RIGHTS.

  6. CNRI SHALL NOT BE LIABLE TO LICENSEE OR OTHER USERS OF THE SOFTWARE FOR
     ANY INCIDENTAL, SPECIAL OR CONSEQUENTIAL DAMAGES OR LOSS AS A RESULT OF
     USING, MODIFYING OR DISTRIBUTING THE SOFTWARE, OR ANY DERIVATIVE
     THEREOF, EVEN IF ADVISED OF THE POSSIBILITY THEREOF. SOME STATES DO NOT
     ALLOW THE LIMITATION OR EXCLUSION OF LIABILITY SO THE ABOVE DISCLAIMER
     MAY NOT APPLY TO LICENSEE.

  7. This License Agreement may be terminated by CNRI (i) immediately upon
     written notice from CNRI of any material breach by the Licensee, if the
     nature of the breach is such that it cannot be promptly remedied; or
     (ii) sixty (60) days following notice from CNRI to Licensee of a
     material remediable breach, if Licensee has not remedied such breach
     within that sixty-day period.

  8. This License Agreement shall be governed by and interpreted in all
     respects by the law of the State of Virginia, excluding conflict of law
     provisions. Nothing in this Agreement shall be deemed to create any
     relationship of agency, partnership, or joint venture between CNRI and
     Licensee.

  9. By clicking on the "ACCEPT" button where indicated, or by installing,
     copying or otherwise using the Software, Licensee agrees to be bound by
     the terms and conditions of this License Agreement.

                               [ACCEPT BUTTON]

B. HISTORY OF THE SOFTWARE
=======================================================

JPython was created in late 1997 by Jim Hugunin. Jim was also the
primary developer while he was at CNRI. In February 1999 Barry Warsaw
took over as primary developer and released JPython version 1.1.

In October 2000 Barry helped move the software to SourceForge
where it was renamed to Jython. Jython 2.0 and 2.1 were developed
under the Jython specific license below.

From the 2.2 release on, Jython contributors have licensed their
contributions to the Python Software Foundation under a Contributor
Agreement, so as to permit distribution under the Python Software Foundation
license.

The Python standard library developed for CPython is also used in Jython, and
(like Jython itself) is provided under the Python Software Foundation
license. See the file LICENSE_CPython.txt for details.

The zxJDBC package was written by Brian Zimmer and originally licensed
under the GNU Public License. The package is now licensed to the Python
Software Foundation under a Contributor Agreement, so as to permit
distribution under the Python Software Foundation license.

Elements of the supporting libraries (appearing renamed in some Jython JARs)
are covered by the Apache Software License.  See the file LICENSE_Apache.txt
for details.

