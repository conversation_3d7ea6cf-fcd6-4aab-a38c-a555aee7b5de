<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>delete-3-customers-json</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <pipeline_status>0</pipeline_status>
    <parameters>
      <parameter>
        <name>NAME1</name>
        <default_value>uvhph-name</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>NAME2</name>
        <default_value>qfwcj-name</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>NAME3</name>
        <default_value>xtukc-name</default_value>
        <description/>
      </parameter>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2022/07/11 10:26:42.842</created_date>
    <modified_user>-</modified_user>
    <modified_date>2022/07/11 10:26:42.842</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>generate 1 row</from>
      <to>delete 3 customers</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>delete 3 customers</name>
    <type>MongoDbDelete</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <collection>m_inserts_1</collection>
    <connection>mongo</connection>
    <execute_for_each_row>N</execute_for_each_row>
    <json_query>{$or: [{"name": "${NAME1}"},{"name": "${NAME2}"}, {"name": "${NAME3}"} ]}</json_query>
    <retries>5</retries>
    <read_preference/>
    <journaled_writes>N</journaled_writes>
    <use_json_query>Y</use_json_query>
    <write_timeout/>
    <write_concern/>
    <write_retries>5</write_retries>
    <retry_delay>10</retry_delay>
    <attributes/>
    <GUI>
      <xloc>320</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>generate 1 row</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
</fields>
    <interval_in_ms>5000</interval_in_ms>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <never_ending>N</never_ending>
    <limit>1</limit>
    <row_time_field>now</row_time_field>
    <attributes/>
    <GUI>
      <xloc>176</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
