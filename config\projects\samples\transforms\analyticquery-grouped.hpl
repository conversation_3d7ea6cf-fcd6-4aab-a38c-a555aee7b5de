<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>analyticquery-grouped</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/07/21 09:56:48.454</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/07/21 09:56:48.454</modified_date>
    <key_for_session_key/>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Fetches the previous and next description from a set of rows, grouped by id</note>
      <xloc>128</xloc>
      <yloc>96</yloc>
      <width>425</width>
      <heigth>27</heigth>
      <fontname>Noto Sans</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Test Data</from>
      <to>Previous and next rows</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Previous and next rows</from>
      <to>Output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Test Data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>id</name>
        <precision>-1</precision>
        <type>Integer</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>desc</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
    </fields>
    <data>
      <line>
        <item>1</item>
        <item>1 1</item>
      </line>
      <line>
        <item>1</item>
        <item>1 2</item>
      </line>
      <line>
        <item>1</item>
        <item>1 3</item>
      </line>
      <line>
        <item>2</item>
        <item>2 1</item>
      </line>
      <line>
        <item>2</item>
        <item>2 2</item>
      </line>
      <line>
        <item>2</item>
        <item>2 3</item>
      </line>
      <line>
        <item>3</item>
        <item>3 1</item>
      </line>
      <line>
        <item>3</item>
        <item>3 2</item>
      </line>
      <line>
        <item>3</item>
        <item>3 3</item>
      </line>
      <line>
        <item>4</item>
        <item>4 1</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>144</xloc>
      <yloc>160</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Previous and next rows</name>
    <type>AnalyticQuery</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <group>
      <field>
        <name>id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>prev_desc</aggregate>
        <type>LAG</type>
        <subject>desc</subject>
        <valuefield>1</valuefield>
      </field>
      <field>
        <aggregate>next_desc</aggregate>
        <type>LEAD</type>
        <subject>desc</subject>
        <valuefield>1</valuefield>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>312</xloc>
      <yloc>160</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>480</xloc>
      <yloc>160</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
