/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

When finding issues please log a bug in GitHub https://github.com/apache/hop/issues
When you have a question you can use our mailing list (http://hop.apache.org/community/mailing-list/) or GitHub Discussions (https://github.com/apache/hop/discussions)


Installation Instruction:
    - Have a recent Java 17 Runtime
    
Windows:
    - To start the GUI run hop-gui.bat

OS X / Linux:
    - To start the GUI run hop-gui.sh



   This distribution includes cryptographic software.  The country in 
   which you currently reside may have restrictions on the import, 
   possession, use, and/or re-export to another country, of 
   encryption software. BEFORE using any encryption software, please 
   check your country's laws, regulations and policies concerning the
   import, possession, or use, and re-export of encryption software, to 
   see if this is permitted. See http://www.wassenaar.org for
   more information.
The Apache Software Foundation has classified this software as Export Commodity 
   Control Number (ECCN) 5D002, which includes information security
   software using or performing cryptographic functions with asymmetric
   algorithms. The form and manner of this Apache Software Foundation
   distribution makes it eligible for export under the "publicly available"
   Section 742.15(b) exemption (see the BIS Export Administration Regulations, 
   Section 742.15(b)) for both object code and source code.
The following provides more details on the included cryptographic
   software:
    PGP Encrypt - Decrypt transforms/actions
    Apache Kafka Transforms
    Apache POI Transforms
    MQTT Transforms
