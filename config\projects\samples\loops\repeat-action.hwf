<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<workflow>
  <name>repeat-action</name>
  <name_sync_with_filename>Y</name_sync_with_filename>
  <description/>
  <extended_description/>
  <workflow_version/>
  <created_user>-</created_user>
  <created_date>2023/03/06 13:40:43.811</created_date>
  <modified_user>-</modified_user>
  <modified_date>2023/03/06 13:40:43.811</modified_date>
  <parameters>
    </parameters>
  <actions>
    <action>
      <name>Start</name>
      <description/>
      <type>SPECIAL</type>
      <attributes/>
      <DayOfMonth>1</DayOfMonth>
      <hour>12</hour>
      <intervalMinutes>60</intervalMinutes>
      <intervalSeconds>0</intervalSeconds>
      <minutes>0</minutes>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <weekDay>1</weekDay>
      <parallel>N</parallel>
      <xloc>50</xloc>
      <yloc>50</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>repeat pipeline</name>
      <description/>
      <type>Repeat</type>
      <attributes/>
      <filename>${PROJECT_HOME}/loops/child-check-set-counter-value.hpl</filename>
      <run_configuration>local</run_configuration>
      <variable_name>END_LOOP</variable_name>
      <variable_value/>
      <delay/>
      <keep_values>N</keep_values>
      <logfile_enabled>N</logfile_enabled>
      <logfile_appended>Y</logfile_appended>
      <logfile_base/>
      <logfile_extension>log</logfile_extension>
      <logfile_add_date>Y</logfile_add_date>
      <logfile_add_time>N</logfile_add_time>
      <logfile_add_repetition>N</logfile_add_repetition>
      <logfile_update_interval>5000</logfile_update_interval>
      <parameters/>
      <parallel>N</parallel>
      <xloc>416</xloc>
      <yloc>48</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>set ${COUNTER} = 1</name>
      <description/>
      <type>SET_VARIABLES</type>
      <attributes/>
      <replacevars>Y</replacevars>
      <filename/>
      <file_variable_type>JVM</file_variable_type>
      <fields>
        <field>
          <variable_name>COUNTER</variable_name>
          <variable_value>1</variable_value>
          <variable_type>CURRENT_WORKFLOW</variable_type>
        </field>
      </fields>
      <parallel>N</parallel>
      <xloc>224</xloc>
      <yloc>48</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>repeat workflow</name>
      <description/>
      <type>Repeat</type>
      <attributes/>
      <filename>${PROJECT_HOME}/loops/end-repeat-pipeline.hwf</filename>
      <run_configuration>local</run_configuration>
      <variable_name>END_LOOP</variable_name>
      <variable_value/>
      <delay/>
      <keep_values>N</keep_values>
      <logfile_enabled>N</logfile_enabled>
      <logfile_appended>Y</logfile_appended>
      <logfile_base/>
      <logfile_extension>log</logfile_extension>
      <logfile_add_date>Y</logfile_add_date>
      <logfile_add_time>N</logfile_add_time>
      <logfile_add_repetition>N</logfile_add_repetition>
      <logfile_update_interval>5000</logfile_update_interval>
      <parameters/>
      <parallel>N</parallel>
      <xloc>416</xloc>
      <yloc>176</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>reset ${COUNTER} to 1</name>
      <description/>
      <type>SET_VARIABLES</type>
      <attributes/>
      <replacevars>Y</replacevars>
      <filename/>
      <file_variable_type>JVM</file_variable_type>
      <fields>
        <field>
          <variable_name>COUNTER</variable_name>
          <variable_value>1</variable_value>
          <variable_type>JVM</variable_type>
        </field>
      </fields>
      <parallel>N</parallel>
      <xloc>224</xloc>
      <yloc>176</yloc>
      <attributes_hac/>
    </action>
  </actions>
  <hops>
    <hop>
      <from>Start</from>
      <to>set ${COUNTER} = 1</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>Y</unconditional>
    </hop>
    <hop>
      <from>set ${COUNTER} = 1</from>
      <to>repeat pipeline</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
    <hop>
      <from>repeat pipeline</from>
      <to>reset ${COUNTER} to 1</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
    <hop>
      <from>reset ${COUNTER} to 1</from>
      <to>repeat workflow</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
  </hops>
  <notepads>
  </notepads>
  <attributes/>
</workflow>
