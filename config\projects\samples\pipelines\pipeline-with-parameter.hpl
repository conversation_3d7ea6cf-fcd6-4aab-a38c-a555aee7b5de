<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>pipeline-with-parameter</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <pipeline_status>0</pipeline_status>
    <parameters>
      <parameter>
        <name>PRM_EXAMPLE</name>
        <default_value/>
        <description/>
      </parameter>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/05/08 08:19:58.557</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/05/08 08:19:58.557</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>get ${PRM_EXAMPLE}</from>
      <to>write parameter to log</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>write parameter to log</from>
      <to>get ${ENV_VARIABLE}</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>get ${ENV_VARIABLE}</from>
      <to>write env_variable to log</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>get ${PRM_EXAMPLE}</name>
    <type>GetVariable</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <length>-1</length>
        <name>example</name>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <type>String</type>
        <variable>${PRM_EXAMPLE}</variable>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>192</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>write parameter to log</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage>we received '${PRM_EXAMPLE}' as the value for this pipeline's parameter.</logmessage>
    <fields>
      <field>
        <name>example</name>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>384</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>get ${ENV_VARIABLE}</name>
    <type>GetVariable</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>env_variable</name>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <type>String</type>
        <variable>${ENV_VARIABLE}</variable>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>384</xloc>
      <yloc>208</yloc>
    </GUI>
  </transform>
  <transform>
    <name>write env_variable to log</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage>the value for variable ENV_VARIABLE is ${ENV_VARIABLE}</logmessage>
    <fields>
      <field>
        <name>env_variable</name>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>592</xloc>
      <yloc>208</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
