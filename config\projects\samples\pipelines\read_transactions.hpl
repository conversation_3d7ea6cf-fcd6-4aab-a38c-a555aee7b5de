<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>read_transactions</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/09/16 22:31:19.820</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/09/16 22:31:19.820</modified_date>
  </info>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Noto Sans</fontname>
      <fontsize>10</fontsize>
      <height>44</height>
      <xloc>96</xloc>
      <yloc>32</yloc>
      <note>This pipeline splits "Headers" and "Data" , then look up Headers to construct a JSON for each xlsx. 
In the JSON doesn't matter the fields order, then extract the fields with JSON input</note>
      <width>551</width>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>sheet_row == 1</from>
      <to>Headers</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>sheet_row == 1</from>
      <to>Data</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Data</from>
      <to>Block Headers</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Headers</from>
      <to>Lookup Header Label</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Block Headers</from>
      <to>Lookup Header Label</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Lookup Header Label</from>
      <to>DATA_LINE</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>DATA_LINE</from>
      <to>Group by DATA_FILE</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Group by DATA_FILE</from>
      <to>JSON_FILE</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>JSON_FILE</from>
      <to>JSON input</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>JSON input</from>
      <to>FINAL_RESULT</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Microsoft Excel input</from>
      <to>sheet_row == 1</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Block Headers</name>
    <type>BlockUntilTransformsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <transforms>
      <transform>
        <name>Headers</name>
      </transform>
    </transforms>
    <attributes/>
    <GUI>
      <xloc>576</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>DATA_LINE</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>//Script here

var DATA_LINE=
 '{'
    + '"' + L1 + '":"' + C1 + '",'
    + '"' + L2 + '":"' + C2 + '",'
    + '"' + L3 + '":"' + C3 + '",'
    + '"' + L4 + '":"' + C4 + '"'
+ '}' 
     </jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>DATA_LINE</name>
        <rename>DATA_LINE</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>960</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Data</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>432</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>FINAL_RESULT</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>short_filename</name>
      </field>
      <field>
        <name>Account</name>
      </field>
      <field>
        <name>Amount</name>
      </field>
      <field>
        <name>Company</name>
      </field>
      <field>
        <name>Report_Date</name>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <GUI>
      <xloc>960</xloc>
      <yloc>480</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Group by DATA_FILE</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <add_linenr>N</add_linenr>
    <all_rows>N</all_rows>
    <directory>${java.io.tmpdir}</directory>
    <fields>
      <field>
        <aggregate>DATA_FILE</aggregate>
        <subject>DATA_LINE</subject>
        <type>CONCAT_COMMA</type>
      </field>
    </fields>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>short_filename</name>
      </field>
    </group>
    <ignore_aggregate>N</ignore_aggregate>
    <prefix>grp</prefix>
    <attributes/>
    <GUI>
      <xloc>960</xloc>
      <yloc>208</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Headers</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>432</xloc>
      <yloc>208</yloc>
    </GUI>
  </transform>
  <transform>
    <name>JSON input</name>
    <type>JsonInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <include>N</include>
    <include_field/>
    <rownum>N</rownum>
    <addresultfile>N</addresultfile>
    <readurl>N</readurl>
    <removeSourceField>N</removeSourceField>
    <IsIgnoreEmptyFile>N</IsIgnoreEmptyFile>
    <doNotFailIfNoFile>Y</doNotFailIfNoFile>
    <ignoreMissingPath>Y</ignoreMissingPath>
    <defaultPathLeafToNull>Y</defaultPathLeafToNull>
    <rownum_field/>
    <file>
      <name/>
      <filemask/>
      <exclude_filemask/>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
    </file>
    <fields>
      <field>
        <name>Account</name>
        <path>$.*.Account</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>Amount</name>
        <path>$.*.Amount</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>Company</name>
        <path>$.*.Company</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>Report_Date</name>
        <path>$.*.Report_Date</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
    </fields>
    <limit>0</limit>
    <IsInFields>Y</IsInFields>
    <IsAFile>N</IsAFile>
    <valueField>JSON_FILE</valueField>
    <shortFileFieldName/>
    <pathFieldName/>
    <hiddenFieldName/>
    <lastModificationTimeFieldName/>
    <uriNameFieldName/>
    <rootUriNameFieldName/>
    <extensionFieldName/>
    <sizeFieldName/>
    <attributes/>
    <GUI>
      <xloc>960</xloc>
      <yloc>400</yloc>
    </GUI>
  </transform>
  <transform>
    <name>JSON_FILE</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>//Script here

var JSON_FILE = '[' + DATA_FILE + ']'</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>JSON_FILE</name>
        <rename>JSON_FILE</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>960</xloc>
      <yloc>304</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Lookup Header Label</name>
    <type>StreamLookup</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <from>Headers</from>
    <input_sorted>N</input_sorted>
    <preserve_memory>Y</preserve_memory>
    <sorted_list>N</sorted_list>
    <integer_pair>N</integer_pair>
    <lookup>
      <key>
        <name>short_filename</name>
        <field>short_filename</field>
      </key>
      <value>
        <name>C1</name>
        <rename>L1</rename>
        <default/>
        <type>String</type>
      </value>
      <value>
        <name>C2</name>
        <rename>L2</rename>
        <default/>
        <type>String</type>
      </value>
      <value>
        <name>C3</name>
        <rename>L3</rename>
        <default/>
        <type>String</type>
      </value>
      <value>
        <name>C4</name>
        <rename>L4</rename>
        <default/>
        <type>String</type>
      </value>
    </lookup>
    <attributes/>
    <GUI>
      <xloc>768</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Microsoft Excel input</name>
    <type>ExcelInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <accept_field/>
    <accept_filenames>N</accept_filenames>
    <accept_transform_name/>
    <add_to_result_filenames>Y</add_to_result_filenames>
    <bad_line_files_destination_directory/>
    <bad_line_files_extension>warning</bad_line_files_extension>
    <encoding/>
    <error_ignored>N</error_ignored>
    <error_line_files_destination_directory/>
    <error_line_files_extension>error</error_line_files_extension>
    <error_line_skipped>N</error_line_skipped>
    <extensionFieldName/>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>C1</name>
        <precision>-1</precision>
        <repeat>N</repeat>
        <trim_type>none</trim_type>
        <type>String</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>C2</name>
        <precision>-1</precision>
        <repeat>N</repeat>
        <trim_type>none</trim_type>
        <type>String</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>C3</name>
        <precision>-1</precision>
        <repeat>N</repeat>
        <trim_type>none</trim_type>
        <type>String</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>C4</name>
        <precision>-1</precision>
        <repeat>N</repeat>
        <trim_type>none</trim_type>
        <type>String</type>
      </field>
    </fields>
    <file>
      <exclude_filemask/>
      <file_required>N</file_required>
      <filemask>file_.*.xlsx</filemask>
      <include_subfolders>N</include_subfolders>
      <name>${PROJECT_HOME}/pipelines/input/</name>
    </file>
    <filefield/>
    <header>N</header>
    <hiddenFieldName/>
    <lastModificationTimeFieldName/>
    <limit>0</limit>
    <line_number_files_destination_directory/>
    <line_number_files_extension>line</line_number_files_extension>
    <noempty>Y</noempty>
    <pathFieldName/>
    <rootUriNameFieldName/>
    <rownumfield/>
    <sheetfield/>
    <sheetrownumfield>sheet_row</sheetrownumfield>
    <sheets>
</sheets>
    <shortFileFieldName>short_filename</shortFileFieldName>
    <sizeFieldName/>
    <spreadsheet_type>SAX_POI</spreadsheet_type>
    <stoponempty>N</stoponempty>
    <strict_types>N</strict_types>
    <uriNameFieldName/>
    <attributes/>
    <GUI>
      <xloc>112</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>sheet_row == 1</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compare>
      <condition>
        <conditions>
</conditions>
        <function>=</function>
        <leftvalue>sheet_row</leftvalue>
        <negated>N</negated>
        <operator>-</operator>
        <value>
          <isnull>N</isnull>
          <length>-1</length>
          <mask>####0;-####0</mask>
          <name>constant</name>
          <precision>0</precision>
          <text>1</text>
          <type>Integer</type>
        </value>
      </condition>
    </compare>
    <send_false_to>Data</send_false_to>
    <send_true_to>Headers</send_true_to>
    <attributes/>
    <GUI>
      <xloc>288</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
