{"executionType": "Pipeline", "parentId": null, "id": "d6331a9b-b5ef-47d5-8668-0718f83ee299", "name": "simple_mapping_test", "copyNr": null, "loggingText": "2025/09/04 09:25:12 - simple_mapping_test - Executing this pipeline using the Local Pipeline Engine with run configuration 'local'\r\n2025/09/04 09:25:12 - simple_mapping_test - 为了 Pipeline 解除补丁开始 [simple_mapping_test]\r\n2025/09/04 09:25:12 - CSV输入.0 - ERROR: Unexpected error\r\n2025/09/04 09:25:12 - CSV输入.0 - ERROR: org.apache.hop.core.exception.HopFileException: \r\n2025/09/04 09:25:12 - CSV输入.0 - \r\n2025/09/04 09:25:12 - CSV输入.0 - 创建字段映射时意外出错\r\n2025/09/04 09:25:12 - CSV输入.0 - Could not read from \"file:///F:/hu/H-Documentation/database/新建文件夹/hop/datasets/source_table.csv\" because it is not a file.\r\n2025/09/04 09:25:12 - CSV输入.0 - \r\n2025/09/04 09:25:12 - CSV输入.0 - Could not read from \"file:///F:/hu/H-Documentation/database/新建文件夹/hop/datasets/source_table.csv\" because it is not a file.\r\n2025/09/04 09:25:12 - CSV输入.0 - \r\n2025/09/04 09:25:12 - CSV输入.0 - \tat org.apache.hop.pipeline.transforms.csvinput.CsvInput.readFieldNamesFromFile(CsvInput.java:495)\r\n2025/09/04 09:25:12 - CSV输入.0 - \tat org.apache.hop.pipeline.transforms.csvinput.CsvInput.createFieldMapping(CsvInput.java:449)\r\n2025/09/04 09:25:12 - CSV输入.0 - \tat org.apache.hop.pipeline.transforms.csvinput.CsvInput.openNextFile(CsvInput.java:330)\r\n2025/09/04 09:25:12 - CSV输入.0 - \tat org.apache.hop.pipeline.transforms.csvinput.CsvInput.processRow(CsvInput.java:122)\r\n2025/09/04 09:25:12 - CSV输入.0 - \tat org.apache.hop.pipeline.transform.RunThread.run(RunThread.java:54)\r\n2025/09/04 09:25:12 - CSV输入.0 - \tat java.base/java.lang.Thread.run(Thread.java:842)\r\n2025/09/04 09:25:12 - CSV输入.0 - Caused by: org.apache.commons.vfs2.FileNotFoundException: Could not read from \"file:///F:/hu/H-Documentation/database/新建文件夹/hop/datasets/source_table.csv\" because it is not a file.\r\n2025/09/04 09:25:12 - CSV输入.0 - \tat org.apache.commons.vfs2.provider.AbstractFileObject.getInputStream(AbstractFileObject.java:1202)\r\n2025/09/04 09:25:12 - CSV输入.0 - \tat org.apache.commons.vfs2.provider.AbstractFileObject.getInputStream(AbstractFileObject.java:1187)\r\n2025", "lastLogLineNr": 25, "metrics": [{"componentName": "CSV输入", "componentCopy": "0", "metrics": {"Read": 0, "Buffers Output": 0, "Errors": 1, "Input": 0, "Written": 0, "Updated": 0, "Output": 0, "Rejected": 0, "Buffers Input": 0}}, {"componentName": "字段映射", "componentCopy": "0", "metrics": {"Read": 0, "Buffers Output": 0, "Errors": 0, "Input": 0, "Written": 0, "Updated": 0, "Output": 0, "Rejected": 0, "Buffers Input": 0}}, {"componentName": "CSV输出", "componentCopy": "0", "metrics": {"Read": 0, "Buffers Output": 0, "Errors": 0, "Input": 0, "Written": 0, "Updated": 0, "Output": 0, "Rejected": 0, "Buffers Input": 0}}], "statusDescription": "Finished (with errors)", "updateTime": 1756949112297, "childIds": ["a4a06d54-e4f0-4f43-a61a-42c7158ec6cb", "faafb041-184e-4c20-bfc7-6e7f0681b085", "a679aed3-b292-4857-bca2-1f174861d4b4"], "details": {}, "failed": true, "executionEndDate": 1756949112294, "containerId": "44f9804d-f366-43f0-8f74-b17241d62c99"}