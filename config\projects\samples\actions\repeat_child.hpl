<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>repeat_child</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2022/04/11 09:14:15.582</created_date>
    <modified_user>-</modified_user>
    <modified_date>2022/04/11 09:14:15.582</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>This is a child pipeline called by repeat_action.hwf</note>
      <xloc>64</xloc>
      <yloc>48</yloc>
      <width>315</width>
      <heigth>26</heigth>
      <fontname>.AppleSystemUIFont</fontname>
      <fontsize>13</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>generate dummy row</from>
      <to>get counter variable</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>counter + 1</from>
      <to>counter >5</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>counter >5</from>
      <to>set counter variable</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>get counter variable</from>
      <to>write counter to log</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>write counter to log</from>
      <to>counter + 1</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>counter >5</from>
      <to>set end loop</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>counter + 1</name>
    <type>Calculator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <failIfNoFile>Y</failIfNoFile>
    <calculation>
      <calc_type>CONSTANT</calc_type>
      <field_a>1</field_a>
      <field_name>C_1</field_name>
      <remove>N</remove>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <value_type>Integer</value_type>
    </calculation>
    <calculation>
      <calc_type>ADD</calc_type>
      <field_a>counter</field_a>
      <field_b>C_1</field_b>
      <field_name>new_counter</field_name>
      <remove>N</remove>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <value_type>Integer</value_type>
    </calculation>
    <attributes/>
    <GUI>
      <xloc>624</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>counter >5</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <send_true_to>set end loop</send_true_to>
    <send_false_to>set counter variable</send_false_to>
    <compare>
      <condition>
        <negated>N</negated>
        <leftvalue>new_counter</leftvalue>
        <function>&gt;</function>
        <rightvalue/>
        <value>
          <name>constant</name>
          <type>Integer</type>
          <text>5</text>
          <length>-1</length>
          <precision>0</precision>
          <isnull>N</isnull>
          <mask>####0;-####0</mask>
        </value>
      </condition>
    </compare>
    <attributes/>
    <GUI>
      <xloc>800</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>generate dummy row</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
</fields>
    <interval_in_ms>5000</interval_in_ms>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <never_ending>N</never_ending>
    <limit>1</limit>
    <row_time_field>now</row_time_field>
    <attributes/>
    <GUI>
      <xloc>80</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>get counter variable</name>
    <type>GetVariable</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>counter</name>
        <variable>${COUNTER}</variable>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>256</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>set counter variable</name>
    <type>SetVariable</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <field_name>new_counter</field_name>
        <variable_name>COUNTER</variable_name>
        <variable_type>PARENT_WORKFLOW</variable_type>
        <default_value/>
      </field>
    </fields>
    <use_formatting>Y</use_formatting>
    <attributes/>
    <GUI>
      <xloc>992</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>set end loop</name>
    <type>SetVariable</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <field_name>new_counter</field_name>
        <variable_name>END_LOOP</variable_name>
        <variable_type>PARENT_WORKFLOW</variable_type>
        <default_value/>
      </field>
    </fields>
    <use_formatting>Y</use_formatting>
    <attributes/>
    <GUI>
      <xloc>800</xloc>
      <yloc>256</yloc>
    </GUI>
  </transform>
  <transform>
    <name>write counter to log</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage>counter value is: ${COUNTER} </logmessage>
    <fields>
      </fields>
    <attributes/>
    <GUI>
      <xloc>448</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
