<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>javascript-get-path</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/09/15 16:29:27.177</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/09/15 16:29:27.177</modified_date>
  </info>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Noto Sans</fontname>
      <fontsize>10</fontsize>
      <height>61</height>
      <xloc>112</xloc>
      <yloc>176</yloc>
      <note>This sample uses javascript to read a system variable ("PATH" in this case) as a field into the pipeline. 

</note>
      <width>571</width>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>generate 1 row</from>
      <to>read environment variable</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>read environment variable</from>
      <to>Write to log</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Write to log</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage/>
    <fields>
      </fields>
    <attributes/>
    <GUI>
      <xloc>480</xloc>
      <yloc>256</yloc>
    </GUI>
  </transform>
  <transform>
    <name>generate 1 row</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
</fields>
    <interval_in_ms>5000</interval_in_ms>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <limit>1</limit>
    <never_ending>N</never_ending>
    <row_time_field>now</row_time_field>
    <attributes/>
    <GUI>
      <xloc>128</xloc>
      <yloc>256</yloc>
    </GUI>
  </transform>
  <transform>
    <name>read environment variable</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>//Script here

var path = java.lang.System.getenv("PATH"); </jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>path</name>
        <rename>path</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>N</replace>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>304</xloc>
      <yloc>256</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
