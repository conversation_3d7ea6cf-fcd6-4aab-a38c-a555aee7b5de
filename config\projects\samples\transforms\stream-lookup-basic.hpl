<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>stream-lookup-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/07/14 17:06:55.768</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/07/14 17:06:55.768</modified_date>
  </info>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Noto Sans</fontname>
      <fontsize>11</fontsize>
      <height>27</height>
      <xloc>96</xloc>
      <yloc>32</yloc>
      <note>Read "Test Data" and lookup the desc field from the corresponding "Test Data Lookup" data grid through the "Lookup desc" Stream Lookup.</note>
      <width>780</width>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Test Data</from>
      <to>Lookup desc</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Lookup desc</from>
      <to>Output</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Test Data Lookup</from>
      <to>Lookup desc</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Lookup desc</name>
    <type>StreamLookup</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <from>Test Data Lookup</from>
    <input_sorted>N</input_sorted>
    <preserve_memory>Y</preserve_memory>
    <sorted_list>N</sorted_list>
    <integer_pair>N</integer_pair>
    <lookup>
      <key>
        <name>id</name>
        <field>id</field>
      </key>
      <value>
        <name>desc</name>
        <rename>desc</rename>
        <default/>
        <type>Integer</type>
      </value>
    </lookup>
    <attributes/>
    <GUI>
      <xloc>320</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>496</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Test Data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>1</item>
      </line>
      <line>
        <item>2</item>
      </line>
      <line>
        <item>3</item>
      </line>
      <line>
        <item>4</item>
      </line>
      <line>
        <item>5</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>id</name>
        <type>Integer</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>144</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Test Data Lookup</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>1</item>
        <item>one</item>
      </line>
      <line>
        <item>2</item>
        <item>two</item>
      </line>
      <line>
        <item>3</item>
        <item>three</item>
      </line>
      <line>
        <item>4</item>
        <item>four</item>
      </line>
      <line>
        <item>5</item>
        <item>five</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>id</name>
        <type>Integer</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>desc</name>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>320</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
