<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<workflow>
  <name>javascript_parse_action_logs</name>
  <name_sync_with_filename>Y</name_sync_with_filename>
  <description/>
  <extended_description/>
  <workflow_version/>
  <created_user>-</created_user>
  <created_date>2024/05/23 07:55:45.794</created_date>
  <modified_user>-</modified_user>
  <modified_date>2024/05/23 07:55:45.794</modified_date>
  <parameters>
    </parameters>
  <actions>
    <action>
      <name>Start</name>
      <description/>
      <type>SPECIAL</type>
      <attributes/>
      <DayOfMonth>1</DayOfMonth>
      <hour>12</hour>
      <intervalMinutes>60</intervalMinutes>
      <intervalSeconds>0</intervalSeconds>
      <minutes>0</minutes>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <weekDay>1</weekDay>
      <parallel>N</parallel>
      <xloc>50</xloc>
      <yloc>50</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>Dummy</name>
      <description/>
      <type>DUMMY</type>
      <attributes/>
      <parallel>N</parallel>
      <xloc>480</xloc>
      <yloc>48</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>set ${logging_text}</name>
      <description/>
      <type>EVAL</type>
      <attributes/>
      <script>var logging_text = previous_result.getLogText(); 
if(logging_text != null &amp;&amp; logging_text != ''){
  logging_text_array = logging_text.split('\n');
  parent_workflow.setVariable('logging_text', logging_text_array[0]);
}else{
  parent_workflow.setVariable('logging_text', 'no logging text received'); 
}
true;</script>
      <parallel>N</parallel>
      <xloc>272</xloc>
      <yloc>160</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>Write to log</name>
      <description/>
      <type>WRITE_TO_LOG</type>
      <attributes/>
      <loglevel>Basic</loglevel>
      <logmessage>####################################
logging text: ${logging_text}
####################################
</logmessage>
      <logsubject/>
      <parallel>N</parallel>
      <xloc>272</xloc>
      <yloc>272</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>Simple evaluation</name>
      <description/>
      <type>SIMPLE_EVAL</type>
      <attributes/>
      <fieldname>VAR_DOES_NOT_EXIST</fieldname>
      <fieldtype>string</fieldtype>
      <successbooleancondition>true</successbooleancondition>
      <successcondition>equal</successcondition>
      <successnumbercondition>equal</successnumbercondition>
      <successwhenvarset>Y</successwhenvarset>
      <valuetype>variable</valuetype>
      <parallel>N</parallel>
      <xloc>272</xloc>
      <yloc>48</yloc>
      <attributes_hac/>
    </action>
  </actions>
  <hops>
    <hop>
      <from>set ${logging_text}</from>
      <to>Write to log</to>
      <enabled>Y</enabled>
      <evaluation>N</evaluation>
      <unconditional>Y</unconditional>
    </hop>
    <hop>
      <from>Start</from>
      <to>Simple evaluation</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>Y</unconditional>
    </hop>
    <hop>
      <from>Simple evaluation</from>
      <to>Dummy</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
    <hop>
      <from>Simple evaluation</from>
      <to>set ${logging_text}</to>
      <enabled>Y</enabled>
      <evaluation>N</evaluation>
      <unconditional>N</unconditional>
    </hop>
  </hops>
  <notepads>
  </notepads>
  <attributes/>
</workflow>
