<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>join_rows</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2025/01/30 11:17:29.800</created_date>
    <modified_user>-</modified_user>
    <modified_date>2025/01/30 11:17:29.800</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>Data grid</from>
      <to>Join </to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Data grid 2</from>
      <to>Join </to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Data grid 3</from>
      <to>Join rows with condition</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Data grid 2 2</from>
      <to>Join rows with condition</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Data grid</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>a</item>
      </line>
      <line>
        <item>b</item>
      </line>
      <line>
        <item>c</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>fiedl1</name>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>240</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Data grid 2</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>a</item>
      </line>
      <line>
        <item>b</item>
      </line>
      <line>
        <item>c</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>field2</name>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>240</xloc>
      <yloc>272</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Data grid 2 2</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>a</item>
      </line>
      <line>
        <item>b</item>
      </line>
      <line>
        <item>c</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>field2</name>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>256</xloc>
      <yloc>592</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Data grid 3</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>a</item>
      </line>
      <line>
        <item>b</item>
      </line>
      <line>
        <item>c</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>fiedl1</name>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>256</xloc>
      <yloc>432</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Join </name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main/>
    <compare>
      <condition>
        <conditions>
</conditions>
        <function>=</function>
        <negated>N</negated>
        <operator>-</operator>
      </condition>
    </compare>
    <attributes/>
    <GUI>
      <xloc>480</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Join rows with condition</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main/>
    <compare>
      <condition>
        <conditions>
          <condition>
            <conditions>
</conditions>
            <function>=</function>
            <leftvalue>fiedl1</leftvalue>
            <negated>N</negated>
            <operator>-</operator>
            <rightvalue>field2</rightvalue>
          </condition>
          <condition>
            <conditions>
</conditions>
            <function>=</function>
            <leftvalue>fiedl1</leftvalue>
            <negated>N</negated>
            <operator>AND</operator>
            <value>
              <isnull>N</isnull>
              <length>-1</length>
              <mask/>
              <name>constant</name>
              <precision>-1</precision>
              <text>a</text>
              <type>String</type>
            </value>
          </condition>
        </conditions>
        <function>=</function>
        <leftvalue>fiedl1</leftvalue>
        <negated>N</negated>
        <operator>-</operator>
        <rightvalue>field2</rightvalue>
      </condition>
    </compare>
    <attributes/>
    <GUI>
      <xloc>496</xloc>
      <yloc>544</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
