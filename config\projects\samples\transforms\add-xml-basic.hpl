<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>add-xml-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/02/05 14:30:26.547</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/02/05 14:30:26.547</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>Data grid</from>
      <to>add XML</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Data grid</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>1</item>
        <item>foo</item>
        <item>bar</item>
        <item>foobar</item>
      </line>
      <line>
        <item>2</item>
        <item>bar</item>
        <item>foo</item>
        <item>barfoo</item>
      </line>
      <line>
        <item>3</item>
        <item>hello</item>
        <item>world</item>
        <item>hello word</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <currency/>
        <set_empty_string>N</set_empty_string>
        <name>id</name>
        <format/>
        <group/>
        <decimal/>
        <type>Integer</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <currency/>
        <set_empty_string>N</set_empty_string>
        <name>description</name>
        <format/>
        <group/>
        <decimal/>
        <type>String</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <currency/>
        <set_empty_string>N</set_empty_string>
        <name>type</name>
        <format/>
        <group/>
        <decimal/>
        <type>String</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <currency/>
        <set_empty_string>N</set_empty_string>
        <name>group</name>
        <format/>
        <group/>
        <decimal/>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>80</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform>
    <name>add XML</name>
    <type>AddXML</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <encoding>UTF-8</encoding>
    <valueName>xmlvaluename</valueName>
    <xml_repeat_element>row</xml_repeat_element>
    <file>
      <omitXMLheader>Y</omitXMLheader>
      <omitNullValues>N</omitNullValues>
    </file>
    <fields>
      <field>
        <name>id</name>
        <element/>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <length>-1</length>
        <precision>-1</precision>
        <attribute>N</attribute>
        <attributeParentName/>
      </field>
      <field>
        <name>description</name>
        <element/>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <length>-1</length>
        <precision>-1</precision>
        <attribute>N</attribute>
        <attributeParentName/>
      </field>
      <field>
        <name>type</name>
        <element/>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <length>-1</length>
        <precision>-1</precision>
        <attribute>N</attribute>
        <attributeParentName/>
      </field>
      <field>
        <name>group</name>
        <element/>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <length>-1</length>
        <precision>-1</precision>
        <attribute>Y</attribute>
        <attributeParentName/>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>256</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
