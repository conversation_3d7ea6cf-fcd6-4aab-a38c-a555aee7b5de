<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>loops-get-rows-from-result</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <pipeline_status>0</pipeline_status>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/03/18 08:55:04.218</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/03/18 08:55:04.218</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>get rows from result</from>
      <to>count rows received</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>count rows received</from>
      <to>log nb_rows</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>count rows received</name>
    <type>MemoryGroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <aggregate>nb_rows</aggregate>
        <subject>random_string</subject>
        <type>COUNT_DISTINCT</type>
      </field>
    </fields>
    <give_back_row>N</give_back_row>
    <group>
</group>
    <attributes/>
    <GUI>
      <xloc>360</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform>
    <name>get rows from result</name>
    <type>RowsFromResult</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>random_string</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>160</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform>
    <name>log nb_rows</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage/>
    <fields>
      <field>
        <name>nb_rows</name>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>560</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
