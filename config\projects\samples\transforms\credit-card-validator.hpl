<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>credit-card-validator</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/02/07 20:16:56.583</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/02/07 20:16:56.583</modified_date>
  </info>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Noto Sans</fontname>
      <fontsize>10</fontsize>
      <height>61</height>
      <xloc>80</xloc>
      <yloc>16</yloc>
      <note>credit card numbers (Mastercard and Visa) generated with https://www.getcreditcardnumbers.com/

card nb 12 (card ****************) had one digit manually changed to make the validation fail. </note>
      <width>564</width>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>credit cards </from>
      <to>Credit card validator</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Credit card validator</from>
      <to>valid?</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>valid?</from>
      <to>log 19 valid cards</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>valid?</from>
      <to>log 1 invalid card</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>credit cards </name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>****************</item>
      </line>
      <line>
        <item>****************</item>
      </line>
      <line>
        <item>****************</item>
      </line>
      <line>
        <item>****************</item>
      </line>
      <line>
        <item>****************</item>
      </line>
      <line>
        <item>****************</item>
      </line>
      <line>
        <item>****************</item>
      </line>
      <line>
        <item>****************</item>
      </line>
      <line>
        <item>****************</item>
      </line>
      <line>
        <item>****************</item>
      </line>
      <line>
        <item>****************</item>
      </line>
      <line>
        <item>****************</item>
      </line>
      <line>
        <item>4539873075591042</item>
      </line>
      <line>
        <item>4916595101857351</item>
      </line>
      <line>
        <item>4532020233002655</item>
      </line>
      <line>
        <item>****************</item>
      </line>
      <line>
        <item>****************</item>
      </line>
      <line>
        <item>****************</item>
      </line>
      <line>
        <item>****************</item>
      </line>
      <line>
        <item>****************</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <currency/>
        <set_empty_string>N</set_empty_string>
        <name>creditcard</name>
        <format/>
        <group/>
        <decimal/>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>144</xloc>
      <yloc>176</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Credit card validator</name>
    <type>CreditCardValidator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <cardtype>card type</cardtype>
    <fieldname>creditcard</fieldname>
    <notvalidmsg>not valid message</notvalidmsg>
    <onlydigits>N</onlydigits>
    <resultfieldname>result</resultfieldname>
    <attributes/>
    <GUI>
      <xloc>336</xloc>
      <yloc>176</yloc>
    </GUI>
  </transform>
  <transform>
    <name>valid?</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compare>
      <condition>
        <conditions>
</conditions>
        <function>IS NULL</function>
        <leftvalue>not valid message</leftvalue>
        <negated>N</negated>
        <operator>-</operator>
      </condition>
    </compare>
    <send_false_to>log 1 invalid card</send_false_to>
    <send_true_to>log 19 valid cards</send_true_to>
    <attributes/>
    <GUI>
      <xloc>512</xloc>
      <yloc>176</yloc>
    </GUI>
  </transform>
  <transform>
    <name>log 19 valid cards</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage>valid card numbers: </logmessage>
    <fields>
      <field>
        <name>creditcard</name>
      </field>
      <field>
        <name>result</name>
      </field>
      <field>
        <name>card type</name>
      </field>
      <field>
        <name>not valid message</name>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>656</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>log 1 invalid card</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage>invalid card numbers: </logmessage>
    <fields>
      <field>
        <name>creditcard</name>
      </field>
      <field>
        <name>result</name>
      </field>
      <field>
        <name>card type</name>
      </field>
      <field>
        <name>not valid message</name>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>656</xloc>
      <yloc>240</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
