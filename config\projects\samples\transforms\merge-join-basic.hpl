<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>merge-join-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/07/14 17:23:00.744</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/07/14 17:23:00.744</modified_date>
    <key_for_session_key/>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Join the rows from "Data Set 1" and "Data Set 2", based on the "id" field. The 4 Merge Join transforms show the possible join behavior (similar to SQL): 
*) INNER
*) LEFT OUTER 
*) RIGHT OUTER 
*) FULL OUTER</note>
      <xloc>144</xloc>
      <yloc>64</yloc>
      <width>837</width>
      <heigth>95</heigth>
      <fontname>Noto Sans</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Test Data 1</from>
      <to>Merge join Left Outer</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Test Data 2</from>
      <to>Merge join Left Outer</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Test Data 2</from>
      <to>Merge join Full Outer</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Test Data 1</from>
      <to>Merge join Right Outer</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Test Data 2</from>
      <to>Merge join Right Outer</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Test Data 1</from>
      <to>Merge join Full Outer</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Test Data 1</from>
      <to>Merge join Inner</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Test Data 2</from>
      <to>Merge join Inner</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Merge join Inner</from>
      <to>Output Inner</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Merge join Left Outer</from>
      <to>Output Left Outer</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Merge join Right Outer</from>
      <to>Output Right Outer</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Merge join Full Outer</from>
      <to>Output Full Outer</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Test Data 1</name>
    <type>DataGrid</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>id</name>
        <precision>-1</precision>
        <type>Integer</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>desc_1</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
    </fields>
    <data>
      <line>
        <item>1</item>
        <item>one</item>
      </line>
      <line>
        <item>2</item>
        <item>two</item>
      </line>
      <line>
        <item>3</item>
        <item>three</item>
      </line>
      <line>
        <item>4</item>
        <item>four</item>
      </line>
      <line>
        <item>5</item>
        <item>five</item>
      </line>
      <line>
        <item>7</item>
        <item>seven</item>
      </line>
      <line>
        <item>9</item>
        <item>nine</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>160</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Test Data 2</name>
    <type>DataGrid</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>id</name>
        <precision>-1</precision>
        <type>Integer</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>desc_2</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
    </fields>
    <data>
      <line>
        <item>1</item>
        <item>one</item>
      </line>
      <line>
        <item>2</item>
        <item>two</item>
      </line>
      <line>
        <item>3</item>
        <item>three</item>
      </line>
      <line>
        <item>4</item>
        <item>four</item>
      </line>
      <line>
        <item>5</item>
        <item>five</item>
      </line>
      <line>
        <item>6</item>
        <item>six</item>
      </line>
      <line>
        <item>8</item>
        <item>eight</item>
      </line>
      <line>
        <item>10</item>
        <item>ten</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>160</xloc>
      <yloc>656</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Merge join Left Outer</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>LEFT OUTER</join_type>
    <keys_1>
      <key>id</key>
    </keys_1>
    <keys_2>
      <key>id</key>
    </keys_2>
    <transform1>Test Data 1</transform1>
    <transform2>Test Data 2</transform2>
    <attributes/>
    <GUI>
      <xloc>496</xloc>
      <yloc>368</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Merge join Right Outer</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>RIGHT OUTER</join_type>
    <keys_1>
      <key>id</key>
    </keys_1>
    <keys_2>
      <key>id</key>
    </keys_2>
    <transform1>Test Data 1</transform1>
    <transform2>Test Data 2</transform2>
    <attributes/>
    <GUI>
      <xloc>496</xloc>
      <yloc>512</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Merge join Full Outer</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>FULL OUTER</join_type>
    <keys_1>
      <key>id</key>
    </keys_1>
    <keys_2>
      <key>id</key>
    </keys_2>
    <transform1>Test Data 1</transform1>
    <transform2>Test Data 2</transform2>
    <attributes/>
    <GUI>
      <xloc>496</xloc>
      <yloc>656</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Merge join Inner</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>INNER</join_type>
    <keys_1>
      <key>id</key>
    </keys_1>
    <keys_2>
      <key>id</key>
    </keys_2>
    <transform1>Test Data 1</transform1>
    <transform2>Test Data 2</transform2>
    <attributes/>
    <GUI>
      <xloc>496</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output Inner</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>720</xloc>
      <yloc>224</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output Left Outer</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>720</xloc>
      <yloc>368</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output Right Outer</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>720</xloc>
      <yloc>512</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output Full Outer</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>720</xloc>
      <yloc>656</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
