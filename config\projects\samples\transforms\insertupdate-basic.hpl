<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>insertupdate-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/07/19 19:37:21.103</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/07/19 19:37:21.103</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Writes 7 lines of data to a table, 2 of which are updates to earlier records. 
Id is used as the key (unique identifier). Values for col_bool and col_string are updated, values for id and col_date aren't</note>
      <xloc>112</xloc>
      <yloc>64</yloc>
      <width>667</width>
      <heigth>44</heigth>
      <fontname>Noto Sans</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Test Data</from>
      <to>Insert / update</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Insert / update</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>hop-samples</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>INSERTUPDATE</table>
      <key>
        <name>id</name>
        <field>id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>id</name>
        <rename>id</rename>
        <update>N</update>
      </value>
      <value>
        <name>col_bool</name>
        <rename>col_bool</rename>
        <update>Y</update>
      </value>
      <value>
        <name>col_string</name>
        <rename>col_string</rename>
        <update>Y</update>
      </value>
      <value>
        <name>col_date</name>
        <rename>col_date</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <GUI>
      <xloc>416</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Test Data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>id</name>
        <precision>-1</precision>
        <type>Integer</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>col_bool</name>
        <precision>-1</precision>
        <type>Boolean</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>col_string</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <format>yyyy-MM-dd</format>
        <length>-1</length>
        <name>col_date</name>
        <precision>-1</precision>
        <type>Date</type>
      </field>
    </fields>
    <data>
      <line>
        <item>1</item>
        <item>Y</item>
        <item>col1</item>
        <item>2021-01-01</item>
      </line>
      <line>
        <item>2</item>
        <item>N</item>
        <item>col2</item>
        <item>2022-01-01</item>
      </line>
      <line>
        <item>3</item>
        <item>N</item>
        <item>col3</item>
        <item>2023-01-01</item>
      </line>
      <line>
        <item>4</item>
        <item>Y</item>
        <item>col4</item>
        <item>2024-01-01</item>
      </line>
      <line>
        <item>5</item>
        <item>Y</item>
        <item>col5</item>
        <item>2025-01-01</item>
      </line>
      <line>
        <item>2</item>
        <item>Y</item>
        <item>new col2</item>
        <item>2022-12-31</item>
      </line>
      <line>
        <item>4</item>
        <item>N</item>
        <item>new col4</item>
        <item>2022-12-31</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>128</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
