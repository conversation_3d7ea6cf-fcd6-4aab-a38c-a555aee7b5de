<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>calculator-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/04/28 16:33:55.315</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/04/28 16:33:55.315</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>In this basic calculator example we use 2 numbers and
- substract
- add
- multiply
- divide</note>
      <xloc>64</xloc>
      <yloc>48</yloc>
      <width>290</width>
      <heigth>90</heigth>
      <fontname>Segoe UI</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Input Data</from>
      <to>Calculator</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Calculator</name>
    <type>Calculator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <failIfNoFile>Y</failIfNoFile>
    <calculation>
      <calc_type>SUBTRACT</calc_type>
      <field_a>first_number</field_a>
      <field_b>second_number</field_b>
      <field_name>Substraction</field_name>
      <remove>N</remove>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <value_type>Number</value_type>
    </calculation>
    <calculation>
      <calc_type>ADD</calc_type>
      <field_a>first_number</field_a>
      <field_b>second_number</field_b>
      <field_name>Addition</field_name>
      <remove>N</remove>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <value_type>Number</value_type>
    </calculation>
    <calculation>
      <calc_type>MULTIPLY</calc_type>
      <field_a>first_number</field_a>
      <field_b>second_number</field_b>
      <field_name>Multiplication</field_name>
      <remove>N</remove>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <value_type>Number</value_type>
    </calculation>
    <calculation>
      <calc_type>DIVIDE</calc_type>
      <field_a>second_number</field_a>
      <field_b>first_number</field_b>
      <field_name>Division</field_name>
      <remove>N</remove>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <value_type>Number</value_type>
    </calculation>
    <attributes/>
    <GUI>
      <xloc>288</xloc>
      <yloc>192</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Input Data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>first_number</name>
        <type>Number</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>second_number</name>
        <type>Number</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <data>
      <line>
        <item>100</item>
        <item>10</item>
      </line>
      <line>
        <item>50</item>
        <item>8</item>
      </line>
      <line>
        <item>90</item>
        <item>7</item>
      </line>
      <line>
        <item>29</item>
        <item>10</item>
      </line>
      <line>
        <item>17</item>
        <item>20</item>
      </line>
      <line>
        <item>44</item>
        <item>10</item>
      </line>
      <line>
        <item>45</item>
        <item>73</item>
      </line>
      <line>
        <item>91</item>
        <item>55</item>
      </line>
      <line>
        <item>40</item>
        <item>21</item>
      </line>
      <line>
        <item>72</item>
        <item>45</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>80</xloc>
      <yloc>192</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
