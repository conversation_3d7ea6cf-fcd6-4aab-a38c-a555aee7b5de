<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>apache-tika-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/02/05 14:37:26.558</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/02/05 14:37:26.558</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>get ASF ICLA</from>
      <to>split words to rows</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>keep word</from>
      <to>Sort rows</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Sort rows</from>
      <to>count words</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>count words</from>
      <to>log nb words </to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>split words to rows</from>
      <to>split newline to rows</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>split newline to rows</from>
      <to>trim, lower</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>word is not null</from>
      <to>keep word</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>trim, lower</from>
      <to>remove special chars</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>remove special chars</from>
      <to>word is not null</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Sort rows</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>${java.io.tmpdir}</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>Y</unique_rows>
    <fields>
      <field>
        <name>word</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>1232</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>count words</name>
    <type>MemoryGroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <aggregate>nb_words</aggregate>
        <subject>word</subject>
        <type>COUNT_DISTINCT</type>
      </field>
    </fields>
    <give_back_row>N</give_back_row>
    <group>
</group>
    <attributes/>
    <GUI>
      <xloc>1376</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>get ASF ICLA</name>
    <type>Tika</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <add-result-file>N</add-result-file>
    <content-field>content</content-field>
    <file-in-field>N</file-in-field>
    <file-size-field>fileSize</file-size-field>
    <files>
      <file>
        <include-sub-folders>N</include-sub-folders>
        <name>https://www.apache.org/licenses/icla.pdf</name>
        <required>N</required>
      </file>
    </files>
    <ignore-empty-file>N</ignore-empty-file>
    <include-filename-field>filename</include-filename-field>
    <metadata-field>metadata</metadata-field>
    <output-format>Plain text</output-format>
    <row-limit>0</row-limit>
    <attributes/>
    <GUI>
      <xloc>96</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>keep word</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>word</name>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <GUI>
      <xloc>1104</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>log nb words </name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage>number of words in document: </logmessage>
    <fields>
      <field>
        <name>nb_words</name>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>1520</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>remove special chars</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>//Script here

var word = word.replace(/[^a-zA-Z0-9 ]/g, '');</jsScript_script>
      </jsScript>
      <jsScript>
        <jsScript_type>-1</jsScript_type>
        <jsScript_name>replace_Sample</jsScript_name>
        <jsScript_script>// Replaces Strings inside the given Variable.
            // It is possible to make more replacements inside
            // one call. The given variables must be an odd number
            //
            // Usage:
            // replace(var, var, var);
            // 1: String - The Variable with the content to replace.
            // 2: String - The Value to search for.
            // 3: String - The Value to replace with.
            //
            // replace(var, var, var, var,var,...)
            // 1: String - The Variable with the content to replace.
            // 2: String - The First Value to search for.
            // 3: String - The First Value to replace with.
            // 4: String - The Second Value to search for.
            // 5: String - The Second Value to replace with.
            // ...
            //
            // 2006-11-15
            //
            var str1 = "Hello World, this is a nice function";
            var str2 = replace(str1,"World", "Folk");
            Alert(str2);
            var str2 = replace(str1,"World", "Folk", "nice","beautifull");
            Alert(str2);
        </jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>word</name>
        <rename>word</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>Y</replace>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>800</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>split newline to rows</name>
    <type>SplitFieldToRows3</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <delimiter>
</delimiter>
    <delimiter_is_regex>N</delimiter_is_regex>
    <newfield>word</newfield>
    <resetrownumber>Y</resetrownumber>
    <rownum>N</rownum>
    <splitfield>tmp_word</splitfield>
    <attributes/>
    <GUI>
      <xloc>464</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>split words to rows</name>
    <type>SplitFieldToRows3</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <delimiter> </delimiter>
    <delimiter_is_regex>N</delimiter_is_regex>
    <newfield>tmp_word</newfield>
    <resetrownumber>Y</resetrownumber>
    <rownum>N</rownum>
    <splitfield>content</splitfield>
    <attributes/>
    <GUI>
      <xloc>272</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>trim, lower</name>
    <type>StringOperations</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <in_stream_name>word</in_stream_name>
        <out_stream_name/>
        <trim_type>both</trim_type>
        <lower_upper>lower</lower_upper>
        <padding_type>none</padding_type>
        <pad_char/>
        <pad_len/>
        <init_cap>no</init_cap>
        <mask_xml>none</mask_xml>
        <digits>none</digits>
        <remove_special_characters>none</remove_special_characters>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>624</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>word is not null</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compare>
      <condition>
        <conditions>
</conditions>
        <function>IS NOT NULL</function>
        <leftvalue>word</leftvalue>
        <negated>N</negated>
        <operator>-</operator>
      </condition>
    </compare>
    <attributes/>
    <GUI>
      <xloc>976</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
