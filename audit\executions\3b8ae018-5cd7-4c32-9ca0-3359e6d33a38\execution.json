{"name": "data_mapping_pipeline", "filename": "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\test-project\\pipelines\\data_mapping_pipeline.hpl", "id": "3b8ae018-5cd7-4c32-9ca0-3359e6d33a38", "parentId": null, "executionType": "Pipeline", "executorXml": "<pipeline>\n  <info>\n    <name>data_mapping_pipeline</name>\n    <name_sync_with_filename>Y</name_sync_with_filename>\n    <description>数据映射测试管道</description>\n    <extended_description/>\n    <pipeline_version/>\n    <pipeline_type>Normal</pipeline_type>\n    <parameters>\n    </parameters>\n    <capture_transform_performance>N</capture_transform_performance>\n    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>\n    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>\n    <created_user>-</created_user>\n    <created_date>2025/09/04 09:14:00.000</created_date>\n    <modified_user>-</modified_user>\n    <modified_date>2025/09/04 09:14:00.000</modified_date>\n  </info>\n  <notepads>\n  </notepads>\n  <order>\n    <hop>\n      <from>CSV file input</from>\n      <to>Select values</to>\n      <enabled>Y</enabled>\n    </hop>\n    <hop>\n      <from>Select values</from>\n      <to>Text file output</to>\n      <enabled>Y</enabled>\n    </hop>\n  </order>\n  <transform>\n    <name>CSV file input</name>\n    <type>CSVInput</type>\n    <description/>\n    <distribute>Y</distribute>\n    <custom_distribution/>\n    <copies>1</copies>\n    <partitioning>\n      <method>none</method>\n      <schema_name/>\n    </partitioning>\n    <filename>${TEST_DATA_PATH}/source_table.csv</filename>\n    <filename_field/>\n    <rownum_field/>\n    <include_filename>N</include_filename>\n    <separator>,</separator>\n    <enclosure>\"</enclosure>\n    <header>Y</header>\n    <buffer_size>50000</buffer_size>\n    <schemaDefinition/>\n    <lazy_conversion>Y</lazy_conversion>\n    <add_filename_result>N</add_filename_result>\n    <parallel>N</parallel>\n    <newline_possible>N</newline_possible>\n    <encoding/>\n    <fields>\n      <field>\n        <name>id</name>\n        <type>Integer</type>\n        <format/>\n        <currency/>\n        <decimal/>\n        <group/>\n        <length>15</length>\n        <precision>0</precision>\n        <trim_type>none</trim_type>\n      </field>\n      <field>\n        <name>name</name>\n        <type>String</type>\n        <format/>\n        <currency/>\n        <decimal/>\n        <group/>\n        <length>50</length>\n        <precision>-1</precision>\n        <trim_type>none</trim_type>\n      </field>\n      <field>\n        <name>age</name>\n        <type>Integer</type>\n        <format/>\n        <currency/>\n        <decimal/>\n        <group/>\n        <length>15</length>\n        <precision>0</precision>\n        <trim_type>none</trim_type>\n      </field>\n      <field>\n        <name>city</name>\n        <type>String</type>\n        <format/>\n        <currency/>\n        <decimal/>\n        <group/>\n        <length>50</length>\n        <precision>-1</precision>\n        <trim_type>none</trim_type>\n      </field>\n      <field>\n        <name>salary</name>\n        <type>Number</type>\n        <format/>\n        <currency/>\n        <decimal/>\n        <group/>\n        <length>15</length>\n        <precision>2</precision>\n        <trim_type>none</trim_type>\n      </field>\n    </fields>\n    <attributes/>\n    <GUI>\n      <xloc>112</xloc>\n      <yloc>112</yloc>\n    </GUI>\n  </transform>\n  <transform>\n    <name>Select values</name>\n    <type>SelectValues</type>\n    <description/>\n    <distribute>Y</distribute>\n    <custom_distribution/>\n    <copies>1</copies>\n    <partitioning>\n      <method>none</method>\n      <schema_name/>\n    </partitioning>\n    <fields>\n      <field>\n        <length>-2</length>\n        <name>id</name>\n        <precision>-2</precision>\n        <rename>employee_id</rename>\n      </field>\n      <field>\n        <length>-2</length>\n        <name>name</name>\n        <precision>-2</precision>\n        <rename>employee_name</rename>\n      </field>\n      <field>\n        <length>-2</length>\n        <name>age</name>\n        <precision>-2</precision>\n        <rename>employee_age</rename>\n      </field>\n      <field>\n        <length>-2</length>\n        <name>city</name>\n        <precision>-2</precision>\n        <rename>work_city</rename>\n      </field>\n      <field>\n        <length>-2</length>\n        <name>salary</name>\n        <precision>-2</precision>\n        <rename>monthly_salary</rename>\n      </field>\n      <select_unspecified>N</select_unspecified>\n    </fields>\n    <attributes/>\n    <GUI>\n      <xloc>272</xloc>\n      <yloc>112</yloc>\n    </GUI>\n  </transform>\n  <transform>\n    <name>Text file output</name>\n    <type>TextFileOutput</type>\n    <description/>\n    <distribute>Y</distribute>\n    <custom_distribution/>\n    <copies>1</copies>\n    <partitioning>\n      <method>none</method>\n      <schema_name/>\n    </partitioning>\n    <schema_definition/>\n    <separator>,</separator>\n    <enclosure>\"</enclosure>\n    <enclosure_forced>N</enclosure_forced>\n    <enclosure_fix_disabled>N</enclosure_fix_disabled>\n    <header>Y</header>\n    <footer>N</footer>\n    <format>DOS</format>\n    <compression>None</compression>\n    <encoding/>\n    <endedLine/>\n    <fileNameInField>N</fileNameInField>\n    <fileNameField/>\n    <create_parent_folder>Y</create_parent_folder>\n    <file>\n      <name>${TEST_DATA_PATH}/mapped_output</name>\n      <servlet_output>N</servlet_output>\n      <do_not_open_new_file_init>N</do_not_open_new_file_init>\n      <extention>csv</extention>\n      <append>N</append>\n      <split>N</split>\n      <haspartno>N</haspartno>\n      <add_date>N</add_date>\n      <add_time>N</add_time>\n      <SpecifyFormat>N</SpecifyFormat>\n      <date_time_format/>\n      <add_to_result_filenames>Y</add_to_result_filenames>\n      <pad>N</pad>\n      <fast_dump>N</fast_dump>\n      <splitevery>0</splitevery>\n    </file>\n    <fields>\n      <field>\n        <name>employee_id</name>\n        <type>Integer</type>\n        <format/>\n        <currency/>\n        <decimal/>\n        <group/>\n        <nullif/>\n        <trim_type>none</trim_type>\n        <roundingType>half_even</roundingType>\n        <length>-1</length>\n        <precision>-1</precision>\n      </field>\n      <field>\n        <name>employee_name</name>\n        <type>String</type>\n        <format/>\n        <currency/>\n        <decimal/>\n        <group/>\n        <nullif/>\n        <trim_type>none</trim_type>\n        <roundingType>half_even</roundingType>\n        <length>-1</length>\n        <precision>-1</precision>\n      </field>\n      <field>\n        <name>employee_age</name>\n        <type>Integer</type>\n        <format/>\n        <currency/>\n        <decimal/>\n        <group/>\n        <nullif/>\n        <trim_type>none</trim_type>\n        <roundingType>half_even</roundingType>\n        <length>-1</length>\n        <precision>-1</precision>\n      </field>\n      <field>\n        <name>work_city</name>\n        <type>String</type>\n        <format/>\n        <currency/>\n        <decimal/>\n        <group/>\n        <nullif/>\n        <trim_type>none</trim_type>\n        <roundingType>half_even</roundingType>\n        <length>-1</length>\n        <precision>-1</precision>\n      </field>\n      <field>\n        <name>monthly_salary</name>\n        <type>Number</type>\n        <format/>\n        <currency/>\n        <decimal/>\n        <group/>\n        <nullif/>\n        <trim_type>none</trim_type>\n        <roundingType>half_even</roundingType>\n        <length>-1</length>\n        <precision>-1</precision>\n      </field>\n    </fields>\n    <attributes/>\n    <GUI>\n      <xloc>432</xloc>\n      <yloc>112</yloc>\n    </GUI>\n  </transform>\n  <transform_error_handling>\n  </transform_error_handling>\n  <attributes/>\n</pipeline>\n", "metadataJson": "{\"server\":[],\"pipeline-probe\":[],\"execution-info-location\":[{\"virtualPath\":null,\"executionInfoLocation\":{\"local-folder\":{\"pluginName\":\"File location\",\"pluginId\":\"local-folder\",\"rootFolder\":\"${HOP_AUDIT_FOLDER}\\/executions\\/\"}},\"dataLoggingDelay\":\"2000\",\"name\":\"local-audit\",\"description\":\"\",\"dataLoggingInterval\":\"5000\"}],\"workflow-log\":[],\"GoogleStorageConnectionDefinition\":[],\"schema-definition\":[],\"cassandra-connection\":[],\"neo4j-graph-model\":[],\"mongodb-connection\":[],\"partition\":[],\"execution-data-profile\":[{\"virtualPath\":null,\"name\":\"first-last\",\"description\":\"capture the first and last 100 rows of every Hop transform\",\"sampler\":[{\"FirstRowsExecutionDataSampler\":{\"sampleSize\":\"100\"}},{\"LastRowsExecutionDataSampler\":{\"sampleSize\":\"100\"}}]}],\"workflow-run-configuration\":[{\"engineRunConfiguration\":{\"Local\":{\"safe_mode\":false,\"transactional\":false}},\"virtualPath\":null,\"defaultSelection\":true,\"name\":\"local\",\"description\":\"\",\"executionInfoLocationName\":\"local-audit\"}],\"restconnection\":[],\"unit-test\":[],\"rdbms\":[],\"AzureConnectionDefinition\":[],\"web-service\":[],\"pipeline-run-configuration\":[{\"engineRunConfiguration\":{\"Local\":{\"feedback_size\":\"50000\",\"sample_size\":\"100\",\"sample_type_in_gui\":\"Last\",\"wait_time\":\"20\",\"rowset_size\":\"10000\",\"safe_mode\":false,\"show_feedback\":false,\"topo_sort\":false,\"gather_metrics\":false,\"transactional\":false}},\"virtualPath\":null,\"defaultSelection\":true,\"configurationVariables\":[],\"name\":\"local\",\"description\":\"\",\"dataProfile\":\"first-last\",\"executionInfoLocationName\":\"local-audit\"}],\"neo4j-connection\":[],\"async-web-service\":[],\"pipeline-log\":[],\"file-definition\":[],\"splunk\":[],\"dataset\":[],\"variable-resolver\":[]}", "runConfigurationName": "local", "logLevel": "BASIC", "parameterValues": {}, "environmentDetails": {"JavaUser": "Yeheng.Hu", "ContainerId": "8d0b8f7e-f14d-4317-b39c-8bee0ce6287d", "AvailableProcessors": "20", "TotalMemory": "326107136", "JavaVersion": "17.0.14", "HostAddress": "************", "HostName": "CN-D-6L0MKZ3", "MaxMemory": "2147483648", "FreeMemory": "149503592"}, "registrationDate": 1756948760757, "executionStartDate": 1756948760689, "copyNr": null}