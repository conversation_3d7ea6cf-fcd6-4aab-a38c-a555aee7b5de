{"nameStateMap": {"F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\metadata-injection\\read-and-inject-metadata.hpl": {"name": "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\metadata-injection\\read-and-inject-metadata.hpl", "stateMap": {"magnification": 1.0, "active": false, "offset-x": 0.0, "offset-y": 0.0}}, "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\test-project\\pipelines\\data_mapping_pipeline.hpl": {"name": "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\test-project\\pipelines\\data_mapping_pipeline.hpl", "stateMap": {"magnification": 1.0, "active": true, "offset-x": 0.0, "offset-y": 0.0}}, "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\pipelines\\pipeline-with-parameter.hpl": {"name": "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\pipelines\\pipeline-with-parameter.hpl", "stateMap": {"magnification": 1.0, "active": false, "offset-x": 0.0, "offset-y": 0.0}}, "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\metadata-injection\\filter-rows-mdi-child.hpl": {"name": "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\metadata-injection\\filter-rows-mdi-child.hpl", "stateMap": {"magnification": 1.0, "active": false, "offset-x": 0.0, "offset-y": 0.0}}, "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\workflows\\csvinput-huge-file.hpl": {"name": "F:\\hu\\H-Documentation\\database\\新建文件夹\\hop\\config\\projects\\samples\\workflows\\csvinput-huge-file.hpl", "stateMap": {"magnification": 1.0, "active": false, "offset-x": 0.0, "offset-y": 0.0}}}}