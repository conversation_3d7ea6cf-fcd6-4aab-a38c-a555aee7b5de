<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>check-set-counter-value</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/03/06 13:40:52.541</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/03/06 13:40:52.541</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>get counter</from>
      <to>counter &lt;= 10?</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>counter &lt;= 10?</from>
      <to>set new_counter</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>counter &lt;= 10?</from>
      <to>set ${END_LOOP}</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>set new_counter</from>
      <to>log counter</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>log counter</from>
      <to>set ${COUNTER}</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>counter &lt;= 10?</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compare>
      <condition>
        <conditions>
</conditions>
        <function>&lt;=</function>
        <leftvalue>counter</leftvalue>
        <negated>N</negated>
        <operator>-</operator>
        <value>
          <isnull>N</isnull>
          <length>-1</length>
          <mask>####0;-####0</mask>
          <name>constant</name>
          <precision>0</precision>
          <text>10</text>
          <type>Integer</type>
        </value>
      </condition>
    </compare>
    <send_false_to>set ${END_LOOP}</send_false_to>
    <send_true_to>set new_counter</send_true_to>
    <attributes/>
    <GUI>
      <xloc>304</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>get counter</name>
    <type>GetVariable</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <length>-1</length>
        <name>counter</name>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <type>Integer</type>
        <variable>${COUNTER}</variable>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>128</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>log counter</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage>#####################################
new counter value set to: 
#####################################
</logmessage>
    <fields>
      <field>
        <name>new_counter</name>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>624</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>set ${COUNTER}</name>
    <type>SetVariable</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <field_name>new_counter</field_name>
        <variable_name>COUNTER</variable_name>
        <variable_type>JVM</variable_type>
      </field>
    </fields>
    <use_formatting>Y</use_formatting>
    <attributes/>
    <GUI>
      <xloc>752</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>set ${END_LOOP}</name>
    <type>SetVariable</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <field_name>counter</field_name>
        <variable_name>END_LOOP</variable_name>
        <variable_type>JVM</variable_type>
      </field>
    </fields>
    <use_formatting>Y</use_formatting>
    <attributes/>
    <GUI>
      <xloc>304</xloc>
      <yloc>272</yloc>
    </GUI>
  </transform>
  <transform>
    <name>set new_counter</name>
    <type>Calculator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <calculation>
      <calc_type>CONSTANT</calc_type>
      <field_a>1</field_a>
      <field_name>one</field_name>
      <remove>Y</remove>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <value_type>Integer</value_type>
    </calculation>
    <calculation>
      <calc_type>ADD</calc_type>
      <field_a>counter</field_a>
      <field_b>one</field_b>
      <field_name>new_counter</field_name>
      <remove>N</remove>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
    </calculation>
    <failIfNoFile>Y</failIfNoFile>
    <attributes/>
    <GUI>
      <xloc>480</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
