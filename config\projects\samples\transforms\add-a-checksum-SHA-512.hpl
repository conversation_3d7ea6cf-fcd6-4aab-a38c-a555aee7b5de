<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>Add a checksum - SHA-512 example</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/04/27 10:47:16.295</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/04/27 10:47:16.295</modified_date>
    <key_for_session_key/>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Add Checksum -- SHA- 512 Algorithm</note>
      <xloc>512</xloc>
      <yloc>64</yloc>
      <width>213</width>
      <heigth>26</heigth>
      <fontname>Segoe UI</fontname>
      <fontsize>9</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Data grid</from>
      <to>Add a checksum</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Add a checksum</name>
    <type>CheckSum</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>4</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <checksumtype>SHA-512</checksumtype>
    <resultfieldName>checksum</resultfieldName>
    <resultType>hexadecimal</resultType>
    <fields>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>description</name>
      </field>
      <field>
        <name>created_date</name>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>672</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Data grid</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>id</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>description</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>created_date</name>
        <type>Date</type>
        <format>yyyy/MM/dd HH:mm:ss</format>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <data>
      <line>
        <item>12345</item>
        <item>B B Roy of great Britain has a very good work</item>
        <item>2021/04/27 10:53:44</item>
      </line>
      <line>
        <item>12345</item>
        <item>B B Roy of great Britain has a very good work</item>
        <item>2021/04/27 10:53:44</item>
      </line>
      <line>
        <item>12345</item>
        <item>B B Roy of great Britain has a very good work</item>
        <item>2021/04/27 10:53:44</item>
      </line>
      <line>
        <item>67891</item>
        <item>Data is new oil</item>
        <item>2020/05/28 09:08:54</item>
      </line>
      <line>
        <item>67891</item>
        <item>Data is new oil</item>
        <item>2020/05/28 09:08:54</item>
      </line>
      <line>
        <item>23456</item>
        <item>A star is born</item>
        <item>1989/09/22 07:45:00</item>
      </line>
      <line>
        <item>23456</item>
        <item>A star is born</item>
        <item>1989/09/22 07:45:00</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>304</xloc>
      <yloc>112</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
