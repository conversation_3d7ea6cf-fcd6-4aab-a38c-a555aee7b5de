<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>Closure Generator - Sample</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/04/29 13:15:08.242</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/04/29 13:15:08.242</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Generate a Reflexive Transitive Closure Table

It calculates the distance to the parent element in a hierarchy</note>
      <xloc>64</xloc>
      <yloc>32</yloc>
      <width>324</width>
      <heigth>57</heigth>
      <fontname>Segoe UI</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Hierarchical data</from>
      <to>Closure generator</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Closure generator</from>
      <to>Sort by ids and distance</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Closure generator</name>
    <type>ClosureGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <parent_id_field>manager_id</parent_id_field>
    <child_id_field>emp_id</child_id_field>
    <distance_field>distance</distance_field>
    <is_root_zero>N</is_root_zero>
    <attributes/>
    <GUI>
      <xloc>224</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Hierarchical data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>emp_id</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>manager_id</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>emp_name </name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <data>
      <line>
        <item>68319</item>
        <item>            </item>
        <item>KAYLING</item>
      </line>
      <line>
        <item>66928</item>
        <item>68319</item>
        <item>BLAZE</item>
      </line>
      <line>
        <item>67832</item>
        <item>68319</item>
        <item>CLARE</item>
      </line>
      <line>
        <item>65646</item>
        <item>68319</item>
        <item>JONAS</item>
      </line>
      <line>
        <item>67858</item>
        <item>65646</item>
        <item>SCARLET</item>
      </line>
      <line>
        <item>69062</item>
        <item>65646</item>
        <item>FRANK</item>
      </line>
      <line>
        <item>63679</item>
        <item>69062</item>
        <item>SANDRINE</item>
      </line>
      <line>
        <item>64989</item>
        <item>66928</item>
        <item>ADELYN</item>
      </line>
      <line>
        <item>65271</item>
        <item>66928</item>
        <item>WADE</item>
      </line>
      <line>
        <item>66564</item>
        <item>66928</item>
        <item>MADDEN</item>
      </line>
      <line>
        <item>68454</item>
        <item>66928</item>
        <item>TUCKER</item>
      </line>
      <line>
        <item>68736</item>
        <item>67858</item>
        <item>ADNRES</item>
      </line>
      <line>
        <item>69000</item>
        <item>66928</item>
        <item>JULIUS</item>
      </line>
      <line>
        <item>69324</item>
        <item>67832</item>
        <item>MARKER</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>80</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Sort by ids and distance</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>${java.io.tmpdir}</directory>
    <prefix>out</prefix>
    <sort_size>5400</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>manager_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>emp_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>distance</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>384</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
