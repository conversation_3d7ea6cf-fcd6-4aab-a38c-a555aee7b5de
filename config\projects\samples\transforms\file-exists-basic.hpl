<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>file-exists-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/02/22 09:31:08.160</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/02/22 09:31:08.160</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>get customers-100.txt filename</from>
      <to>check if customers-100.txt exists</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>generate 1 row</from>
      <to>get customers-100.txt filename</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>check if customers-100.txt exists</from>
      <to>exists?</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>exists?</from>
      <to>log exists</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>exists?</from>
      <to>log does not exist</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>get customers-100.txt filename</name>
    <type>GetVariable</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>filename</name>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <type>String</type>
        <variable>${PROJECT_HOME}/files/customers-100.txt</variable>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>328</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>check if customers-100.txt exists</name>
    <type>FileExists</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <addresultfilenames>N</addresultfilenames>
    <filenamefield>filename</filenamefield>
    <filetypefieldname>file_type</filetypefieldname>
    <includefiletype>Y</includefiletype>
    <resultfieldname>result</resultfieldname>
    <attributes/>
    <GUI>
      <xloc>560</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>generate 1 row</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
</fields>
    <interval_in_ms>5000</interval_in_ms>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <limit>1</limit>
    <never_ending>N</never_ending>
    <row_time_field>now</row_time_field>
    <attributes/>
    <GUI>
      <xloc>96</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>exists?</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compare>
      <condition>
        <conditions>
</conditions>
        <function>=</function>
        <leftvalue>result</leftvalue>
        <negated>N</negated>
        <operator>-</operator>
        <value>
          <isnull>N</isnull>
          <length>-1</length>
          <mask/>
          <name>constant</name>
          <precision>-1</precision>
          <text>Y</text>
          <type>Boolean</type>
        </value>
      </condition>
    </compare>
    <send_false_to>log does not exist</send_false_to>
    <send_true_to>log exists</send_true_to>
    <attributes/>
    <GUI>
      <xloc>792</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>log exists</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage/>
    <fields>
      </fields>
    <attributes/>
    <GUI>
      <xloc>1024</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>log does not exist</name>
    <type>WriteToLog</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <loglevel>log_level_basic</loglevel>
    <displayHeader>Y</displayHeader>
    <limitRows>N</limitRows>
    <limitRowsNumber>0</limitRowsNumber>
    <logmessage/>
    <fields>
      </fields>
    <attributes/>
    <GUI>
      <xloc>1024</xloc>
      <yloc>192</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
