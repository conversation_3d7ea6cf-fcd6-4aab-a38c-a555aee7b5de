<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>delete-5-customers</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2022/07/11 10:26:42.842</created_date>
    <modified_user>-</modified_user>
    <modified_date>2022/07/11 10:26:42.842</modified_date>
    <key_for_session_key/>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>5 lines to delete</from>
      <to>delete 5 customers</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>5 lines to delete</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>lastname</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>firstname</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
    </fields>
    <data>
      <line>
        <item>jwcdf-name</item>
        <item>fsj-firstname</item>
      </line>
      <line>
        <item>flhxu-name</item>
        <item>tum-firstname</item>
      </line>
      <line>
        <item>xthfg-name</item>
        <item>gfe-firstname</item>
      </line>
      <line>
        <item>ulzrz-name</item>
        <item>bnl-firstname</item>
      </line>
      <line>
        <item>oxhyr-name</item>
        <item>onx-firstname</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>160</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>delete 5 customers</name>
    <type>MongoDbDelete</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <collection>m_inserts_1</collection>
    <connection>mongo</connection>
    <execute_for_each_row>N</execute_for_each_row>
    <json_query/>
    <fields>
      <field>
        <comparator>=</comparator>
        <incoming_field_1>lastname</incoming_field_1>
        <incoming_field_2/>
        <doc_path>name</doc_path>
      </field>
      <field>
        <comparator>=</comparator>
        <incoming_field_1>firstname</incoming_field_1>
        <incoming_field_2/>
        <doc_path>firstname</doc_path>
      </field>
    </fields>
    <retries>5</retries>
    <read_preference/>
    <journaled_writes>N</journaled_writes>
    <use_json_query>N</use_json_query>
    <write_timeout/>
    <write_concern/>
    <write_retries>5</write_retries>
    <retry_delay>10</retry_delay>
    <attributes/>
    <GUI>
      <xloc>320</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
