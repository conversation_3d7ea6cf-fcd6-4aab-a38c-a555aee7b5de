{"executionType": "Pipeline", "parentId": null, "id": "3b8ae018-5cd7-4c32-9ca0-3359e6d33a38", "name": "data_mapping_pipeline", "copyNr": null, "loggingText": "2025/09/04 09:19:20 - data_mapping_pipeline - Executing this pipeline using the Local Pipeline Engine with run configuration 'local'\r\n2025/09/04 09:19:20 - data_mapping_pipeline - 为了 Pipeline 解除补丁开始 [data_mapping_pipeline]\r\n2025/09/04 09:19:21 - CSV file input.0 - ERROR: Unexpected error\r\n2025/09/04 09:19:21 - CSV file input.0 - ERROR: org.apache.hop.core.exception.HopFileException: \r\n2025/09/04 09:19:21 - CSV file input.0 - \r\n2025/09/04 09:19:21 - CSV file input.0 - 创建字段映射时意外出错\r\n2025/09/04 09:19:21 - CSV file input.0 - Could not read from \"file:///F:/hu/H-Documentation/database/新建文件夹/hop/${TEST_DATA_PATH}/source_table.csv\" because it is not a file.\r\n2025/09/04 09:19:21 - CSV file input.0 - \r\n2025/09/04 09:19:21 - CSV file input.0 - Could not read from \"file:///F:/hu/H-Documentation/database/新建文件夹/hop/${TEST_DATA_PATH}/source_table.csv\" because it is not a file.\r\n2025/09/04 09:19:21 - CSV file input.0 - \r\n2025/09/04 09:19:21 - CSV file input.0 - \tat org.apache.hop.pipeline.transforms.csvinput.CsvInput.readFieldNamesFromFile(CsvInput.java:495)\r\n2025/09/04 09:19:21 - CSV file input.0 - \tat org.apache.hop.pipeline.transforms.csvinput.CsvInput.createFieldMapping(CsvInput.java:449)\r\n2025/09/04 09:19:21 - CSV file input.0 - \tat org.apache.hop.pipeline.transforms.csvinput.CsvInput.openNextFile(CsvInput.java:330)\r\n2025/09/04 09:19:21 - CSV file input.0 - \tat org.apache.hop.pipeline.transforms.csvinput.CsvInput.processRow(CsvInput.java:122)\r\n2025/09/04 09:19:21 - CSV file input.0 - \tat org.apache.hop.pipeline.transform.RunThread.run(RunThread.java:54)\r\n2025/09/04 09:19:21 - CSV file input.0 - \tat java.base/java.lang.Thread.run(Thread.java:842)\r\n2025/09/04 09:19:21 - CSV file input.0 - Caused by: org.apache.commons.vfs2.FileNotFoundException: Could not read from \"file:///F:/hu/H-Documentation/database/新建文件夹/hop/${TEST_DATA_PATH}/source_table.csv\" because it is not a file.\r\n2025/09/04 09:19:21 - CSV file input.0 - \tat org.apache.commons.vfs2.provider.AbstractFileObject.getInpu", "lastLogLineNr": 13, "metrics": [{"componentName": "CSV file input", "componentCopy": "0", "metrics": {"Read": 0, "Buffers Output": 0, "Errors": 1, "Input": 0, "Written": 0, "Updated": 0, "Output": 0, "Rejected": 0, "Buffers Input": 0}}, {"componentName": "Select values", "componentCopy": "0", "metrics": {"Read": 0, "Buffers Output": 0, "Errors": 0, "Input": 0, "Written": 0, "Updated": 0, "Output": 0, "Rejected": 0, "Buffers Input": 0}}, {"componentName": "Text file output", "componentCopy": "0", "metrics": {"Read": 0, "Buffers Output": 0, "Errors": 0, "Input": 0, "Written": 0, "Updated": 0, "Output": 0, "Rejected": 0, "Buffers Input": 0}}], "statusDescription": "Finished (with errors)", "updateTime": 1756948761040, "childIds": ["7b2d4a35-200a-4735-ba12-7e354c526e33", "588290e7-ee78-4925-996d-3731666a2a7e", "4c5c77a5-b8a5-4644-8cec-65df8b98e935"], "details": {}, "failed": true, "executionEndDate": 1756948761025, "containerId": "8d0b8f7e-f14d-4317-b39c-8bee0ce6287d"}