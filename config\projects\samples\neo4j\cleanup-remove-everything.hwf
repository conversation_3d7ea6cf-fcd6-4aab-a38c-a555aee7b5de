<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<workflow>
  <name>cleanup-remove-everything</name>
  <name_sync_with_filename>Y</name_sync_with_filename>
  <description/>
  <extended_description/>
  <workflow_version/>
  <workflow_status>0</workflow_status>
  <created_user>-</created_user>
  <created_date>2021/08/03 18:28:50.176</created_date>
  <modified_user>-</modified_user>
  <modified_date>2021/08/03 18:28:50.176</modified_date>
  <parameters>
    <parameter>
      <name>BATCH_SIZE</name>
      <default_value>1000</default_value>
      <description/>
    </parameter>
  </parameters>
  <actions>
    <action>
      <name>Start</name>
      <description/>
      <type>SPECIAL</type>
      <attributes/>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <intervalSeconds>0</intervalSeconds>
      <intervalMinutes>60</intervalMinutes>
      <hour>12</hour>
      <minutes>0</minutes>
      <weekDay>1</weekDay>
      <DayOfMonth>1</DayOfMonth>
      <parallel>N</parallel>
      <xloc>96</xloc>
      <yloc>80</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>cleanup-remove-all-constraints.hpl</name>
      <description/>
      <type>PIPELINE</type>
      <attributes/>
      <filename>${PROJECT_HOME}/neo4j/cleanup-remove-all-constraints.hpl</filename>
      <params_from_previous>N</params_from_previous>
      <exec_per_row>N</exec_per_row>
      <clear_rows>N</clear_rows>
      <clear_files>N</clear_files>
      <set_logfile>N</set_logfile>
      <logfile/>
      <logext/>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <loglevel>Basic</loglevel>
      <set_append_logfile>N</set_append_logfile>
      <wait_until_finished>Y</wait_until_finished>
      <follow_abort_remote>N</follow_abort_remote>
      <create_parent_folder>N</create_parent_folder>
      <run_configuration>local</run_configuration>
      <parameters>
        <pass_all_parameters>Y</pass_all_parameters>
      </parameters>
      <parallel>N</parallel>
      <xloc>272</xloc>
      <yloc>80</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>cleanup-remove-all-nodes.hpl</name>
      <description/>
      <type>PIPELINE</type>
      <attributes/>
      <filename>${PROJECT_HOME}/neo4j/cleanup-remove-all-nodes.hpl</filename>
      <params_from_previous>N</params_from_previous>
      <exec_per_row>N</exec_per_row>
      <clear_rows>N</clear_rows>
      <clear_files>N</clear_files>
      <set_logfile>N</set_logfile>
      <logfile/>
      <logext/>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <loglevel>Basic</loglevel>
      <set_append_logfile>N</set_append_logfile>
      <wait_until_finished>Y</wait_until_finished>
      <follow_abort_remote>N</follow_abort_remote>
      <create_parent_folder>N</create_parent_folder>
      <run_configuration>local</run_configuration>
      <parameters>
        <pass_all_parameters>Y</pass_all_parameters>
        <parameter>
          <name>BATCH_SIZE</name>
          <stream_name/>
          <value>${BATCH_SIZE}</value>
        </parameter>
      </parameters>
      <parallel>N</parallel>
      <xloc>576</xloc>
      <yloc>192</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>cleanup-remove-all-indexes.hpl</name>
      <description/>
      <type>PIPELINE</type>
      <attributes/>
      <filename>${PROJECT_HOME}/neo4j/cleanup-remove-all-indexes.hpl</filename>
      <params_from_previous>N</params_from_previous>
      <exec_per_row>N</exec_per_row>
      <clear_rows>N</clear_rows>
      <clear_files>N</clear_files>
      <set_logfile>N</set_logfile>
      <logfile/>
      <logext/>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <loglevel>Basic</loglevel>
      <set_append_logfile>N</set_append_logfile>
      <wait_until_finished>Y</wait_until_finished>
      <follow_abort_remote>N</follow_abort_remote>
      <create_parent_folder>N</create_parent_folder>
      <run_configuration>local</run_configuration>
      <parameters>
        <pass_all_parameters>Y</pass_all_parameters>
      </parameters>
      <parallel>N</parallel>
      <xloc>576</xloc>
      <yloc>80</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>cleanup-remove-all-relationships.hpl</name>
      <description/>
      <type>PIPELINE</type>
      <attributes/>
      <filename>${PROJECT_HOME}/neo4j/cleanup-remove-all-relationships.hpl</filename>
      <params_from_previous>N</params_from_previous>
      <exec_per_row>N</exec_per_row>
      <clear_rows>N</clear_rows>
      <clear_files>N</clear_files>
      <set_logfile>N</set_logfile>
      <logfile/>
      <logext/>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <loglevel>Basic</loglevel>
      <set_append_logfile>N</set_append_logfile>
      <wait_until_finished>Y</wait_until_finished>
      <follow_abort_remote>N</follow_abort_remote>
      <create_parent_folder>N</create_parent_folder>
      <run_configuration>local</run_configuration>
      <parameters>
        <pass_all_parameters>Y</pass_all_parameters>
      </parameters>
      <parallel>N</parallel>
      <xloc>272</xloc>
      <yloc>192</yloc>
      <attributes_hac/>
    </action>
  </actions>
  <hops>
    <hop>
      <from>cleanup-remove-all-constraints.hpl</from>
      <to>cleanup-remove-all-indexes.hpl</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
    <hop>
      <from>cleanup-remove-all-indexes.hpl</from>
      <to>cleanup-remove-all-relationships.hpl</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
    <hop>
      <from>cleanup-remove-all-relationships.hpl</from>
      <to>cleanup-remove-all-nodes.hpl</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
    <hop>
      <from>Start</from>
      <to>cleanup-remove-all-constraints.hpl</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>Y</unconditional>
    </hop>
  </hops>
  <notepads>
  </notepads>
  <attributes/>
</workflow>
