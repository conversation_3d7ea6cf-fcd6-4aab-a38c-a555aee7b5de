<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>input-process-output</name>
    <name_sync_with_filename>N</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <pipeline_status>0</pipeline_status>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit/>
    <created_user>-</created_user>
    <created_date>2018/11/29 13:30:22.901</created_date>
    <modified_user>-</modified_user>
    <modified_date>2018/11/29 13:30:22.901</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>Limit fields, re-order</from>
      <to>input-process-output</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Only CA</from>
      <to>Limit fields, re-order</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Customers</from>
      <to>Only CA</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Customers</name>
    <type>BeamInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <file_description_name>Customers</file_description_name>
    <input_location>${DATA_INPUT}</input_location>
    <attributes/>
    <GUI>
      <xloc>160</xloc>
      <yloc>64</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Limit fields, re-order</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>First name</name>
      </field>
      <field>
        <name>Last name</name>
      </field>
      <field>
        <name>city</name>
      </field>
      <field>
        <name>state</name>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <GUI>
      <xloc>352</xloc>
      <yloc>64</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Only CA</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <send_true_to/>
    <send_false_to/>
    <compare>
      <condition>
        <negated>N</negated>
        <leftvalue>stateCode</leftvalue>
        <function>=</function>
        <rightvalue/>
        <value>
          <name/>
          <type>String</type>
          <text>CA</text>
          <length>-1</length>
          <precision>-1</precision>
          <isnull>N</isnull>
          <mask/>
        </value>
      </condition>
    </compare>
    <attributes/>
    <GUI>
      <xloc>256</xloc>
      <yloc>64</yloc>
    </GUI>
  </transform>
  <transform>
    <name>input-process-output</name>
    <type>BeamOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <file_prefix>input-process-output</file_prefix>
    <file_suffix>.csv</file_suffix>
    <output_location>${DATA_OUTPUT}</output_location>
    <windowed>N</windowed>
    <attributes/>
    <GUI>
      <xloc>480</xloc>
      <yloc>64</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
