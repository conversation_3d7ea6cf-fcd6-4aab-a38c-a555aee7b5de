<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>bespoke-minicheck</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <pipeline_status>0</pipeline_status>
    <parameters>
      <parameter>
        <name>HF_TOKEN</name>
        <default_value>hf_abc123</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>OPENAI_API_KEY</name>
        <default_value>sk-abc123</default_value>
        <description/>
      </parameter>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/12/18 15:00:31.032</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/12/18 15:00:31.032</modified_date>
  </info>
  <notepads>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Ubuntu</fontname>
      <fontsize>11</fontsize>
      <height>282</height>
      <xloc>1152</xloc>
      <yloc>96</yloc>
      <note>Sample A
Source: LLM Generated
-------------
Document:
The population of Toronto as of 2024 is 2.93 million.

Prompt:
How many people live in Toronto?


Claim generated by GPT 4o Mini:
As of my last update in October 2023, Toronto's estimated population was around 3 million people. For the most current population figures, it ...
Bespoke-Minicheck Response: Yes

Llama 3.1 70B:
Toronto is the largest city in Canada, and as of 2021, it has a population of approximately 2.7 million people within the city ...
Bespoke-Minicheck Response: No</note>
      <width>873</width>
    </notepad>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Ubuntu</fontname>
      <fontsize>11</fontsize>
      <height>314</height>
      <xloc>80</xloc>
      <yloc>96</yloc>
      <note>Fact-Checking on Grounding Documents use case
-------------
This sample is based on the article 'Reduce hallucinations with Bespoke-Minicheck', which derived from
the model proposed in the paper 'MiniCheck: Efficient Fact-Checking of LLMs on Grounding Documents'.

Two models are used in this sample to generate claims:
- OpenAI's GPT-4o-mini (API key required)
- Meta's Llama 3.1 70B, hosted on Hugging Face (access token required)

In this sample, the GPT/Llama models were prompted directly to use their pre-existing knowledge to generate a claim each.

One model is used to fact check the claims:
- Bespoke-Minicheck (hosted locally via Ollama). This model is particularly useful for building applications that detect inaccuracies or
misleading information in generated content. It can be used as a post-processing step to identify hallucinations and ensure the
generated content aligns with the grounding context.

References:
- https://ollama.com/blog/reduce-hallucinations-with-bespoke-minicheck
- https://arxiv.org/pdf/2404.10774</note>
      <width>805</width>
    </notepad>
    <notepad>
      <backgroundcolorblue>251</backgroundcolorblue>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorred>201</backgroundcolorred>
      <bordercolorblue>90</bordercolorblue>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorred>14</bordercolorred>
      <fontbold>N</fontbold>
      <fontcolorblue>90</fontcolorblue>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorred>14</fontcolorred>
      <fontitalic>N</fontitalic>
      <fontname>Ubuntu</fontname>
      <fontsize>11</fontsize>
      <height>282</height>
      <xloc>1152</xloc>
      <yloc>400</yloc>
      <note>Sample B
Source: https://github.com/ollama/ollama/tree/main/examples/python-grounded-factuality-simple-check
-------------

Document:
The Apple Computer 1 (Apple-1[a]), later known predominantly as the Apple I(written with a Roman numeral),[b] is an 8-bit motherboard-only personal
computer designed by Steve Wozniak[5][6] and released by the Apple Computer Company (now Apple Inc.) in 1976. The company was initially formed to
 sell the Apple I – its first product – and would later become the world's largest technology company.[7] The idea of starting a company and selling the
computer came from Wozniak's friend and Apple co-founder Steve Jobs.[8][9] One of the main innovations of the Apple I was that it included video
display terminal circuitry on its circuit board, allowing it to connect to a low-cost composite video monitor or television, instead of an expensive computer
terminal, compared to most existing computers at the time.

Claim: The Apple I is a 16-bit computer.
Bespoke-Minicheck Response: No

Claim: Apple was originally called the Apple Computer Company.
Bespoke-Minicheck Response: Yes</note>
      <width>927</width>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Yes/No</from>
      <to>Text output [YES]</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Yes/No</from>
      <to>Text output [NO]</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Yes/No</from>
      <to>Text output [ERROR]</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Generate Claim Prompt</from>
      <to>Generate Claim: gpt-4o-mini</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Generate Claim Prompt</from>
      <to>Generate Claim: Llama-3.1-70B</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Generate Claim: gpt-4o-mini</from>
      <to>Select GPT values</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Generate Claim: Llama-3.1-70B</from>
      <to>Select Llama values</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Select GPT values</from>
      <to>Claims</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Select Llama values</from>
      <to>Claims</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Claims</from>
      <to>Attach Document</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Grounding Document</from>
      <to>Attach Document</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Attach Document</from>
      <to>Document/Claim Prompt</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Document/Claim Prompt</from>
      <to>Fact-check Claims</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Fact-check Claims</from>
      <to>Select values</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Select values</from>
      <to>Yes/No</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Attach Document</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main/>
    <compare>
      <condition>
        <conditions>
</conditions>
        <function>=</function>
        <negated>N</negated>
        <operator>-</operator>
      </condition>
    </compare>
    <attributes/>
    <GUI>
      <xloc>992</xloc>
      <yloc>592</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Claims</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>864</xloc>
      <yloc>592</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Document/Claim Prompt</name>
    <type>TokenReplacementPlugin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <input_type>text</input_type>
    <input_text>Document: ${document}
Claim: ${claim}</input_text>
    <input_field_name/>
    <input_filename/>
    <input_filename_in_field>N</input_filename_in_field>
    <input_filename_field/>
    <add_input_filename_to_result>N</add_input_filename_to_result>
    <output_type>Field</output_type>
    <output_field_name>fact_check_prompt</output_field_name>
    <output_filename/>
    <output_filename_in_field>N</output_filename_in_field>
    <output_filename_field/>
    <append_output_filename>N</append_output_filename>
    <create_parent_folder>N</create_parent_folder>
    <output_file_format>UNIX</output_file_format>
    <output_file_encoding>UTF-8</output_file_encoding>
    <output_split_every>0</output_split_every>
    <include_transform_nr_in_output_filename>N</include_transform_nr_in_output_filename>
    <include_part_nr_in_output_filename>N</include_part_nr_in_output_filename>
    <include_date_in_output_filename>N</include_date_in_output_filename>
    <include_time_in_output_filename>N</include_time_in_output_filename>
    <specify_date_format_output_filename>N</specify_date_format_output_filename>
    <date_format_output_filename/>
    <add_output_filename_to_result>N</add_output_filename_to_result>
    <token_start_string>${</token_start_string>
    <token_end_string>}</token_end_string>
    <fields>
      <field>
        <field_name>claim</field_name>
        <token_name>claim</token_name>
      </field>
      <field>
        <field_name>document</field_name>
        <token_name>document</token_name>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>992</xloc>
      <yloc>720</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Fact-check Claims</name>
    <type>LanguageModelChat</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <inputField>fact_check_prompt</inputField>
    <inputChatJson>false</inputChatJson>
    <outputChatJson>false</outputChatJson>
    <mock>false</mock>
    <outputFieldNamePrefix>minicheck_</outputFieldNamePrefix>
    <modelType>OLLAMA</modelType>
    <parallelism>1</parallelism>
    <openAiUseProxy>false</openAiUseProxy>
    <openAiLogRequests>false</openAiLogRequests>
    <openAiLogResponses>false</openAiLogResponses>
    <huggingFaceReturnFullText>false</huggingFaceReturnFullText>
    <huggingFaceWaitForModel>false</huggingFaceWaitForModel>
    <mistralSafePrompt>false</mistralSafePrompt>
    <mistralLogRequests>false</mistralLogRequests>
    <mistralLogResponses>false</mistralLogResponses>
    <ollamaImageEndpoint>http://localhost:11434</ollamaImageEndpoint>
    <ollamaModelName>bespoke-minicheck</ollamaModelName>
    <ollamaTemperature>0.1</ollamaTemperature>
    <ollamaNumPredict>100</ollamaNumPredict>
    <ollamaTimeout>60</ollamaTimeout>
    <ollamaMaxRetries>3</ollamaMaxRetries>
    <anthropicLogRequests>false</anthropicLogRequests>
    <anthropicLogResponses>false</anthropicLogResponses>
    <attributes/>
    <GUI>
      <xloc>992</xloc>
      <yloc>816</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Generate Claim Prompt</name>
    <type>DataGrid</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>How many people live in Toronto?</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>prompt</name>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>224</xloc>
      <yloc>592</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Generate Claim: Llama-3.1-70B</name>
    <type>LanguageModelChat</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <inputField>prompt</inputField>
    <inputChatJson>false</inputChatJson>
    <outputChatJson>false</outputChatJson>
    <mock>false</mock>
    <outputFieldNamePrefix>llama_</outputFieldNamePrefix>
    <modelType>HUGGING_FACE</modelType>
    <parallelism>1</parallelism>
    <openAiUseProxy>false</openAiUseProxy>
    <openAiLogRequests>false</openAiLogRequests>
    <openAiLogResponses>false</openAiLogResponses>
    <huggingFaceAccessToken>${HF_TOKEN}</huggingFaceAccessToken>
    <huggingFaceModelId>meta-llama/Llama-3.1-70B-Instruct</huggingFaceModelId>
    <huggingFaceTemperature>0.7</huggingFaceTemperature>
    <huggingFaceMaxNewTokens>50</huggingFaceMaxNewTokens>
    <huggingFaceReturnFullText>false</huggingFaceReturnFullText>
    <huggingFaceWaitForModel>false</huggingFaceWaitForModel>
    <mistralSafePrompt>false</mistralSafePrompt>
    <mistralLogRequests>false</mistralLogRequests>
    <mistralLogResponses>false</mistralLogResponses>
    <anthropicLogRequests>false</anthropicLogRequests>
    <anthropicLogResponses>false</anthropicLogResponses>
    <attributes/>
    <GUI>
      <xloc>496</xloc>
      <yloc>640</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Generate Claim: gpt-4o-mini</name>
    <type>LanguageModelChat</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <inputField>prompt</inputField>
    <inputChatJson>false</inputChatJson>
    <outputChatJson>false</outputChatJson>
    <mock>false</mock>
    <outputFieldNamePrefix>gpt_</outputFieldNamePrefix>
    <modelType>OPEN_AI</modelType>
    <parallelism>1</parallelism>
    <openAiBaseUrl>https://api.openai.com/v1</openAiBaseUrl>
    <openAiApiKey>${OPENAI_API_KEY}</openAiApiKey>
    <openAiModelName>gpt-4o-mini</openAiModelName>
    <openAiTemperature>0.7</openAiTemperature>
    <openAiMaxTokens>50</openAiMaxTokens>
    <openAiResponseFormat>text</openAiResponseFormat>
    <openAiTimeout>60</openAiTimeout>
    <openAiMaxRetries>3</openAiMaxRetries>
    <openAiUseProxy>false</openAiUseProxy>
    <openAiProxyHost>127.0.0.1</openAiProxyHost>
    <openAiProxyPort>30000</openAiProxyPort>
    <openAiLogRequests>false</openAiLogRequests>
    <openAiLogResponses>false</openAiLogResponses>
    <huggingFaceReturnFullText>false</huggingFaceReturnFullText>
    <huggingFaceWaitForModel>false</huggingFaceWaitForModel>
    <mistralSafePrompt>false</mistralSafePrompt>
    <mistralLogRequests>false</mistralLogRequests>
    <mistralLogResponses>false</mistralLogResponses>
    <anthropicLogRequests>false</anthropicLogRequests>
    <anthropicLogResponses>false</anthropicLogResponses>
    <attributes/>
    <GUI>
      <xloc>496</xloc>
      <yloc>528</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Grounding Document</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>The population of Toronto as of 2024 is 2.93 million.</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>document</name>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>992</xloc>
      <yloc>432</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Select GPT values</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>gpt_model_name</name>
        <rename>model_name</rename>
      </field>
      <field>
        <name>gpt_inference_time</name>
        <rename>inference_time</rename>
      </field>
      <field>
        <name>gpt_output</name>
        <rename>claim</rename>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <GUI>
      <xloc>704</xloc>
      <yloc>528</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Select Llama values</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>llama_model_name</name>
        <rename>model_name</rename>
      </field>
      <field>
        <name>llama_inference_time</name>
        <rename>inference_time</rename>
      </field>
      <field>
        <name>llama_output</name>
        <rename>claim</rename>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <GUI>
      <xloc>704</xloc>
      <yloc>640</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Select values</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>claim</name>
      </field>
      <field>
        <name>minicheck_output</name>
        <rename>fact_check_response</rename>
      </field>
      <field>
        <name>document</name>
      </field>
      <field>
        <name>model_name</name>
        <rename>claim_model_name</rename>
      </field>
      <field>
        <name>inference_time</name>
        <rename>claim_inference_time</rename>
      </field>
      <field>
        <name>minicheck_model_name</name>
        <rename>fact_check_model_name</rename>
      </field>
      <field>
        <name>minicheck_inference_time</name>
        <rename>fact_check_inference_time</rename>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <GUI>
      <xloc>992</xloc>
      <yloc>928</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Text output [ERROR]</name>
    <type>TextFileOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <schema_definition/>
    <separator>;</separator>
    <enclosure>"</enclosure>
    <enclosure_forced>N</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>Y</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding>UTF-8</encoding>
    <endedLine/>
    <fileNameInField>N</fileNameInField>
    <fileNameField/>
    <create_parent_folder>Y</create_parent_folder>
    <file>
      <name>languagemodelchat-rag-output-err</name>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>Y</do_not_open_new_file_init>
      <extention>csv</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format/>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>N</fast_dump>
      <splitevery/>
    </file>
    <fields>
    </fields>
    <attributes/>
    <GUI>
      <xloc>1328</xloc>
      <yloc>928</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Text output [NO]</name>
    <type>TextFileOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <schema_definition/>
    <separator>;</separator>
    <enclosure>"</enclosure>
    <enclosure_forced>N</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>Y</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding>UTF-8</encoding>
    <endedLine/>
    <fileNameInField>N</fileNameInField>
    <fileNameField/>
    <create_parent_folder>Y</create_parent_folder>
    <file>
      <name>languagemodelchat-rag-output-no</name>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>Y</do_not_open_new_file_init>
      <extention>csv</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format/>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>N</fast_dump>
      <splitevery/>
    </file>
    <fields>
    </fields>
    <attributes/>
    <GUI>
      <xloc>1328</xloc>
      <yloc>1024</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Text output [YES]</name>
    <type>TextFileOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <schema_definition/>
    <separator>;</separator>
    <enclosure>"</enclosure>
    <enclosure_forced>N</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>Y</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding>UTF-8</encoding>
    <endedLine/>
    <fileNameInField>N</fileNameInField>
    <fileNameField/>
    <create_parent_folder>Y</create_parent_folder>
    <file>
      <name>languagemodelchat-rag-output-yes</name>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>Y</do_not_open_new_file_init>
      <extention>csv</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format/>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>N</fast_dump>
      <splitevery/>
    </file>
    <fields>
    </fields>
    <attributes/>
    <GUI>
      <xloc>1328</xloc>
      <yloc>848</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Yes/No</name>
    <type>SwitchCase</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <case_value_type>String</case_value_type>
    <cases>
      <case>
        <target_transform>Text output [YES]</target_transform>
        <value>Yes</value>
      </case>
      <case>
        <target_transform>Text output [NO]</target_transform>
        <value>No</value>
      </case>
    </cases>
    <default_target_transform>Text output [ERROR]</default_target_transform>
    <fieldname>fact_check_response</fieldname>
    <use_contains>N</use_contains>
    <attributes/>
    <GUI>
      <xloc>1136</xloc>
      <yloc>928</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
