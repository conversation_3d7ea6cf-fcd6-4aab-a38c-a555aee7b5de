<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<workflow>
  <name>workflow-executor-child-workflow</name>
  <name_sync_with_filename>Y</name_sync_with_filename>
  <description/>
  <extended_description/>
  <workflow_version/>
  <workflow_status>0</workflow_status>
  <created_user>-</created_user>
  <created_date>2023/03/06 08:53:58.506</created_date>
  <modified_user>-</modified_user>
  <modified_date>2023/03/06 08:53:58.506</modified_date>
  <parameters>
    <parameter>
      <name>PRM_COUNTER</name>
      <default_value/>
      <description>receive a counter parameter and print the value to the log.</description>
    </parameter>
  </parameters>
  <actions>
    <action>
      <name>Start</name>
      <description/>
      <type>SPECIAL</type>
      <attributes/>
      <DayOfMonth>1</DayOfMonth>
      <hour>12</hour>
      <intervalMinutes>60</intervalMinutes>
      <intervalSeconds>0</intervalSeconds>
      <minutes>0</minutes>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <weekDay>1</weekDay>
      <parallel>N</parallel>
      <xloc>50</xloc>
      <yloc>50</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>log ${PRM_COUNTER}</name>
      <description/>
      <type>WRITE_TO_LOG</type>
      <attributes/>
      <loglevel>Basic</loglevel>
      <logmessage>#################################
the vaule for PRM_COUNTER is now ${PRM_COUNTER}
#################################
</logmessage>
      <loglevel/>
      <logsubject/>
      <parallel>N</parallel>
      <xloc>208</xloc>
      <yloc>48</yloc>
      <attributes_hac/>
    </action>
  </actions>
  <hops>
    <hop>
      <from>Start</from>
      <to>log ${PRM_COUNTER}</to>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>Y</unconditional>
    </hop>
  </hops>
  <notepads>
  </notepads>
  <attributes/>
</workflow>
