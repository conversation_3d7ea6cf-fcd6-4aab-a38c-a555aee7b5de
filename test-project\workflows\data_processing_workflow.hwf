<?xml version="1.0" encoding="UTF-8"?>
<workflow>
  <name>data_processing_workflow</name>
  <name_sync_with_filename>Y</name_sync_with_filename>
  <description>数据处理工作流</description>
  <extended_description/>
  <workflow_version/>
  <workflow_status>0</workflow_status>
  <created_user>-</created_user>
  <created_date>2025/09/04 09:14:00.000</created_date>
  <modified_user>-</modified_user>
  <modified_date>2025/09/04 09:14:00.000</modified_date>
  <parameters>
  </parameters>
  <actions>
    <action>
      <name>START</name>
      <description/>
      <type>SPECIAL</type>
      <attributes/>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <intervalSeconds>0</intervalSeconds>
      <intervalMinutes>60</intervalMinutes>
      <hour>12</hour>
      <minutes>0</minutes>
      <weekDay>1</weekDay>
      <DayOfMonth>1</DayOfMonth>
      <parallel>N</parallel>
      <draw>Y</draw>
      <nr>0</nr>
      <xloc>50</xloc>
      <yloc>50</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>检查输入文件</name>
      <description/>
      <type>FILE_EXISTS</type>
      <attributes/>
      <filename>${TEST_DATA_PATH}/source_table.csv</filename>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <intervalSeconds>0</intervalSeconds>
      <intervalMinutes>60</intervalMinutes>
      <hour>12</hour>
      <minutes>0</minutes>
      <weekDay>1</weekDay>
      <DayOfMonth>1</DayOfMonth>
      <parallel>N</parallel>
      <draw>Y</draw>
      <nr>1</nr>
      <xloc>200</xloc>
      <yloc>50</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>执行数据映射管道</name>
      <description/>
      <type>PIPELINE</type>
      <attributes/>
      <filename>${PROJECT_HOME}/pipelines/data_mapping_pipeline.hpl</filename>
      <params_from_previous>N</params_from_previous>
      <exec_per_row>N</exec_per_row>
      <clear_rows>N</clear_rows>
      <clear_files>N</clear_files>
      <set_logfile>N</set_logfile>
      <logfile/>
      <logext/>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <loglevel>Basic</loglevel>
      <cluster>N</cluster>
      <slave_server_name/>
      <set_append_logfile>N</set_append_logfile>
      <wait_until_finished>Y</wait_until_finished>
      <follow_abort_remote>N</follow_abort_remote>
      <create_parent_folder>N</create_parent_folder>
      <logging_remote_work>N</logging_remote_work>
      <run_configuration/>
      <parameters>
        <pass_all_parameters>Y</pass_all_parameters>
      </parameters>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <intervalSeconds>0</intervalSeconds>
      <intervalMinutes>60</intervalMinutes>
      <hour>12</hour>
      <minutes>0</minutes>
      <weekDay>1</weekDay>
      <DayOfMonth>1</DayOfMonth>
      <parallel>N</parallel>
      <draw>Y</draw>
      <nr>2</nr>
      <xloc>400</xloc>
      <yloc>50</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>验证输出文件</name>
      <description/>
      <type>FILE_EXISTS</type>
      <attributes/>
      <filename>${TEST_DATA_PATH}/mapped_output.csv</filename>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <intervalSeconds>0</intervalSeconds>
      <intervalMinutes>60</intervalMinutes>
      <hour>12</hour>
      <minutes>0</minutes>
      <weekDay>1</weekDay>
      <DayOfMonth>1</DayOfMonth>
      <parallel>N</parallel>
      <draw>Y</draw>
      <nr>3</nr>
      <xloc>600</xloc>
      <yloc>50</yloc>
      <attributes_hac/>
    </action>
    <action>
      <name>成功完成</name>
      <description/>
      <type>SUCCESS</type>
      <attributes/>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <intervalSeconds>0</intervalSeconds>
      <intervalMinutes>60</intervalMinutes>
      <hour>12</hour>
      <minutes>0</minutes>
      <weekDay>1</weekDay>
      <DayOfMonth>1</DayOfMonth>
      <parallel>N</parallel>
      <draw>Y</draw>
      <nr>4</nr>
      <xloc>750</xloc>
      <yloc>50</yloc>
      <attributes_hac/>
    </action>
  </actions>
  <hops>
    <hop>
      <from>START</from>
      <to>检查输入文件</to>
      <from_nr>0</from_nr>
      <to_nr>1</to_nr>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>Y</unconditional>
    </hop>
    <hop>
      <from>检查输入文件</from>
      <to>执行数据映射管道</to>
      <from_nr>1</from_nr>
      <to_nr>2</to_nr>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
    <hop>
      <from>执行数据映射管道</from>
      <to>验证输出文件</to>
      <from_nr>2</from_nr>
      <to_nr>3</to_nr>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
    <hop>
      <from>验证输出文件</from>
      <to>成功完成</to>
      <from_nr>3</from_nr>
      <to_nr>4</to_nr>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
  </hops>
  <notepads>
  </notepads>
  <attributes/>
</workflow>
