<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>neo4j-cypher-unwind-simple</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <pipeline_status>0</pipeline_status>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2018/09/05 16:06:11.970</created_date>
    <modified_user>-</modified_user>
    <modified_date>2018/09/05 16:06:11.970</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>Sample data</from>
      <to>Merge (:Year)-[:IN]-(:Event)</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Merge (:Year)-[:IN]-(:Event)</name>
    <type>Neo4jCypherOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>demo</connection>
    <cypher>UNWIND $events AS event
MERGE (y:Year { year: event.year })
MERGE (y)&lt;-[:IN]-(e:Event { id: event.id })
RETURN e.id AS x
ORDER BY x</cypher>
    <batch_size>10</batch_size>
    <read_only>N</read_only>
    <nr_retries_on_error/>
    <retry>Y</retry>
    <cypher_from_field>N</cypher_from_field>
    <cypher_field/>
    <unwind>Y</unwind>
    <unwind_map>events</unwind_map>
    <returning_graph>N</returning_graph>
    <return_graph_field/>
    <mappings>
      <mapping>
        <parameter>year</parameter>
        <field>_year_</field>
        <type>String</type>
      </mapping>
      <mapping>
        <parameter>id</parameter>
        <field>Id</field>
        <type>String</type>
      </mapping>
    </mappings>
    <returns/>
    <attributes/>
    <GUI>
      <xloc>288</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Sample data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>_year_</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>Id</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
    </fields>
    <data>
      <line>
        <item>2013</item>
        <item>1</item>
      </line>
      <line>
        <item>2014</item>
        <item>2</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>112</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
