<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>avro-file-output</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/02/06 20:04:32.499</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/02/06 20:04:32.499</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>read customers-100.txt</from>
      <to>Avro File Output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Avro File Output</name>
    <type>AvroOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <SpecifyFormat>N</SpecifyFormat>
    <add_date>N</add_date>
    <add_time>N</add_time>
    <add_to_result_filenames>Y</add_to_result_filenames>
    <compressiontype>none</compressiontype>
    <create_parent_folder>Y</create_parent_folder>
    <create_schema_file>Y</create_schema_file>
    <date_time_format/>
    <doc/>
    <fields>
      <field>
        <avroname>id</avroname>
        <avrotype>4</avrotype>
        <name>id</name>
        <nullable>Y</nullable>
      </field>
      <field>
        <avroname>name</avroname>
        <avrotype>6</avrotype>
        <name>name</name>
        <nullable>Y</nullable>
      </field>
      <field>
        <avroname>firstname</avroname>
        <avrotype>6</avrotype>
        <name>firstname</name>
        <nullable>Y</nullable>
      </field>
      <field>
        <avroname>zip</avroname>
        <avrotype>6</avrotype>
        <name>zip</name>
        <nullable>Y</nullable>
      </field>
      <field>
        <avroname>city</avroname>
        <avrotype>6</avrotype>
        <name>city</name>
        <nullable>Y</nullable>
      </field>
      <field>
        <avroname>birthdate</avroname>
        <avrotype>6</avrotype>
        <name>birthdate</name>
        <nullable>Y</nullable>
      </field>
      <field>
        <avroname>street</avroname>
        <avrotype>6</avrotype>
        <name>street</name>
        <nullable>Y</nullable>
      </field>
      <field>
        <avroname>housenr</avroname>
        <avrotype>4</avrotype>
        <name>housenr</name>
        <nullable>Y</nullable>
      </field>
      <field>
        <avroname>stateCode</avroname>
        <avrotype>6</avrotype>
        <name>stateCode</name>
        <nullable>Y</nullable>
      </field>
      <field>
        <avroname>state</avroname>
        <avrotype>6</avrotype>
        <name>state</name>
        <nullable>Y</nullable>
      </field>
    </fields>
    <filename>${java.io.tmpdir}/file.avro</filename>
    <haspartno>N</haspartno>
    <namespace>namespace</namespace>
    <output_field_name>avro_record</output_field_name>
    <output_type>BinaryFile</output_type>
    <recordname>recordname</recordname>
    <schemafilename>schema.avsc</schemafilename>
    <split>N</split>
    <write_schema_file>Y</write_schema_file>
    <attributes/>
    <GUI>
      <xloc>384</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform>
    <name>read customers-100.txt</name>
    <type>TextFileInput2</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <accept_filenames>N</accept_filenames>
    <passing_through_fields>N</passing_through_fields>
    <accept_field/>
    <accept_transform_name/>
    <separator>;</separator>
    <enclosure>"</enclosure>
    <enclosure_breaks>N</enclosure_breaks>
    <escapechar/>
    <header>Y</header>
    <nr_headerlines>1</nr_headerlines>
    <footer>N</footer>
    <nr_footerlines>1</nr_footerlines>
    <line_wrapped>N</line_wrapped>
    <nr_wraps>1</nr_wraps>
    <layout_paged>N</layout_paged>
    <nr_lines_per_page>80</nr_lines_per_page>
    <nr_lines_doc_header>0</nr_lines_doc_header>
    <noempty>Y</noempty>
    <include>N</include>
    <include_field/>
    <rownum>N</rownum>
    <rownumByFile>N</rownumByFile>
    <rownum_field/>
    <format>mixed</format>
    <encoding/>
    <length>Characters</length>
    <add_to_result_filenames>Y</add_to_result_filenames>
    <file>
      <name>${PROJECT_HOME}/files/customers-100.txt</name>
      <filemask/>
      <exclude_filemask/>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
      <type>CSV</type>
      <compression>None</compression>
    </file>
    <filters>
    </filters>
    <fields>
      <field>
        <name>id</name>
        <type>Integer</type>
        <format> #</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif>-</nullif>
        <ifnull/>
        <position>-1</position>
        <length>15</length>
        <precision>0</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>name</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif>-</nullif>
        <ifnull/>
        <position>-1</position>
        <length>15</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>firstname</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif>-</nullif>
        <ifnull/>
        <position>-1</position>
        <length>20</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>zip</name>
        <type>Integer</type>
        <format> #</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif>-</nullif>
        <ifnull/>
        <position>-1</position>
        <length>15</length>
        <precision>0</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>city</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif>-</nullif>
        <ifnull/>
        <position>-1</position>
        <length>8</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>birthdate</name>
        <type>Date</type>
        <format>yyyy/MM/dd</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif>-</nullif>
        <ifnull/>
        <position>-1</position>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>street</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif>-</nullif>
        <ifnull/>
        <position>-1</position>
        <length>11</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>housenr</name>
        <type>Integer</type>
        <format> #</format>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif>-</nullif>
        <ifnull/>
        <position>-1</position>
        <length>15</length>
        <precision>0</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>stateCode</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif>-</nullif>
        <ifnull/>
        <position>-1</position>
        <length>2</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>state</name>
        <type>String</type>
        <format/>
        <currency>$</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif>-</nullif>
        <ifnull/>
        <position>-1</position>
        <length>30</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
    </fields>
    <limit>0</limit>
    <error_ignored>N</error_ignored>
    <skip_bad_files>N</skip_bad_files>
    <file_error_field/>
    <file_error_message_field/>
    <error_line_skipped>N</error_line_skipped>
    <error_count_field/>
    <error_fields_field/>
    <error_text_field/>
    <bad_line_files_destination_directory/>
    <bad_line_files_extension>warning</bad_line_files_extension>
    <error_line_files_destination_directory/>
    <error_line_files_extension>error</error_line_files_extension>
    <line_number_files_destination_directory/>
    <line_number_files_extension>line</line_number_files_extension>
    <date_format_lenient>Y</date_format_lenient>
    <date_format_locale>en_US</date_format_locale>
    <shortFileFieldName/>
    <pathFieldName/>
    <hiddenFieldName/>
    <lastModificationTimeFieldName/>
    <uriNameFieldName/>
    <rootUriNameFieldName/>
    <extensionFieldName/>
    <sizeFieldName/>
    <attributes/>
    <GUI>
      <xloc>96</xloc>
      <yloc>80</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
