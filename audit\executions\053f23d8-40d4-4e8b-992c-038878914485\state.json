{"executionType": "Workflow", "parentId": null, "id": "053f23d8-40d4-4e8b-992c-038878914485", "name": "write-read-huge-file", "copyNr": null, "loggingText": "2025/09/03 13:31:14 - write-read-huge-file - Start of workflow execution\r\n2025/09/03 13:31:14 - write-read-huge-file - Starting action [textfileoutput-huge-file.hpl]\r\n2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - ERROR: Unable to run workflow write-read-huge-file. The textfileoutput-huge-file.hpl has an error. The pipeline path config/projects/default/workflows/textfileoutput-huge-file.hpl is invalid, and will not run successfully.\r\n2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - ERROR: org.apache.hop.core.exception.HopXmlException: \r\n2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - The pipeline path config/projects/default/workflows/textfileoutput-huge-file.hpl is invalid, and will not run successfully.\r\n2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - \r\n2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - \tat org.apache.hop.pipeline.PipelineMeta.loadXml(PipelineMeta.java:1676)\r\n2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - \tat org.apache.hop.pipeline.PipelineMeta.<init>(PipelineMeta.java:1664)\r\n2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - \tat org.apache.hop.workflow.actions.pipeline.ActionPipeline.getPipelineMeta(ActionPipeline.java:633)\r\n2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - \tat org.apache.hop.workflow.actions.pipeline.ActionPipeline.execute(ActionPipeline.java:342)\r\n2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - \tat org.apache.hop.workflow.Workflow.executeFromStart(Workflow.java:751)\r\n2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - \tat org.apache.hop.workflow.Workflow.executeFromStart(Workflow.java:894)\r\n2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - \tat org.apache.hop.workflow.Workflow.executeFromStart(Workflow.java:453)\r\n2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - \tat org.apache.hop.workflow.Workflow.startExecution(Workflow.java:313)\r\n2025/09/03 13:31:14 - textfileoutput-huge-file.hpl - \tat org.apache.hop.workflow.engines.local.LocalWorkflowEngine.startExecution(LocalWorkflowEngine.jav", "lastLogLineNr": 10, "metrics": [], "statusDescription": "Finished (with errors)", "updateTime": 1756877474834, "childIds": ["8611b2c7-1a10-432a-8139-7bef55d7c3b5", "d08dbb27-87ef-47a5-ae8a-91d2a2e91ab4"], "details": {}, "failed": true, "executionEndDate": 1756877474831, "containerId": null}