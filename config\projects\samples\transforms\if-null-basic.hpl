<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>if-null-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/07/14 15:03:08.342</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/07/14 15:03:08.342</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Add a value when a field or data type is null 
*) If 'desc' null: check if a field is null, replace with a hard coded value or variable if it is 
*) If Dates null: check all fields of a given data type, replace null values with a hard coded value or variable value </note>
      <xloc>80</xloc>
      <yloc>40</yloc>
      <width>633</width>
      <heigth>61</heigth>
      <fontname>Noto Sans</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Test Data</from>
      <to>If 'desc' Null</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>If 'desc' Null</from>
      <to>Output 'desc'</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Test Data</from>
      <to>If Dates Null</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>If Dates Null</from>
      <to>Output Dates</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Test Data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>id</name>
        <precision>-1</precision>
        <type>Integer</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format/>
        <group/>
        <length>-1</length>
        <name>desc</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
      <field>
        <currency/>
        <decimal/>
        <set_empty_string>N</set_empty_string>
        <format>yyyy-MM-dd</format>
        <group/>
        <length>-1</length>
        <name>a_date</name>
        <precision>-1</precision>
        <type>Date</type>
      </field>
    </fields>
    <data>
      <line>
        <item>1</item>
        <item>one</item>
        <item>2021-01-01</item>
      </line>
      <line>
        <item>2</item>
        <item>two</item>
        <item>2022-01-01</item>
      </line>
      <line>
        <item>3</item>
        <item/>
        <item>2023-01-01</item>
      </line>
      <line>
        <item>4</item>
        <item>four</item>
        <item/>
      </line>
      <line>
        <item>5</item>
        <item>five</item>
        <item>2025-01-01</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>96</xloc>
      <yloc>176</yloc>
    </GUI>
  </transform>
  <transform>
    <name>If 'desc' Null</name>
    <type>IfNull</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <replaceAllByValue/>
    <replaceAllMask/>
    <selectFields>Y</selectFields>
    <selectValuesType>N</selectValuesType>
    <setEmptyStringAll>N</setEmptyStringAll>
    <valuetypes>
      </valuetypes>
    <fields>
      <field>
        <name>desc</name>
        <value>three</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>240</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output 'desc'</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>416</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>If Dates Null</name>
    <type>IfNull</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <replaceAllByValue/>
    <replaceAllMask/>
    <selectFields>N</selectFields>
    <selectValuesType>Y</selectValuesType>
    <setEmptyStringAll>N</setEmptyStringAll>
    <valuetypes>
      <valuetype>
        <name>Date</name>
        <value>2024-01-01</value>
        <mask>yyyy-MM-dd</mask>
        <set_type_empty_string>N</set_type_empty_string>
      </valuetype>
    </valuetypes>
    <fields>
      </fields>
    <attributes/>
    <GUI>
      <xloc>240</xloc>
      <yloc>240</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Output Dates</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>416</xloc>
      <yloc>240</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
