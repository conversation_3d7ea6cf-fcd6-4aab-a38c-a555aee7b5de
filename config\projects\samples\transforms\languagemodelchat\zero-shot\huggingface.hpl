<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>languagemodelchat-huggingface</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <pipeline_status>0</pipeline_status>
    <parameters>
      <parameter>
        <name>HF_TOKEN</name>
        <default_value>hf_abc123</default_value>
        <description/>
      </parameter>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/12/18 15:00:31.032</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/12/18 15:00:31.032</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>Llama-3.1-70B-Instruct</from>
      <to>Text output</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>messages</from>
      <to>prompt</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>prompt</from>
      <to>Llama-3.1-70B-Instruct</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Llama-3.1-70B-Instruct</name>
    <type>LanguageModelChat</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <inputField>prompt</inputField>
    <inputChatJson>true</inputChatJson>
    <outputChatJson>false</outputChatJson>
    <mock>false</mock>
    <outputFieldNamePrefix>llm_</outputFieldNamePrefix>
    <modelType>HUGGING_FACE</modelType>
    <parallelism>1</parallelism>
    <openAiUseProxy>false</openAiUseProxy>
    <openAiLogRequests>false</openAiLogRequests>
    <openAiLogResponses>false</openAiLogResponses>
    <huggingFaceAccessToken>${HF_TOKEN}</huggingFaceAccessToken>
    <huggingFaceModelId>meta-llama/Llama-3.1-70B-Instruct</huggingFaceModelId>
    <huggingFaceTemperature>0.1</huggingFaceTemperature>
    <huggingFaceMaxNewTokens>50</huggingFaceMaxNewTokens>
    <huggingFaceReturnFullText>false</huggingFaceReturnFullText>
    <huggingFaceWaitForModel>false</huggingFaceWaitForModel>
    <mistralSafePrompt>false</mistralSafePrompt>
    <mistralLogRequests>false</mistralLogRequests>
    <mistralLogResponses>false</mistralLogResponses>
    <anthropicLogRequests>false</anthropicLogRequests>
    <anthropicLogResponses>false</anthropicLogResponses>
    <attributes/>
    <GUI>
      <xloc>832</xloc>
      <yloc>288</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Text output</name>
    <type>TextFileOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <schema_definition/>
    <separator>;</separator>
    <enclosure>"</enclosure>
    <enclosure_forced>N</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>Y</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding>UTF-8</encoding>
    <endedLine/>
    <fileNameInField>N</fileNameInField>
    <fileNameField/>
    <create_parent_folder>Y</create_parent_folder>
    <file>
      <name>languagemodelchat-huggingface-output</name>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>Y</do_not_open_new_file_init>
      <extention>csv</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format/>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>N</fast_dump>
      <splitevery/>
    </file>
    <fields>
      <field>
        <name>prompt</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_identifier</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_model_type</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_model_name</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_finish_reason</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_input_token_count</name>
        <type>Integer</type>
        <format>0</format>
        <currency>£</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_output_token_count</name>
        <type>Integer</type>
        <format>0</format>
        <currency>£</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_total_token_count</name>
        <type>Integer</type>
        <format>0</format>
        <currency>£</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_inference_time</name>
        <type>Integer</type>
        <format>0</format>
        <currency>£</currency>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>llm_output</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>both</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>1024</xloc>
      <yloc>288</yloc>
    </GUI>
  </transform>
  <transform>
    <name>messages</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>system</item>
        <item>You are an AI with the emotional range of a brick wall. Your tone is always deadpan, with zero excitement or enthusiasm. No matter how bizarre, funny, or ridiculous the situation, you respond as if it’s the most mundane thing in the world. Your goal is to provide helpful answers while maintaining an unshakably flat delivery, as if nothing could possibly surprise or amuse you.</item>
      </line>
      <line>
        <item>user</item>
        <item>Using no more than 20 words, tell me the great wonders of our beautiful planet and wrap your answer in &lt;answer>...&lt;/answer> tags.</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>role</name>
        <type>String</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>content</name>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>432</xloc>
      <yloc>288</yloc>
    </GUI>
  </transform>
  <transform>
    <name>prompt</name>
    <type>EnhancedJsonOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <outputValue>prompt</outputValue>
    <jsonBloc/>
    <operation_type>outputvalue</operation_type>
    <use_arrays_with_single_instance>N</use_arrays_with_single_instance>
    <use_single_item_per_group>N</use_single_item_per_group>
    <json_prittified>Y</json_prittified>
    <encoding>UTF-8</encoding>
    <addtoresult>N</addtoresult>
    <file>
      <name/>
      <split_output_after>0</split_output_after>
      <extention>json</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <create_parent_folder>N</create_parent_folder>
      <doNotOpenNewFileInit>N</doNotOpenNewFileInit>
    </file>
    <additional_fields>
      <json_size_field/>
    </additional_fields>
    <key_fields>
    </key_fields>
    <fields>
      <field>
        <name>role</name>
        <element>role</element>
        <json_fragment>N</json_fragment>
        <is_without_enclosing>N</is_without_enclosing>
        <remove_if_blank>Y</remove_if_blank>
      </field>
      <field>
        <name>content</name>
        <element>content</element>
        <json_fragment>N</json_fragment>
        <is_without_enclosing>N</is_without_enclosing>
        <remove_if_blank>Y</remove_if_blank>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>608</xloc>
      <yloc>288</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
