<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>filter-rows-basic</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/07/14 14:14:51.304</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/07/14 14:14:51.304</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Filters an incoming data set based on a set of conditions
*) Filter Rows - True False: continue in different streams for data that meets or doesn't meet the filter condition
*) Filter Rows - Main Output: continue with data that meets the filter condition only, data that doesn't meet the filter condition is ignored. </note>
      <xloc>80</xloc>
      <yloc>32</yloc>
      <width>762</width>
      <heigth>61</heigth>
      <fontname>Noto Sans</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Test Data</from>
      <to>Filter rows - True False</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Filter rows - True False</from>
      <to>True Ouput</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Filter rows - True False</from>
      <to>False Ouput</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Test Data</from>
      <to>Filter Rows - Main Output</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Filter Rows - Main Output</from>
      <to>Main Output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>False Ouput</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>384</xloc>
      <yloc>208</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Filter rows - True False</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <send_true_to>True Ouput</send_true_to>
    <send_false_to>False Ouput</send_false_to>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>id</leftvalue>
                <function>&lt;</function>
                <rightvalue/>
                <value>
                  <name>constant</name>
                  <type>Integer</type>
                  <text>5</text>
                  <length>-1</length>
                  <precision>0</precision>
                  <isnull>N</isnull>
                  <mask>####0;-####0</mask>
                </value>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>OR</operator>
                <leftvalue>group</leftvalue>
                <function>=</function>
                <rightvalue/>
                <value>
                  <name>constant</name>
                  <type>String</type>
                  <text>GR03</text>
                  <length>-1</length>
                  <precision>-1</precision>
                  <isnull>N</isnull>
                  <mask/>
                </value>
              </condition>
            </conditions>
          </condition>
          <condition>
            <negated>Y</negated>
            <operator>AND</operator>
            <leftvalue>desc</leftvalue>
            <function>=</function>
            <rightvalue/>
            <value>
              <name>constant</name>
              <type>String</type>
              <text>d</text>
              <length>-1</length>
              <precision>-1</precision>
              <isnull>N</isnull>
              <mask/>
            </value>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <GUI>
      <xloc>208</xloc>
      <yloc>160</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Main Output</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>384</xloc>
      <yloc>336</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Filter Rows - Main Output</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <send_true_to/>
    <send_false_to/>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>id</leftvalue>
                <function>&lt;</function>
                <rightvalue/>
                <value>
                  <name>constant</name>
                  <type>Integer</type>
                  <text>5</text>
                  <length>-1</length>
                  <precision>0</precision>
                  <isnull>N</isnull>
                  <mask>####0;-####0</mask>
                </value>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>OR</operator>
                <leftvalue>group</leftvalue>
                <function>=</function>
                <rightvalue/>
                <value>
                  <name>constant</name>
                  <type>String</type>
                  <text>GR03</text>
                  <length>-1</length>
                  <precision>-1</precision>
                  <isnull>N</isnull>
                  <mask/>
                </value>
              </condition>
            </conditions>
          </condition>
          <condition>
            <negated>Y</negated>
            <operator>AND</operator>
            <leftvalue>desc</leftvalue>
            <function>=</function>
            <rightvalue/>
            <value>
              <name>constant</name>
              <type>String</type>
              <text>d</text>
              <length>-1</length>
              <precision>-1</precision>
              <isnull>N</isnull>
              <mask/>
            </value>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <GUI>
      <xloc>208</xloc>
      <yloc>336</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Test Data</name>
    <type>DataGrid</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>id</name>
        <precision>-1</precision>
        <type>Integer</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>desc</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
      <field>
        <set_empty_string>N</set_empty_string>
        <length>-1</length>
        <name>group</name>
        <precision>-1</precision>
        <type>String</type>
      </field>
    </fields>
    <data>
      <line>
        <item>1</item>
        <item>a</item>
        <item>GR01</item>
      </line>
      <line>
        <item>2</item>
        <item>b</item>
        <item>GR02</item>
      </line>
      <line>
        <item>3</item>
        <item>c</item>
        <item>GR03</item>
      </line>
      <line>
        <item>4</item>
        <item>d</item>
        <item>GR01</item>
      </line>
      <line>
        <item>5</item>
        <item>e</item>
        <item>GR02</item>
      </line>
      <line>
        <item>6</item>
        <item>f</item>
        <item>GR03</item>
      </line>
      <line>
        <item>7</item>
        <item>g</item>
        <item>GR01</item>
      </line>
      <line>
        <item>8</item>
        <item>h</item>
        <item>GR02</item>
      </line>
      <line>
        <item>9</item>
        <item>i</item>
        <item>GR03</item>
      </line>
      <line>
        <item>10</item>
        <item>j</item>
        <item>GR01</item>
      </line>
    </data>
    <attributes/>
    <GUI>
      <xloc>80</xloc>
      <yloc>208</yloc>
    </GUI>
  </transform>
  <transform>
    <name>True Ouput</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>384</xloc>
      <yloc>128</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
