<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>process-pokemon-data-child</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/07/29 10:49:11.440</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/07/29 10:49:11.440</modified_date>
    <key_for_session_key/>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>Get messages from Kafka</from>
      <to>Read JSON message</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Read JSON message</from>
      <to>Remove JSON message</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Remove JSON message</from>
      <to>OUTPUT</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Get messages from Kafka</name>
    <type>Injector</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <length/>
        <name>Key</name>
        <precision/>
        <type>String</type>
      </field>
      <field>
        <length/>
        <name>Message</name>
        <precision/>
        <type>String</type>
      </field>
      <field>
        <length/>
        <name>Topic</name>
        <precision/>
        <type>String</type>
      </field>
      <field>
        <length/>
        <name>Partition</name>
        <precision/>
        <type>Integer</type>
      </field>
      <field>
        <length/>
        <name>Offset</name>
        <precision/>
        <type>Integer</type>
      </field>
      <field>
        <length/>
        <name>Timestamp</name>
        <precision/>
        <type>Integer</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>96</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>OUTPUT</name>
    <type>Dummy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <attributes/>
    <GUI>
      <xloc>640</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Read JSON message</name>
    <type>JsonInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <include>N</include>
    <include_field/>
    <rownum>N</rownum>
    <addresultfile>N</addresultfile>
    <readurl>N</readurl>
    <removeSourceField>N</removeSourceField>
    <IsIgnoreEmptyFile>N</IsIgnoreEmptyFile>
    <doNotFailIfNoFile>Y</doNotFailIfNoFile>
    <ignoreMissingPath>Y</ignoreMissingPath>
    <defaultPathLeafToNull>Y</defaultPathLeafToNull>
    <rownum_field/>
    <file>
      <name/>
      <filemask/>
      <exclude_filemask/>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
    </file>
    <fields>
      <field>
        <name>userId</name>
        <path>data[0].userId</path>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>pokemonName</name>
        <path>data[0].pokemonName</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>pokemonLocation</name>
        <path>data[0].pokemonLocation</path>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
      <field>
        <name>detectionDate</name>
        <path>data[0].detectionDate</path>
        <type>Date</type>
        <format>yyyy/MM/dd HH:mm:ss.SSS</format>
        <currency/>
        <decimal/>
        <group/>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
    </fields>
    <limit>0</limit>
    <IsInFields>Y</IsInFields>
    <IsAFile>N</IsAFile>
    <valueField>Message</valueField>
    <shortFileFieldName/>
    <pathFieldName/>
    <hiddenFieldName/>
    <lastModificationTimeFieldName/>
    <uriNameFieldName/>
    <rootUriNameFieldName/>
    <extensionFieldName/>
    <sizeFieldName/>
    <attributes/>
    <GUI>
      <xloc>272</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Remove JSON message</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <select_unspecified>N</select_unspecified>
      <remove>
        <name>Message</name>
      </remove>
    </fields>
    <attributes/>
    <GUI>
      <xloc>464</xloc>
      <yloc>96</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
