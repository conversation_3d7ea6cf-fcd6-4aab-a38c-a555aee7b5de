<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>zip_file</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/04/27 13:14:36.144</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/04/27 13:14:36.144</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Get source filename and target file name in columns (you can use variables or get data from previous steps as well)
Pass the fields to zip file step (you can select post zip action for source file as well in step i.e. move source file or delete source file or do nothing)</note>
      <xloc>192</xloc>
      <yloc>64</yloc>
      <width>767</width>
      <heigth>42</heigth>
      <fontname>Segoe UI</fontname>
      <fontsize>9</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>Get file names</from>
      <to>define output filename</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>define output filename</from>
      <to>create path</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>create path</from>
      <to>Zip file</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>Zip file</name>
    <type>ZipFile</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <sourcefilenamefield>filename</sourcefilenamefield>
    <targetfilenamefield>zip_filename</targetfilenamefield>
    <baseFolderField/>
    <operation_type/>
    <addresultfilenames>N</addresultfilenames>
    <overwritezipentry>N</overwritezipentry>
    <createparentfolder>N</createparentfolder>
    <keepsourcefolder>N</keepsourcefolder>
    <movetofolderfield/>
    <attributes/>
    <GUI>
      <xloc>816</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Get file names</name>
    <type>GetFileNames</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <filter>
      <filterfiletype>all_files</filterfiletype>
    </filter>
    <doNotFailIfNoFile>N</doNotFailIfNoFile>
    <rownum>N</rownum>
    <isaddresult>N</isaddresult>
    <filefield>N</filefield>
    <rownum_field/>
    <filename_Field/>
    <wildcard_Field/>
    <exclude_wildcard_Field/>
    <dynamic_include_subfolders>N</dynamic_include_subfolders>
    <limit>0</limit>
    <file>
      <name>${PROJECT_HOME}/transforms/files/transactions.csv</name>
      <filemask/>
      <exclude_filemask/>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
    </file>
    <attributes/>
    <GUI>
      <xloc>336</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>define output filename</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>o_filename</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>transactions.zip</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>496</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>create path</name>
    <type>ConcatFields</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <separator>/</separator>
    <enclosure/>
    <enclosure_forced>N</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <format/>
    <encoding/>
    <fields>
      <field>
        <name>path</name>
        <type>None</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>o_filename</name>
        <type>None</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
    </fields>
    <ConcatFields>
      <targetFieldName>zip_filename</targetFieldName>
      <targetFieldLength>0</targetFieldLength>
      <removeSelectedFields>N</removeSelectedFields>
    </ConcatFields>
    <attributes/>
    <GUI>
      <xloc>624</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
