<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>read-and-inject-metadata</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2023/01/07 15:06:34.020</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/01/07 15:06:34.020</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>read customer metadata</from>
      <to>ETL metadata injection</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>static filename, format</from>
      <to>ETL metadata injection</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>rename fields</from>
      <to>ETL metadata injection</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>zips per state</from>
      <to>ETL metadata injection</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>ETL metadata injection</name>
    <type>MetaInject</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <filename>${PROJECT_HOME}/metadata-injection/read-file-template.hpl</filename>
    <run_configuration>local</run_configuration>
    <source_transform/>
    <source_output_fields>    </source_output_fields>
    <target_file></target_file>
    <create_parent_folder>Y</create_parent_folder>
    <no_execution>N</no_execution>
    <stream_source_transform/>
    <stream_target_transform/>
    <mappings>
      <mapping>
        <target_transform_name>cast dates</target_transform_name>
        <target_attribute_key>META_RENAME</target_attribute_key>
        <target_detail>Y</target_detail>
        <source_transform>rename fields</source_transform>
        <source_field>rename_to</source_field>
      </mapping>
      <mapping>
        <target_transform_name>cast dates</target_transform_name>
        <target_attribute_key>META_CONVERSION_MASK</target_attribute_key>
        <target_detail>Y</target_detail>
        <source_transform>rename fields</source_transform>
        <source_field>format</source_field>
      </mapping>
      <mapping>
        <target_transform_name>read customer data</target_transform_name>
        <target_attribute_key>FIELD_LENGTH</target_attribute_key>
        <target_detail>Y</target_detail>
        <source_transform>read customer metadata</source_transform>
        <source_field>length</source_field>
      </mapping>
      <mapping>
        <target_transform_name>read customer data</target_transform_name>
        <target_attribute_key>ENCLOSURE</target_attribute_key>
        <target_detail>N</target_detail>
        <source_transform>read customer metadata</source_transform>
        <source_field>enclosure</source_field>
      </mapping>
      <mapping>
        <target_transform_name>cast dates</target_transform_name>
        <target_attribute_key>META_NAME</target_attribute_key>
        <target_detail>Y</target_detail>
        <source_transform>rename fields</source_transform>
        <source_field>field</source_field>
      </mapping>
      <mapping>
        <target_transform_name>sort state</target_transform_name>
        <target_attribute_key>SORT_ASCENDING</target_attribute_key>
        <target_detail>Y</target_detail>
        <source_transform/>
        <source_field>Y</source_field>
      </mapping>
      <mapping>
        <target_transform_name>sort state</target_transform_name>
        <target_attribute_key>NAME</target_attribute_key>
        <target_detail>Y</target_detail>
        <source_transform>zips per state</source_transform>
        <source_field>group_field</source_field>
      </mapping>
      <mapping>
        <target_transform_name>nb zip codes per state</target_transform_name>
        <target_attribute_key>AGGREGATETYPE</target_attribute_key>
        <target_detail>Y</target_detail>
        <source_transform>zips per state</source_transform>
        <source_field>type</source_field>
      </mapping>
      <mapping>
        <target_transform_name>read customer data</target_transform_name>
        <target_attribute_key>FIELD_PRECISION</target_attribute_key>
        <target_detail>Y</target_detail>
        <source_transform>read customer metadata</source_transform>
        <source_field>precision</source_field>
      </mapping>
      <mapping>
        <target_transform_name>read customer data</target_transform_name>
        <target_attribute_key>SEPARATOR</target_attribute_key>
        <target_detail>N</target_detail>
        <source_transform>read customer metadata</source_transform>
        <source_field>delimiter</source_field>
      </mapping>
      <mapping>
        <target_transform_name>read customer data</target_transform_name>
        <target_attribute_key>FILE_FORMAT</target_attribute_key>
        <target_detail>N</target_detail>
        <source_transform>static filename, format</source_transform>
        <source_field>format</source_field>
      </mapping>
      <mapping>
        <target_transform_name>read customer data</target_transform_name>
        <target_attribute_key>FIELD_FORMAT</target_attribute_key>
        <target_detail>Y</target_detail>
        <source_transform>read customer metadata</source_transform>
        <source_field>mask</source_field>
      </mapping>
      <mapping>
        <target_transform_name>nb zip codes per state</target_transform_name>
        <target_attribute_key>AGGREGATEFIELD</target_attribute_key>
        <target_detail>Y</target_detail>
        <source_transform>zips per state</source_transform>
        <source_field>name</source_field>
      </mapping>
      <mapping>
        <target_transform_name>nb zip codes per state</target_transform_name>
        <target_attribute_key>GROUPFIELD</target_attribute_key>
        <target_detail>Y</target_detail>
        <source_transform>zips per state</source_transform>
        <source_field>group_field</source_field>
      </mapping>
      <mapping>
        <target_transform_name>read customer data</target_transform_name>
        <target_attribute_key>FIELD_NAME</target_attribute_key>
        <target_detail>Y</target_detail>
        <source_transform>read customer metadata</source_transform>
        <source_field>name</source_field>
      </mapping>
      <mapping>
        <target_transform_name>sort state</target_transform_name>
        <target_attribute_key>ONLY_PASS_UNIQUE_ROWS</target_attribute_key>
        <target_detail>N</target_detail>
        <source_transform/>
        <source_field>Y</source_field>
      </mapping>
      <mapping>
        <target_transform_name>read customer data</target_transform_name>
        <target_attribute_key>FILENAME</target_attribute_key>
        <target_detail>Y</target_detail>
        <source_transform>static filename, format</source_transform>
        <source_field>filename</source_field>
      </mapping>
      <mapping>
        <target_transform_name>cast dates</target_transform_name>
        <target_attribute_key>META_TYPE</target_attribute_key>
        <target_detail>Y</target_detail>
        <source_transform>rename fields</source_transform>
        <source_field>type</source_field>
      </mapping>
      <mapping>
        <target_transform_name>read customer data</target_transform_name>
        <target_attribute_key>FIELD_TYPE</target_attribute_key>
        <target_detail>Y</target_detail>
        <source_transform>read customer metadata</source_transform>
        <source_field>type</source_field>
      </mapping>
      <mapping>
        <target_transform_name>nb zip codes per state</target_transform_name>
        <target_attribute_key>SUBJECTFIELD</target_attribute_key>
        <target_detail>Y</target_detail>
        <source_transform>zips per state</source_transform>
        <source_field>subject</source_field>
      </mapping>
    </mappings>
    <attributes/>
    <GUI>
      <xloc>496</xloc>
      <yloc>160</yloc>
    </GUI>
  </transform>
  <transform>
    <name>read customer metadata</name>
    <type>FileMetadataPlugin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <defaultCharset>ISO-8859-1</defaultCharset>
    <delimiterCandidate>
      <candidate>	</candidate>
    </delimiterCandidate>
    <delimiterCandidate>
      <candidate>;</candidate>
    </delimiterCandidate>
    <delimiterCandidate>
      <candidate>,</candidate>
    </delimiterCandidate>
    <enclosureCandidate>
      <candidate>"</candidate>
    </enclosureCandidate>
    <enclosureCandidate>
      <candidate>'</candidate>
    </enclosureCandidate>
    <fileName>${PROJECT_HOME}/files/customers-100.txt</fileName>
    <limitRows>10000</limitRows>
    <attributes/>
    <GUI>
      <xloc>128</xloc>
      <yloc>160</yloc>
    </GUI>
  </transform>
  <transform>
    <name>rename fields</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>birthdate</item>
        <item/>
        <item>String</item>
        <item>yyyy-MM-dd</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>field</name>
        <type>String</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>rename_to</name>
        <type>String</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>type</name>
        <type>String</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>format</name>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>128</xloc>
      <yloc>384</yloc>
    </GUI>
  </transform>
  <transform>
    <name>static filename, format</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>${PROJECT_HOME}/files/customers-100.txt</item>
        <item>mixed</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>filename</name>
        <type>String</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>format</name>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>128</xloc>
      <yloc>288</yloc>
    </GUI>
  </transform>
  <transform>
    <name>zips per state</name>
    <type>DataGrid</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <data>
      <line>
        <item>stateCode</item>
        <item>Number of Distinct Values (N)</item>
        <item>zip</item>
        <item>nb_zip_codes</item>
      </line>
    </data>
    <fields>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>group_field</name>
        <type>String</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>type</name>
        <type>String</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>subject</name>
        <type>String</type>
      </field>
      <field>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <name>name</name>
        <type>String</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>128</xloc>
      <yloc>480</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
