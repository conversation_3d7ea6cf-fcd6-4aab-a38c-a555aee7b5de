<?xml version="1.0" encoding="UTF-8"?>
<!--

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->
<pipeline>
  <info>
    <name>Clone row</name>
    <name_sync_with_filename>Y</name_sync_with_filename>
    <description/>
    <extended_description/>
    <pipeline_version/>
    <pipeline_type>Normal</pipeline_type>
    <pipeline_status>0</pipeline_status>
    <parameters>
    </parameters>
    <capture_transform_performance>N</capture_transform_performance>
    <transform_performance_capturing_delay>1000</transform_performance_capturing_delay>
    <transform_performance_capturing_size_limit>100</transform_performance_capturing_size_limit>
    <created_user>-</created_user>
    <created_date>2021/04/29 12:59:13.792</created_date>
    <modified_user>-</modified_user>
    <modified_date>2021/04/29 12:59:13.792</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>Duplicate input rows with clone rows.

The 'Flagfield' output field contains:
>> N  : it’s the original row
>> Y  : cloned row, a copy of the original row</note>
      <xloc>80</xloc>
      <yloc>16</yloc>
      <width>231</width>
      <heigth>89</heigth>
      <fontname>Segoe UI</fontname>
      <fontsize>11</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>14</fontcolorred>
      <fontcolorgreen>58</fontcolorgreen>
      <fontcolorblue>90</fontcolorblue>
      <backgroundcolorred>201</backgroundcolorred>
      <backgroundcolorgreen>232</backgroundcolorgreen>
      <backgroundcolorblue>251</backgroundcolorblue>
      <bordercolorred>14</bordercolorred>
      <bordercolorgreen>58</bordercolorgreen>
      <bordercolorblue>90</bordercolorblue>
    </notepad>
  </notepads>
  <order>
    <hop>
      <from>add unique id</from>
      <to>duplicate rows</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>5 static rows</from>
      <to>Wait 1s</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Wait 1s</from>
      <to>add system date</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>add system date</from>
      <to>add unique id</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <transform>
    <name>add unique id</name>
    <type>Sequence</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <counter_name/>
    <use_counter>Y</use_counter>
    <use_database>N</use_database>
    <increment_by>1</increment_by>
    <max_value>999999999</max_value>
    <schema/>
    <seqname>SEQ_</seqname>
    <start_at>1</start_at>
    <valuename>id</valuename>
    <attributes/>
    <GUI>
      <xloc>480</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>duplicate rows</name>
    <type>CloneRow</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <nrclones>1</nrclones>
    <addcloneflag>Y</addcloneflag>
    <cloneflagfield>Flagfield</cloneflagfield>
    <nrcloneinfield>N</nrcloneinfield>
    <nrclonefield/>
    <addclonenum>Y</addclonenum>
    <clonenumfield>CopyNumber</clonenumfield>
    <attributes/>
    <GUI>
      <xloc>624</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>5 static rows</name>
    <type>RowGenerator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <currency/>
        <decimal/>
        <format/>
        <group/>
        <length>-1</length>
        <name>quote</name>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
        <type>String</type>
        <nullif>Welcome to Apache Hop!</nullif>
      </field>
    </fields>
    <interval_in_ms>5000</interval_in_ms>
    <last_time_field>FiveSecondsAgo</last_time_field>
    <never_ending>N</never_ending>
    <limit>5</limit>
    <row_time_field>now</row_time_field>
    <attributes/>
    <GUI>
      <xloc>96</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>Wait 1s</name>
    <type>Delay</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <timeout>1</timeout>
    <scaletime>seconds</scaletime>
    <attributes/>
    <GUI>
      <xloc>208</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform>
    <name>add system date</name>
    <type>SystemInfo</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>SystemDate</name>
        <type>system date (variable)</type>
      </field>
    </fields>
    <attributes/>
    <GUI>
      <xloc>336</xloc>
      <yloc>144</yloc>
    </GUI>
  </transform>
  <transform_error_handling>
  </transform_error_handling>
  <attributes/>
</pipeline>
