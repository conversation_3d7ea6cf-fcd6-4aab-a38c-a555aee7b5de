{"server": "${NEO4J_HOSTNAME}", "trustAllCertificatesVariable": "", "browserPort": "7474", "databaseName": "neo4j", "maxConnectionLifetime": "", "routingVariable": "", "manualUrls": [], "routingPolicy": "", "boltPort": "${NEO4J_PORT}", "trustAllCertificates": false, "usingEncryption": false, "routing": false, "version4": true, "password": "${NEO4J_PASSWORD}", "usingEncryptionVariable": "", "maxTransactionRetryTime": "0", "maxConnectionPoolSize": "", "name": "demo", "connectionLivenessCheckTimeout": "", "connectionTimeout": "", "username": "${NEO4J_USERNAME}", "version4Variable": "", "connectionAcquisitionTimeout": ""}